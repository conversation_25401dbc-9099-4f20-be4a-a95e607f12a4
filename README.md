# PDF Text Extraction Application

A full-stack application that automatically extracts text and bounding box information from uploaded PDF files using pattern matching.

## Features

- **PDF Upload**: Drag and drop or click to upload PDF files
- **Real-time Processing**: Automatic text extraction upon upload
- **Pattern Matching**: Finds text elements matching the pattern `XX.Y.XX` (where X is a digit and Y is a capital letter)
- **Bounding Box Extraction**: Captures precise coordinates for each matched element
- **Interactive Results**: View, copy, and download extraction results
- **PDF Viewer**: Built-in PDF viewer using Nutrient SDK

## Architecture

- **Frontend**: React + Vite + TailwindCSS
- **Backend**: FastAPI + Python
- **PDF Processing**: pdfminer.six library
- **Communication**: REST API with JSON responses

## Quick Start

### Option 1: Use the Startup Script (Recommended)

```bash
./start.sh
```

This will automatically:
- Install all dependencies
- Start the backend server on port 8000
- Start the frontend development server on port 5173

### Option 2: Manual Setup

#### Backend Setup

```bash
cd python_backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

#### Frontend Setup

```bash
# In a new terminal, from the project root
npm install
npm run dev
```

## Usage

1. **Access the Application**: Open http://localhost:5173 in your browser
2. **Upload PDF**: Click the "Upload PDF" button and select a PDF file
3. **View Results**: The application will automatically process the PDF and show:
   - Number of matches found
   - Detailed extraction results with labels, codes, and bounding boxes
   - Options to copy or download the results as JSON

## Pattern Matching

The application looks for text elements with exactly 2 lines where:
- **First line**: Any text (becomes the label)
- **Second line**: Matches pattern `XX.Y.XX` where:
  - X = digit (0-9)
  - Y = capital letter (A-Z)
  - Example: `01.A.01`, `25.B.99`

## API Endpoints

- `GET /` - Health check
- `POST /upload-pdf` - Upload and process PDF file

### Example API Response

```json
{
  "success": true,
  "filename": "example.pdf",
  "total_matches": 3,
  "filtered_pairs": [
    {
      "label": "Sample Label",
      "code": "01.A.01",
      "bbox": {
        "x0": 100.0,
        "y0": 200.0,
        "x1": 300.0,
        "y1": 220.0
      }
    }
  ]
}
```

## Development

### Backend Development

```bash
cd python_backend
source venv/bin/activate
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development

```bash
npm run dev
```

### API Documentation

Visit http://localhost:8000/docs for interactive API documentation.

## Dependencies

### Backend
- FastAPI
- uvicorn
- pdfminer.six
- python-multipart

### Frontend
- React 19
- Vite
- TailwindCSS
- @nutrient-sdk/viewer

## File Structure

```
├── src/
│   ├── components/
│   │   ├── TestViewer.jsx          # PDF viewer component
│   │   └── ExtractionResults.jsx   # Results display component
│   ├── App.jsx                     # Main application component
│   └── main.jsx                    # React entry point
├── python_backend/
│   ├── main.py                     # FastAPI server
│   ├── requirements.txt            # Python dependencies
│   └── README.md                   # Backend documentation
├── public/
│   └── FP1.pdf                     # Sample PDF file
├── start.sh                        # Startup script
└── README.md                       # This file
```

## Troubleshooting

### Backend Issues
- Ensure Python 3.7+ is installed
- Check that all dependencies are installed: `pip install -r python_backend/requirements.txt`
- Verify the backend is running on port 8000

### Frontend Issues
- Ensure Node.js 16+ is installed
- Check that dependencies are installed: `npm install`
- Verify the frontend is running on port 5173

### CORS Issues
- The backend is configured to accept requests from localhost:5173
- If using a different port, update the CORS settings in `python_backend/main.py`

## License

This project is open source and available under the MIT License.
