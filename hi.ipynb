from pdfminer.high_level import extract_pages
from pdfminer.layout import <PERSON><PERSON><PERSON>, LTRect
from shapely.geometry import Polygon, LineString, Point
import matplotlib.pyplot as plt

# --- Step 1: Extract lines and rectangles from PDF ---
line_elements = []
rect_elements = []

for page_layout in extract_pages("FP1.pdf"):
    for element in page_layout:
        if isinstance(element, LTLine):
            line_elements.append(element)
        elif isinstance(element, LTRect):
            rect_elements.append(element)

print(f"Total lines: {len(line_elements)}")
print(f"Total rects: {len(rect_elements)}")

# --- Step 2: Cluster connected lines (very basic) ---
def are_lines_connected(line1, line2, tolerance=3):
    """Check if any endpoint of line1 is close to any endpoint of line2."""
    pts1 = [(line1.x0, line1.y0), (line1.x1, line1.y1)]
    pts2 = [(line2.x0, line2.y0), (line2.x1, line2.y1)]
    for x1, y1 in pts1:
        for x2, y2 in pts2:
            if abs(x1 - x2) < tolerance and abs(y1 - y2) < tolerance:
                return True
    return False

clusters = []
for line in line_elements:
    added = False
    for cluster in clusters:
        if any(are_lines_connected(line, other) for other in cluster):
            cluster.append(line)
            added = True
            break
    if not added:
        clusters.append([line])

print(f"Found {len(clusters)} line clusters.")

# --- Step 3: Convert clusters to polygons (naive) ---
polygons = []
for cluster in clusters:
    if len(cluster) >= 4:
        # Attempt to form a polygon using all start points
        points = []
        for l in cluster:
            points.append((l.x0, l.y0))
        points.append((cluster[-1].x1, cluster[-1].y1))  # Close path
        try:
            poly = Polygon(points)
            if poly.is_valid and poly.area > 500:  # Ignore tiny noise
                polygons.append(poly)
        except:
            continue

print(f"Extracted {len(polygons)} valid polygonal areas.")

# --- Step 4: Plotting the extracted shapes ---
plt.figure(figsize=(12, 12))
for poly in polygons:
    x, y = poly.exterior.xy
    plt.plot(x, y, color="blue")

for rect in rect_elements:
    x0, y0, x1, y1 = rect.bbox
    rect_poly = Polygon([(x0, y0), (x0, y1), (x1, y1), (x1, y0)])
    x, y = rect_poly.exterior.xy
    plt.plot(x, y, color="green", linestyle='--')

plt.gca().invert_yaxis()
plt.axis("equal")
plt.title("Vector Extraction from PDF Floor Plan")
plt.show()


# EXTRACTION OF TEXT AND BBOX AND CODE

from pdfminer.high_level import extract_pages
from pdfminer.layout import LTTextBox, LTTextLine, LTChar
import re

code_pattern = re.compile(r'^\d{2}\.[A-Z]\.\d{2}$')
filtered_pairs = []
for page_layout in extract_pages("FP1.pdf"):
    for element in page_layout:
        if isinstance(element, LTTextBox) or isinstance(element, LTTextLine):
            text = element.get_text().strip()
            x0, y0, x1, y1 = element.bbox
            # print(f"Text: '{text}' at ({x0}, {y0}, {x1}, {y1})")
            lines = text.strip().splitlines()
            if len(lines) == 2 and code_pattern.match(lines[1].strip()):
                print(f"  - Label: '{lines[0]}' - Code: '{lines[1]}' - BBox: ({x0}, {y0}, {x1}, {y1})")
                filtered_pairs.append({
                    "label": lines[0].strip(),
                    "code": lines[1].strip(),
                    "bbox": ({x0}, {y0}, {x1}, {y1})
                })

# MARKING EXTRACTED TEXT ROOMS AND CODE

from pdfminer.high_level import extract_pages
from pdfminer.layout import LTTextBox, LTTextLine
import re
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from PyPDF2 import PdfReader, PdfWriter

# Step 1: Extract filtered markers
code_pattern1 = re.compile(r'^\d{2}\.[A-Z]\.\d{2}$')
code_pattern2 = re.compile(r'^\d{2}\.[A-Z]\.\d{2}.\d{1}$')
filtered_pairs = []

pdf_path = "FP1.pdf"

for page_layout in extract_pages(pdf_path):
    for element in page_layout:
        if isinstance(element, (LTTextBox, LTTextLine)):
            text = element.get_text().strip()
            lines = text.splitlines()
            if len(lines) == 2 and (code_pattern1.match(lines[1].strip()) or code_pattern2.match(lines[1].strip())):
                x0, y0, x1, y1 = element.bbox
                filtered_pairs.append({
                    "label": lines[0].strip(),
                    "code": lines[1].strip(),
                    "x": x0,
                    "y": y0
                })

# Step 2: Create overlay with red markers
reader = PdfReader(pdf_path)
page = reader.pages[0]
page_width = float(page.mediabox.width)
page_height = float(page.mediabox.height)

overlay_path = "overlay.pdf"
c = canvas.Canvas(overlay_path, pagesize=(page_width, page_height))

for item in filtered_pairs:
    x, y = item["x"], item["y"]
    c.setFillColorRGB(1, 0, 0)  # red
    c.circle(x, y, 6, fill=1)   # red circle marker
    c.setFont("Helvetica", 6)
    c.drawString(x + 8, y + 2, item["code"])  # show the code next to the marker

c.save()

# Step 3: Merge overlay with original PDF
overlay_reader = PdfReader(overlay_path)
writer = PdfWriter()

for i, page in enumerate(reader.pages):
    overlay_page = overlay_reader.pages[0]  # same overlay for all pages
    page.merge_page(overlay_page)
    writer.add_page(page)

output_path = "FP1_marked.pdf"
with open(output_path, "wb") as f:
    writer.write(f)

print(f"✅ Marked PDF saved as: {output_path}")


# REMOVING ANY OTHER TEXT

import fitz  # PyMuPDF
import re

pdf_path = "FP1.pdf"
output_path = "FP1_cleaned.pdf"

doc = fitz.open(pdf_path)

# Define code pattern
code_pattern1 = re.compile(r'^\d{2}\.[A-Z]\.\d{2}$')
code_pattern2 = re.compile(r'^\d{2}\.[A-Z]\.\d{2}\.\d{1}$')

for page in doc:
    blocks = page.get_text("dict")["blocks"]

    for block in blocks:
        if "lines" not in block:
            continue

        text_lines = block["lines"]
        block_text = "\n".join(["".join([span["text"] for span in line["spans"]]) for line in text_lines])
        lines = block_text.strip().splitlines()

        # Check if it matches the 2-line pattern
        if not (len(lines) == 2 and (code_pattern1.match(lines[1]) or code_pattern2.match(lines[1]))):
            # Redact the whole block (remove it)
            rect = fitz.Rect(block["bbox"])
            page.add_redact_annot(rect, fill=(1, 1, 1))  # White-out
            page.apply_redactions()

# Save cleaned PDF
doc.save(output_path)
print(f"✅ Cleaned PDF saved as: {output_path}")


from pdfminer.high_level import extract_pages
from pdfminer.layout import LTRect

# --- Step 1: Extract lines and rectangles from PDF ---
rect_elements = []
first_page = None # Variable to store the first page layout

for page_layout in extract_pages("FP1.pdf"):
    first_page = page_layout # Store the page layout
    for element in page_layout:
        if isinstance(element, LTRect):
            rect_elements.append(element)

print(f"Total rects on first page: {len(rect_elements)}")




import json
from matplotlib.path import Path

# Polygon from the image
polygon_points = [
    (1705.3562083794848, 805.6705155733945),
    (1705.3562083794848, 902.1197427020825),
    (1820.973578439111,  903.3367676560786),
    (1820.973578439111,  806.2790280473926)
]

# Load your JSON (replace with actual file read)
with open("FP1.pdf_extraction.json", "r") as f:
    data = json.load(f)

# Create a Path object from polygon
polygon_path = Path(polygon_points)


# Check which bounding boxes intersect the polygon
matches_inside_polygon = []

for pair in data["filtered_pairs"]:
    bbox = pair["bbox"]
    # Get all 4 corners of the bounding box
    bbox_points = [
        (bbox["x0"], bbox["y0"]),
        (bbox["x0"], bbox["y1"]),
        (bbox["x1"], bbox["y0"]),
        (bbox["x1"], bbox["y1"])
    ]
    
    # Check if any corner lies within the polygon
    if any(polygon_path.contains_point(point) for point in bbox_points):
        matches_inside_polygon.append({
            "label": pair["label"],
            "code": pair["code"]
        })

# Output matches found inside the polygon
print("Labels inside polygon:")
for match in matches_inside_polygon:
    print(f"{match['label']} ({match['code']})")


