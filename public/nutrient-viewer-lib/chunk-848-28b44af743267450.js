/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
"use strict";(globalThis.webpackChunkNutrientViewer=globalThis.webpackChunkNutrientViewer||[]).push([[848],{17633:(e,t,n)=>{var r=n(96540);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=Object.prototype.hasOwnProperty,l=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,u={},i={};function s(e){return!!a.call(i,e)||!a.call(u,e)&&(l.test(e)?i[e]=!0:(u[e]=!0,!1))}function c(e,t,n,r,o,a,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=l}var d={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){d[e]=new c(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];d[t]=new c(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){d[e]=new c(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){d[e]=new c(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){d[e]=new c(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){d[e]=new c(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){d[e]=new c(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){d[e]=new c(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){d[e]=new c(e,5,!1,e.toLowerCase(),null,!1,!1)}));var f=/[\-:]([a-z])/g;function p(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(f,p);d[t]=new c(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(f,p);d[t]=new c(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(f,p);d[t]=new c(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){d[e]=new c(e,1,!1,e.toLowerCase(),null,!1,!1)})),d.xlinkHref=new c("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){d[e]=new c(e,1,!1,e.toLowerCase(),null,!0,!0)}));var h={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},g=["Webkit","ms","Moz","O"];Object.keys(h).forEach((function(e){g.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),h[t]=h[e]}))}));var m=/["'&<>]/;function b(e){if("boolean"==typeof e||"number"==typeof e)return""+e;e=""+e;var t=m.exec(e);if(t){var n,r="",o=0;for(n=t.index;n<e.length;n++){switch(e.charCodeAt(n)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#x27;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}o!==n&&(r+=e.substring(o,n)),o=n+1,r+=t}e=o!==n?r+e.substring(o,n):r}return e}var y=/([A-Z])/g,v=/^ms-/,S=Array.isArray;function x(e,t){return{insertionMode:e,selectedValue:t}}var k=new Map;function w(e,t,n){if("object"!=typeof n)throw Error(o(62));for(var r in t=!0,n)if(a.call(n,r)){var l=n[r];if(null!=l&&"boolean"!=typeof l&&""!==l){if(0===r.indexOf("--")){var u=b(r);l=b((""+l).trim())}else{u=r;var i=k.get(u);void 0!==i||(i=b(u.replace(y,"-$1").toLowerCase().replace(v,"-ms-")),k.set(u,i)),u=i,l="number"==typeof l?0===l||a.call(h,r)?""+l:l+"px":b((""+l).trim())}t?(t=!1,e.push(' style="',u,":",l)):e.push(";",u,":",l)}}t||e.push('"')}function C(e,t,n,r){switch(n){case"style":return void w(e,t,r);case"defaultValue":case"defaultChecked":case"innerHTML":case"suppressContentEditableWarning":case"suppressHydrationWarning":return}if(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])if(null!==(t=d.hasOwnProperty(n)?d[n]:null)){switch(typeof r){case"function":case"symbol":return;case"boolean":if(!t.acceptsBooleans)return}switch(n=t.attributeName,t.type){case 3:r&&e.push(" ",n,'=""');break;case 4:!0===r?e.push(" ",n,'=""'):!1!==r&&e.push(" ",n,'="',b(r),'"');break;case 5:isNaN(r)||e.push(" ",n,'="',b(r),'"');break;case 6:!isNaN(r)&&1<=r&&e.push(" ",n,'="',b(r),'"');break;default:t.sanitizeURL&&(r=""+r),e.push(" ",n,'="',b(r),'"')}}else if(s(n)){switch(typeof r){case"function":case"symbol":return;case"boolean":if("data-"!==(t=n.toLowerCase().slice(0,5))&&"aria-"!==t)return}e.push(" ",n,'="',b(r),'"')}}function E(e,t,n){if(null!=t){if(null!=n)throw Error(o(60));if("object"!=typeof t||!("__html"in t))throw Error(o(61));null!=(t=t.__html)&&e.push(""+t)}}function F(e,t,n,r){e.push(_(n));var o,l=n=null;for(o in t)if(a.call(t,o)){var u=t[o];if(null!=u)switch(o){case"children":n=u;break;case"dangerouslySetInnerHTML":l=u;break;default:C(e,r,o,u)}}return e.push(">"),E(e,l,n),"string"==typeof n?(e.push(b(n)),null):n}var T=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,R=new Map;function _(e){var t=R.get(e);if(void 0===t){if(!T.test(e))throw Error(o(65,e));t="<"+e,R.set(e,t)}return t}function P(e,t,n,l,u){switch(t){case"select":e.push(_("select"));var i=null,c=null;for(h in n)if(a.call(n,h)){var d=n[h];if(null!=d)switch(h){case"children":i=d;break;case"dangerouslySetInnerHTML":c=d;break;case"defaultValue":case"value":break;default:C(e,l,h,d)}}return e.push(">"),E(e,c,i),i;case"option":c=u.selectedValue,e.push(_("option"));var f=d=null,p=null,h=null;for(i in n)if(a.call(n,i)){var g=n[i];if(null!=g)switch(i){case"children":d=g;break;case"selected":p=g;break;case"dangerouslySetInnerHTML":h=g;break;case"value":f=g;default:C(e,l,i,g)}}if(null!=c)if(n=null!==f?""+f:function(e){var t="";return r.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(d),S(c)){for(l=0;l<c.length;l++)if(""+c[l]===n){e.push(' selected=""');break}}else""+c===n&&e.push(' selected=""');else p&&e.push(' selected=""');return e.push(">"),E(e,h,d),d;case"textarea":for(d in e.push(_("textarea")),h=c=i=null,n)if(a.call(n,d)&&null!=(f=n[d]))switch(d){case"children":h=f;break;case"value":i=f;break;case"defaultValue":c=f;break;case"dangerouslySetInnerHTML":throw Error(o(91));default:C(e,l,d,f)}if(null===i&&null!==c&&(i=c),e.push(">"),null!=h){if(null!=i)throw Error(o(92));if(S(h)&&1<h.length)throw Error(o(93));i=""+h}return"string"==typeof i&&"\n"===i[0]&&e.push("\n"),null!==i&&e.push(b(""+i)),null;case"input":for(c in e.push(_("input")),f=h=d=i=null,n)if(a.call(n,c)&&null!=(p=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(o(399,"input"));case"defaultChecked":f=p;break;case"defaultValue":d=p;break;case"checked":h=p;break;case"value":i=p;break;default:C(e,l,c,p)}return null!==h?C(e,l,"checked",h):null!==f&&C(e,l,"checked",f),null!==i?C(e,l,"value",i):null!==d&&C(e,l,"value",d),e.push("/>"),null;case"menuitem":for(var m in e.push(_("menuitem")),n)if(a.call(n,m)&&null!=(i=n[m]))switch(m){case"children":case"dangerouslySetInnerHTML":throw Error(o(400));default:C(e,l,m,i)}return e.push(">"),null;case"title":for(g in e.push(_("title")),i=null,n)if(a.call(n,g)&&null!=(c=n[g]))switch(g){case"children":i=c;break;case"dangerouslySetInnerHTML":throw Error(o(434));default:C(e,l,g,c)}return e.push(">"),i;case"listing":case"pre":for(f in e.push(_(t)),c=i=null,n)if(a.call(n,f)&&null!=(d=n[f]))switch(f){case"children":i=d;break;case"dangerouslySetInnerHTML":c=d;break;default:C(e,l,f,d)}if(e.push(">"),null!=c){if(null!=i)throw Error(o(60));if("object"!=typeof c||!("__html"in c))throw Error(o(61));null!=(n=c.__html)&&("string"==typeof n&&0<n.length&&"\n"===n[0]?e.push("\n",n):e.push(""+n))}return"string"==typeof i&&"\n"===i[0]&&e.push("\n"),i;case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":for(var y in e.push(_(t)),n)if(a.call(n,y)&&null!=(i=n[y]))switch(y){case"children":case"dangerouslySetInnerHTML":throw Error(o(399,t));default:C(e,l,y,i)}return e.push("/>"),null;case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return F(e,n,t,l);case"html":return 0===u.insertionMode&&e.push("<!DOCTYPE html>"),F(e,n,t,l);default:if(-1===t.indexOf("-")&&"string"!=typeof n.is)return F(e,n,t,l);for(p in e.push(_(t)),c=i=null,n)if(a.call(n,p)&&null!=(d=n[p]))switch(p){case"children":i=d;break;case"dangerouslySetInnerHTML":c=d;break;case"style":w(e,l,d);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":break;default:s(p)&&"function"!=typeof d&&"symbol"!=typeof d&&e.push(" ",p,'="',b(d),'"')}return e.push(">"),E(e,c,i),i}}function I(e,t,n){if(e.push('\x3c!--$?--\x3e<template id="'),null===n)throw Error(o(395));return e.push(n),e.push('"></template>')}var M=/[<\u2028\u2029]/g;function B(e){return JSON.stringify(e).replace(M,(function(e){switch(e){case"<":return"\\u003c";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React")}}))}function D(e,t,n,r){return n.generateStaticMarkup?(e.push(b(t)),!1):(""===t?e=r:(r&&e.push("\x3c!-- --\x3e"),e.push(b(t)),e=!0),e)}var N=Object.assign,z=Symbol.for("react.element"),V=Symbol.for("react.portal"),$=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),O=Symbol.for("react.profiler"),A=Symbol.for("react.provider"),j=Symbol.for("react.context"),q=Symbol.for("react.forward_ref"),H=Symbol.for("react.suspense"),U=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),X=Symbol.for("react.scope"),G=Symbol.for("react.debug_trace_mode"),J=Symbol.for("react.legacy_hidden"),Y=Symbol.for("react.default_value"),K=Symbol.iterator;function Q(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case $:return"Fragment";case V:return"Portal";case O:return"Profiler";case L:return"StrictMode";case H:return"Suspense";case U:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case j:return(e.displayName||"Context")+".Consumer";case A:return(e._context.displayName||"Context")+".Provider";case q:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case W:return null!==(t=e.displayName||null)?t:Q(e.type)||"Memo";case Z:t=e._payload,e=e._init;try{return Q(e(t))}catch(e){}}return null}var ee={};function te(e,t){if(!(e=e.contextTypes))return ee;var n,r={};for(n in e)r[n]=t[n];return r}var ne=null;function re(e,t){if(e!==t){e.context._currentValue2=e.parentValue,e=e.parent;var n=t.parent;if(null===e){if(null!==n)throw Error(o(401))}else{if(null===n)throw Error(o(401));re(e,n)}t.context._currentValue2=t.value}}function oe(e){e.context._currentValue2=e.parentValue,null!==(e=e.parent)&&oe(e)}function ae(e){var t=e.parent;null!==t&&ae(t),e.context._currentValue2=e.value}function le(e,t){if(e.context._currentValue2=e.parentValue,null===(e=e.parent))throw Error(o(402));e.depth===t.depth?re(e,t):le(e,t)}function ue(e,t){var n=t.parent;if(null===n)throw Error(o(402));e.depth===n.depth?re(e,n):ue(e,n),t.context._currentValue2=t.value}function ie(e){var t=ne;t!==e&&(null===t?ae(e):null===e?oe(t):t.depth===e.depth?re(t,e):t.depth>e.depth?le(t,e):ue(t,e),ne=e)}var se={isMounted:function(){return!1},enqueueSetState:function(e,t){null!==(e=e._reactInternals).queue&&e.queue.push(t)},enqueueReplaceState:function(e,t){(e=e._reactInternals).replace=!0,e.queue=[t]},enqueueForceUpdate:function(){}};function ce(e,t,n,r){var o=void 0!==e.state?e.state:null;e.updater=se,e.props=n,e.state=o;var a={queue:[],replace:!1};e._reactInternals=a;var l=t.contextType;if(e.context="object"==typeof l&&null!==l?l._currentValue2:r,"function"==typeof(l=t.getDerivedStateFromProps)&&(o=null==(l=l(n,o))?o:N({},o,l),e.state=o),"function"!=typeof t.getDerivedStateFromProps&&"function"!=typeof e.getSnapshotBeforeUpdate&&("function"==typeof e.UNSAFE_componentWillMount||"function"==typeof e.componentWillMount))if(t=e.state,"function"==typeof e.componentWillMount&&e.componentWillMount(),"function"==typeof e.UNSAFE_componentWillMount&&e.UNSAFE_componentWillMount(),t!==e.state&&se.enqueueReplaceState(e,e.state,null),null!==a.queue&&0<a.queue.length)if(t=a.queue,l=a.replace,a.queue=null,a.replace=!1,l&&1===t.length)e.state=t[0];else{for(a=l?t[0]:e.state,o=!0,l=l?1:0;l<t.length;l++){var u=t[l];null!=(u="function"==typeof u?u.call(e,a,n,r):u)&&(o?(o=!1,a=N({},a,u)):N(a,u))}e.state=a}else a.queue=null}var de={id:1,overflow:""};function fe(e,t,n){var r=e.id;e=e.overflow;var o=32-pe(r)-1;r&=~(1<<o),n+=1;var a=32-pe(t)+o;if(30<a){var l=o-o%5;return a=(r&(1<<l)-1).toString(32),r>>=l,o-=l,{id:1<<32-pe(t)+o|n<<o|r,overflow:a+e}}return{id:1<<a|n<<o|r,overflow:e}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(he(e)/ge|0)|0},he=Math.log,ge=Math.LN2;var me="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},be=null,ye=null,ve=null,Se=null,xe=!1,ke=!1,we=0,Ce=null,Ee=0;function Fe(){if(null===be)throw Error(o(321));return be}function Te(){if(0<Ee)throw Error(o(312));return{memoizedState:null,queue:null,next:null}}function Re(){return null===Se?null===ve?(xe=!1,ve=Se=Te()):(xe=!0,Se=ve):null===Se.next?(xe=!1,Se=Se.next=Te()):(xe=!0,Se=Se.next),Se}function _e(){ye=be=null,ke=!1,ve=null,Ee=0,Se=Ce=null}function Pe(e,t){return"function"==typeof t?t(e):t}function Ie(e,t,n){if(be=Fe(),Se=Re(),xe){var r=Se.queue;if(t=r.dispatch,null!==Ce&&void 0!==(n=Ce.get(r))){Ce.delete(r),r=Se.memoizedState;do{r=e(r,n.action),n=n.next}while(null!==n);return Se.memoizedState=r,[r,t]}return[Se.memoizedState,t]}return e=e===Pe?"function"==typeof t?t():t:void 0!==n?n(t):t,Se.memoizedState=e,e=(e=Se.queue={last:null,dispatch:null}).dispatch=Be.bind(null,be,e),[Se.memoizedState,e]}function Me(e,t){if(be=Fe(),t=void 0===t?null:t,null!==(Se=Re())){var n=Se.memoizedState;if(null!==n&&null!==t){var r=n[1];e:if(null===r)r=!1;else{for(var o=0;o<r.length&&o<t.length;o++)if(!me(t[o],r[o])){r=!1;break e}r=!0}if(r)return n[0]}}return e=e(),Se.memoizedState=[e,t],e}function Be(e,t,n){if(25<=Ee)throw Error(o(301));if(e===be)if(ke=!0,e={action:n,next:null},null===Ce&&(Ce=new Map),void 0===(n=Ce.get(t)))Ce.set(t,e);else{for(t=n;null!==t.next;)t=t.next;t.next=e}}function De(){throw Error(o(394))}function Ne(){}var ze={readContext:function(e){return e._currentValue2},useContext:function(e){return Fe(),e._currentValue2},useMemo:Me,useReducer:Ie,useRef:function(e){be=Fe();var t=(Se=Re()).memoizedState;return null===t?(e={current:e},Se.memoizedState=e):t},useState:function(e){return Ie(Pe,e)},useInsertionEffect:Ne,useLayoutEffect:function(){},useCallback:function(e,t){return Me((function(){return e}),t)},useImperativeHandle:Ne,useEffect:Ne,useDebugValue:Ne,useDeferredValue:function(e){return Fe(),e},useTransition:function(){return Fe(),[!1,De]},useId:function(){var e=ye.treeContext,t=e.overflow;e=((e=e.id)&~(1<<32-pe(e)-1)).toString(32)+t;var n=Ve;if(null===n)throw Error(o(404));return t=we++,e=":"+n.idPrefix+"R"+e,0<t&&(e+="H"+t.toString(32)),e+":"},useMutableSource:function(e,t){return Fe(),t(e._source)},useSyncExternalStore:function(e,t,n){if(void 0===n)throw Error(o(407));return n()}},Ve=null,$e=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher;function Le(e){return console.error(e),null}function Oe(){}function Ae(e,t,n,r,o,a,l,u){e.allPendingTasks++,null===n?e.pendingRootTasks++:n.pendingTasks++;var i={node:t,ping:function(){var t=e.pingedTasks;t.push(i),1===t.length&&nt(e)},blockedBoundary:n,blockedSegment:r,abortSet:o,legacyContext:a,context:l,treeContext:u};return o.add(i),i}function je(e,t,n,r,o,a){return{status:0,id:-1,index:t,parentFlushed:!1,chunks:[],children:[],formatContext:r,boundary:n,lastPushedText:o,textEmbedded:a}}function qe(e,t){if(null!=(e=e.onError(t))&&"string"!=typeof e)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e}function He(e,t){var n=e.onShellError;n(t),(n=e.onFatalError)(t),null!==e.destination?(e.status=2,e.destination.destroy(t)):(e.status=1,e.fatalError=t)}function Ue(e,t,n,r,o){for(be={},ye=t,we=0,e=n(r,o);ke;)ke=!1,we=0,Ee+=1,Se=null,e=n(r,o);return _e(),e}function We(e,t,n,r){var a=n.render(),l=r.childContextTypes;if(null!=l){var u=t.legacyContext;if("function"!=typeof n.getChildContext)r=u;else{for(var i in n=n.getChildContext())if(!(i in l))throw Error(o(108,Q(r)||"Unknown",i));r=N({},u,n)}t.legacyContext=r,Ge(e,t,a),t.legacyContext=u}else Ge(e,t,a)}function Ze(e,t){if(e&&e.defaultProps){for(var n in t=N({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function Xe(e,t,n,r,a){if("function"==typeof n)if(n.prototype&&n.prototype.isReactComponent){a=te(n,t.legacyContext);var l=n.contextType;ce(l=new n(r,"object"==typeof l&&null!==l?l._currentValue2:a),n,r,a),We(e,t,l,n)}else{a=Ue(e,t,n,r,l=te(n,t.legacyContext));var u=0!==we;if("object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof)ce(a,n,r,l),We(e,t,a,n);else if(u){r=t.treeContext,t.treeContext=fe(r,1,0);try{Ge(e,t,a)}finally{t.treeContext=r}}else Ge(e,t,a)}else{if("string"!=typeof n){switch(n){case J:case G:case L:case O:case $:case U:return void Ge(e,t,r.children);case X:throw Error(o(343));case H:e:{n=t.blockedBoundary,a=t.blockedSegment,l=r.fallback,r=r.children;var i={id:null,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:u=new Set,errorDigest:null},s=je(0,a.chunks.length,i,a.formatContext,!1,!1);a.children.push(s),a.lastPushedText=!1;var c=je(0,0,null,a.formatContext,!1,!1);c.parentFlushed=!0,t.blockedBoundary=i,t.blockedSegment=c;try{if(Ye(e,t,r),e.responseState.generateStaticMarkup||c.lastPushedText&&c.textEmbedded&&c.chunks.push("\x3c!-- --\x3e"),c.status=1,et(i,c),0===i.pendingTasks)break e}catch(t){c.status=4,i.forceClientRender=!0,i.errorDigest=qe(e,t)}finally{t.blockedBoundary=n,t.blockedSegment=a}t=Ae(e,l,n,s,u,t.legacyContext,t.context,t.treeContext),e.pingedTasks.push(t)}return}if("object"==typeof n&&null!==n)switch(n.$$typeof){case q:if(r=Ue(e,t,n.render,r,a),0!==we){n=t.treeContext,t.treeContext=fe(n,1,0);try{Ge(e,t,r)}finally{t.treeContext=n}}else Ge(e,t,r);return;case W:return void Xe(e,t,n=n.type,r=Ze(n,r),a);case A:if(a=r.children,n=n._context,r=r.value,l=n._currentValue2,n._currentValue2=r,ne=r={parent:u=ne,depth:null===u?0:u.depth+1,context:n,parentValue:l,value:r},t.context=r,Ge(e,t,a),null===(e=ne))throw Error(o(403));return r=e.parentValue,e.context._currentValue2=r===Y?e.context._defaultValue:r,e=ne=e.parent,void(t.context=e);case j:return void Ge(e,t,r=(r=r.children)(n._currentValue2));case Z:return void Xe(e,t,n=(a=n._init)(n._payload),r=Ze(n,r),void 0)}throw Error(o(130,null==n?n:typeof n,""))}switch(l=P((a=t.blockedSegment).chunks,n,r,e.responseState,a.formatContext),a.lastPushedText=!1,u=a.formatContext,a.formatContext=function(e,t,n){switch(t){case"select":return x(1,null!=n.value?n.value:n.defaultValue);case"svg":return x(2,null);case"math":return x(3,null);case"foreignObject":return x(1,null);case"table":return x(4,null);case"thead":case"tbody":case"tfoot":return x(5,null);case"colgroup":return x(7,null);case"tr":return x(6,null)}return 4<=e.insertionMode||0===e.insertionMode?x(1,null):e}(u,n,r),Ye(e,t,l),a.formatContext=u,n){case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"input":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":break;default:a.chunks.push("</",n,">")}a.lastPushedText=!1}}function Ge(e,t,n){if(t.node=n,"object"==typeof n&&null!==n){switch(n.$$typeof){case z:return void Xe(e,t,n.type,n.props,n.ref);case V:throw Error(o(257));case Z:var r=n._init;return void Ge(e,t,n=r(n._payload))}if(S(n))return void Je(e,t,n);if(null===n||"object"!=typeof n?r=null:r="function"==typeof(r=K&&n[K]||n["@@iterator"])?r:null,r&&(r=r.call(n))){if(!(n=r.next()).done){var a=[];do{a.push(n.value),n=r.next()}while(!n.done);Je(e,t,a)}return}throw e=Object.prototype.toString.call(n),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(n).join(", ")+"}":e))}"string"==typeof n?(r=t.blockedSegment).lastPushedText=D(t.blockedSegment.chunks,n,e.responseState,r.lastPushedText):"number"==typeof n&&((r=t.blockedSegment).lastPushedText=D(t.blockedSegment.chunks,""+n,e.responseState,r.lastPushedText))}function Je(e,t,n){for(var r=n.length,o=0;o<r;o++){var a=t.treeContext;t.treeContext=fe(a,r,o);try{Ye(e,t,n[o])}finally{t.treeContext=a}}}function Ye(e,t,n){var r=t.blockedSegment.formatContext,o=t.legacyContext,a=t.context;try{return Ge(e,t,n)}catch(i){if(_e(),"object"!=typeof i||null===i||"function"!=typeof i.then)throw t.blockedSegment.formatContext=r,t.legacyContext=o,t.context=a,ie(a),i;n=i;var l=t.blockedSegment,u=je(0,l.chunks.length,null,l.formatContext,l.lastPushedText,!0);l.children.push(u),l.lastPushedText=!1,e=Ae(e,t.node,t.blockedBoundary,u,t.abortSet,t.legacyContext,t.context,t.treeContext).ping,n.then(e,e),t.blockedSegment.formatContext=r,t.legacyContext=o,t.context=a,ie(a)}}function Ke(e){var t=e.blockedBoundary;(e=e.blockedSegment).status=3,tt(this,t,e)}function Qe(e,t,n){var r=e.blockedBoundary;e.blockedSegment.status=3,null===r?(t.allPendingTasks--,2!==t.status&&(t.status=2,null!==t.destination&&t.destination.push(null))):(r.pendingTasks--,r.forceClientRender||(r.forceClientRender=!0,e=void 0===n?Error(o(432)):n,r.errorDigest=t.onError(e),r.parentFlushed&&t.clientRenderedBoundaries.push(r)),r.fallbackAbortableTasks.forEach((function(e){return Qe(e,t,n)})),r.fallbackAbortableTasks.clear(),t.allPendingTasks--,0===t.allPendingTasks&&(r=t.onAllReady)())}function et(e,t){if(0===t.chunks.length&&1===t.children.length&&null===t.children[0].boundary){var n=t.children[0];n.id=t.id,n.parentFlushed=!0,1===n.status&&et(e,n)}else e.completedSegments.push(t)}function tt(e,t,n){if(null===t){if(n.parentFlushed){if(null!==e.completedRootSegment)throw Error(o(389));e.completedRootSegment=n}e.pendingRootTasks--,0===e.pendingRootTasks&&(e.onShellError=Oe,(t=e.onShellReady)())}else t.pendingTasks--,t.forceClientRender||(0===t.pendingTasks?(n.parentFlushed&&1===n.status&&et(t,n),t.parentFlushed&&e.completedBoundaries.push(t),t.fallbackAbortableTasks.forEach(Ke,e),t.fallbackAbortableTasks.clear()):n.parentFlushed&&1===n.status&&(et(t,n),1===t.completedSegments.length&&t.parentFlushed&&e.partialBoundaries.push(t)));e.allPendingTasks--,0===e.allPendingTasks&&(e=e.onAllReady)()}function nt(e){if(2!==e.status){var t=ne,n=$e.current;$e.current=ze;var r=Ve;Ve=e.responseState;try{var o,a=e.pingedTasks;for(o=0;o<a.length;o++){var l=a[o],u=e,i=l.blockedSegment;if(0===i.status){ie(l.context);try{Ge(u,l,l.node),u.responseState.generateStaticMarkup||i.lastPushedText&&i.textEmbedded&&i.chunks.push("\x3c!-- --\x3e"),l.abortSet.delete(l),i.status=1,tt(u,l.blockedBoundary,i)}catch(e){if(_e(),"object"==typeof e&&null!==e&&"function"==typeof e.then){var s=l.ping;e.then(s,s)}else{l.abortSet.delete(l),i.status=4;var c=l.blockedBoundary,d=e,f=qe(u,d);if(null===c?He(u,d):(c.pendingTasks--,c.forceClientRender||(c.forceClientRender=!0,c.errorDigest=f,c.parentFlushed&&u.clientRenderedBoundaries.push(c))),u.allPendingTasks--,0===u.allPendingTasks)(0,u.onAllReady)()}}}}a.splice(0,o),null!==e.destination&&it(e,e.destination)}catch(t){qe(e,t),He(e,t)}finally{Ve=r,$e.current=n,n===ze&&ie(t)}}}function rt(e,t,n){switch(n.parentFlushed=!0,n.status){case 0:var r=n.id=e.nextSegmentId++;return n.lastPushedText=!1,n.textEmbedded=!1,e=e.responseState,t.push('<template id="'),t.push(e.placeholderPrefix),e=r.toString(16),t.push(e),t.push('"></template>');case 1:n.status=2;var a=!0;r=n.chunks;var l=0;n=n.children;for(var u=0;u<n.length;u++){for(a=n[u];l<a.index;l++)t.push(r[l]);a=ot(e,t,a)}for(;l<r.length-1;l++)t.push(r[l]);return l<r.length&&(a=t.push(r[l])),a;default:throw Error(o(390))}}function ot(e,t,n){var r=n.boundary;if(null===r)return rt(e,t,n);if(r.parentFlushed=!0,r.forceClientRender)return e.responseState.generateStaticMarkup||(r=r.errorDigest,t.push("\x3c!--$!--\x3e"),t.push("<template"),r&&(t.push(' data-dgst="'),r=b(r),t.push(r),t.push('"')),t.push("></template>")),rt(e,t,n),e=!!e.responseState.generateStaticMarkup||t.push("\x3c!--/$--\x3e");if(0<r.pendingTasks){r.rootSegmentID=e.nextSegmentId++,0<r.completedSegments.length&&e.partialBoundaries.push(r);var a=e.responseState,l=a.nextSuspenseID++;return a=a.boundaryPrefix+l.toString(16),r=r.id=a,I(t,e.responseState,r),rt(e,t,n),t.push("\x3c!--/$--\x3e")}if(r.byteSize>e.progressiveChunkSize)return r.rootSegmentID=e.nextSegmentId++,e.completedBoundaries.push(r),I(t,e.responseState,r.id),rt(e,t,n),t.push("\x3c!--/$--\x3e");if(e.responseState.generateStaticMarkup||t.push("\x3c!--$--\x3e"),1!==(n=r.completedSegments).length)throw Error(o(391));return ot(e,t,n[0]),e=!!e.responseState.generateStaticMarkup||t.push("\x3c!--/$--\x3e")}function at(e,t,n){return function(e,t,n,r){switch(n.insertionMode){case 0:case 1:return e.push('<div hidden id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 2:return e.push('<svg aria-hidden="true" style="display:none" id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 3:return e.push('<math aria-hidden="true" style="display:none" id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 4:return e.push('<table hidden id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 5:return e.push('<table hidden><tbody id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 6:return e.push('<table hidden><tr id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 7:return e.push('<table hidden><colgroup id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');default:throw Error(o(397))}}(t,e.responseState,n.formatContext,n.id),ot(e,t,n),function(e,t){switch(t.insertionMode){case 0:case 1:return e.push("</div>");case 2:return e.push("</svg>");case 3:return e.push("</math>");case 4:return e.push("</table>");case 5:return e.push("</tbody></table>");case 6:return e.push("</tr></table>");case 7:return e.push("</colgroup></table>");default:throw Error(o(397))}}(t,n.formatContext)}function lt(e,t,n){for(var r=n.completedSegments,a=0;a<r.length;a++)ut(e,t,n,r[a]);if(r.length=0,e=e.responseState,r=n.id,n=n.rootSegmentID,t.push(e.startInlineScript),e.sentCompleteBoundaryFunction?t.push('$RC("'):(e.sentCompleteBoundaryFunction=!0,t.push('function $RC(a,b){a=document.getElementById(a);b=document.getElementById(b);b.parentNode.removeChild(b);if(a){a=a.previousSibling;var f=a.parentNode,c=a.nextSibling,e=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d)if(0===e)break;else e--;else"$"!==d&&"$?"!==d&&"$!"!==d||e++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;b.firstChild;)f.insertBefore(b.firstChild,c);a.data="$";a._reactRetry&&a._reactRetry()}};$RC("')),null===r)throw Error(o(395));return n=n.toString(16),t.push(r),t.push('","'),t.push(e.segmentPrefix),t.push(n),t.push('")<\/script>')}function ut(e,t,n,r){if(2===r.status)return!0;var a=r.id;if(-1===a){if(-1===(r.id=n.rootSegmentID))throw Error(o(392));return at(e,t,r)}return at(e,t,r),e=e.responseState,t.push(e.startInlineScript),e.sentCompleteSegmentFunction?t.push('$RS("'):(e.sentCompleteSegmentFunction=!0,t.push('function $RS(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')),t.push(e.segmentPrefix),a=a.toString(16),t.push(a),t.push('","'),t.push(e.placeholderPrefix),t.push(a),t.push('")<\/script>')}function it(e,t){try{var n=e.completedRootSegment;if(null!==n&&0===e.pendingRootTasks){ot(e,t,n),e.completedRootSegment=null;var r=e.responseState.bootstrapChunks;for(n=0;n<r.length-1;n++)t.push(r[n]);n<r.length&&t.push(r[n])}var a,l=e.clientRenderedBoundaries;for(a=0;a<l.length;a++){var u=l[a];r=t;var i=e.responseState,s=u.id,c=u.errorDigest,d=u.errorMessage,f=u.errorComponentStack;if(r.push(i.startInlineScript),i.sentClientRenderFunction?r.push('$RX("'):(i.sentClientRenderFunction=!0,r.push('function $RX(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};$RX("')),null===s)throw Error(o(395));if(r.push(s),r.push('"'),c||d||f){r.push(",");var p=B(c||"");r.push(p)}if(d||f){r.push(",");var h=B(d||"");r.push(h)}if(f){r.push(",");var g=B(f);r.push(g)}if(!r.push(")<\/script>"))return e.destination=null,a++,void l.splice(0,a)}l.splice(0,a);var m=e.completedBoundaries;for(a=0;a<m.length;a++)if(!lt(e,t,m[a]))return e.destination=null,a++,void m.splice(0,a);m.splice(0,a);var b=e.partialBoundaries;for(a=0;a<b.length;a++){var y=b[a];e:{l=e,u=t;var v=y.completedSegments;for(i=0;i<v.length;i++)if(!ut(l,u,y,v[i])){i++,v.splice(0,i);var S=!1;break e}v.splice(0,i),S=!0}if(!S)return e.destination=null,a++,void b.splice(0,a)}b.splice(0,a);var x=e.completedBoundaries;for(a=0;a<x.length;a++)if(!lt(e,t,x[a]))return e.destination=null,a++,void x.splice(0,a);x.splice(0,a)}finally{0===e.allPendingTasks&&0===e.pingedTasks.length&&0===e.clientRenderedBoundaries.length&&0===e.completedBoundaries.length&&t.push(null)}}function st(e,t){try{var n=e.abortableTasks;n.forEach((function(n){return Qe(n,e,t)})),n.clear(),null!==e.destination&&it(e,e.destination)}catch(t){qe(e,t),He(e,t)}}function ct(){}function dt(e,t,n,r){var a=!1,l=null,u="",i={push:function(e){return null!==e&&(u+=e),!0},destroy:function(e){a=!0,l=e}},s=!1;if(e=function(e,t,n,r,o,a,l,u,i){var s=[],c=new Set;return(n=je(t={destination:null,responseState:t,progressiveChunkSize:void 0===r?12800:r,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:c,pingedTasks:s,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:void 0===o?Le:o,onAllReady:void 0===a?Oe:a,onShellReady:void 0===l?Oe:l,onShellError:void 0===u?Oe:u,onFatalError:void 0===i?Oe:i},0,null,n,!1,!1)).parentFlushed=!0,e=Ae(t,e,null,n,c,ee,null,de),s.push(e),t}(e,function(e,t){return{bootstrapChunks:[],startInlineScript:"<script>",placeholderPrefix:(t=void 0===t?"":t)+"P:",segmentPrefix:t+"S:",boundaryPrefix:t+"B:",idPrefix:t,nextSuspenseID:0,sentCompleteSegmentFunction:!1,sentCompleteBoundaryFunction:!1,sentClientRenderFunction:!1,generateStaticMarkup:e}}(n,t?t.identifierPrefix:void 0),{insertionMode:1,selectedValue:null},1/0,ct,void 0,(function(){s=!0}),void 0,void 0),nt(e),st(e,r),1===e.status)e.status=2,i.destroy(e.fatalError);else if(2!==e.status&&null===e.destination){e.destination=i;try{it(e,i)}catch(t){qe(e,t),He(e,t)}}if(a)throw l;if(!s)throw Error(o(426));return u}t.renderToNodeStream=function(){throw Error(o(207))},t.renderToStaticMarkup=function(e,t){return dt(e,t,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')},t.renderToStaticNodeStream=function(){throw Error(o(208))},t.renderToString=function(e,t){return dt(e,t,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')},t.version="18.3.1"},72911:(e,t,n)=>{var r=n(96540);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=null,l=0;function u(e,t){if(0!==t.length)if(512<t.length)0<l&&(e.enqueue(new Uint8Array(a.buffer,0,l)),a=new Uint8Array(512),l=0),e.enqueue(t);else{var n=a.length-l;n<t.length&&(0===n?e.enqueue(a):(a.set(t.subarray(0,n),l),e.enqueue(a),t=t.subarray(n)),a=new Uint8Array(512),l=0),a.set(t,l),l+=t.length}}function i(e,t){return u(e,t),!0}function s(e){a&&0<l&&(e.enqueue(new Uint8Array(a.buffer,0,l)),a=null,l=0)}var c=new TextEncoder;function d(e){return c.encode(e)}function f(e){return c.encode(e)}function p(e,t){"function"==typeof e.error?e.error(t):e.close()}var h=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,m={},b={};function y(e){return!!h.call(b,e)||!h.call(m,e)&&(g.test(e)?b[e]=!0:(m[e]=!0,!1))}function v(e,t,n,r,o,a,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=l}var S={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){S[e]=new v(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];S[t]=new v(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){S[e]=new v(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){S[e]=new v(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){S[e]=new v(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){S[e]=new v(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){S[e]=new v(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){S[e]=new v(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){S[e]=new v(e,5,!1,e.toLowerCase(),null,!1,!1)}));var x=/[\-:]([a-z])/g;function k(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(x,k);S[t]=new v(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(x,k);S[t]=new v(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(x,k);S[t]=new v(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){S[e]=new v(e,1,!1,e.toLowerCase(),null,!1,!1)})),S.xlinkHref=new v("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){S[e]=new v(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},C=["Webkit","ms","Moz","O"];Object.keys(w).forEach((function(e){C.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),w[t]=w[e]}))}));var E=/["'&<>]/;function F(e){if("boolean"==typeof e||"number"==typeof e)return""+e;e=""+e;var t=E.exec(e);if(t){var n,r="",o=0;for(n=t.index;n<e.length;n++){switch(e.charCodeAt(n)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#x27;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}o!==n&&(r+=e.substring(o,n)),o=n+1,r+=t}e=o!==n?r+e.substring(o,n):r}return e}var T=/([A-Z])/g,R=/^ms-/,_=Array.isArray,P=f("<script>"),I=f("<\/script>"),M=f('<script src="'),B=f('<script type="module" src="'),D=f('" async=""><\/script>'),N=/(<\/|<)(s)(cript)/gi;function z(e,t,n,r){return t+("s"===n?"\\u0073":"\\u0053")+r}function V(e,t){return{insertionMode:e,selectedValue:t}}var $=f("\x3c!-- --\x3e");function L(e,t,n,r){return""===t?r:(r&&e.push($),e.push(d(F(t))),!0)}var O=new Map,A=f(' style="'),j=f(":"),q=f(";");function H(e,t,n){if("object"!=typeof n)throw Error(o(62));for(var r in t=!0,n)if(h.call(n,r)){var a=n[r];if(null!=a&&"boolean"!=typeof a&&""!==a){if(0===r.indexOf("--")){var l=d(F(r));a=d(F((""+a).trim()))}else{l=r;var u=O.get(l);void 0!==u||(u=f(F(l.replace(T,"-$1").toLowerCase().replace(R,"-ms-"))),O.set(l,u)),l=u,a="number"==typeof a?0===a||h.call(w,r)?d(""+a):d(a+"px"):d(F((""+a).trim()))}t?(t=!1,e.push(A,l,j,a)):e.push(q,l,j,a)}}t||e.push(Z)}var U=f(" "),W=f('="'),Z=f('"'),X=f('=""');function G(e,t,n,r){switch(n){case"style":return void H(e,t,r);case"defaultValue":case"defaultChecked":case"innerHTML":case"suppressContentEditableWarning":case"suppressHydrationWarning":return}if(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])if(null!==(t=S.hasOwnProperty(n)?S[n]:null)){switch(typeof r){case"function":case"symbol":return;case"boolean":if(!t.acceptsBooleans)return}switch(n=d(t.attributeName),t.type){case 3:r&&e.push(U,n,X);break;case 4:!0===r?e.push(U,n,X):!1!==r&&e.push(U,n,W,d(F(r)),Z);break;case 5:isNaN(r)||e.push(U,n,W,d(F(r)),Z);break;case 6:!isNaN(r)&&1<=r&&e.push(U,n,W,d(F(r)),Z);break;default:t.sanitizeURL&&(r=""+r),e.push(U,n,W,d(F(r)),Z)}}else if(y(n)){switch(typeof r){case"function":case"symbol":return;case"boolean":if("data-"!==(t=n.toLowerCase().slice(0,5))&&"aria-"!==t)return}e.push(U,d(n),W,d(F(r)),Z)}}var J=f(">"),Y=f("/>");function K(e,t,n){if(null!=t){if(null!=n)throw Error(o(60));if("object"!=typeof t||!("__html"in t))throw Error(o(61));null!=(t=t.__html)&&e.push(d(""+t))}}var Q=f(' selected=""');function ee(e,t,n,r){e.push(oe(n));var o,a=n=null;for(o in t)if(h.call(t,o)){var l=t[o];if(null!=l)switch(o){case"children":n=l;break;case"dangerouslySetInnerHTML":a=l;break;default:G(e,r,o,l)}}return e.push(J),K(e,a,n),"string"==typeof n?(e.push(d(F(n))),null):n}var te=f("\n"),ne=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,re=new Map;function oe(e){var t=re.get(e);if(void 0===t){if(!ne.test(e))throw Error(o(65,e));t=f("<"+e),re.set(e,t)}return t}var ae=f("<!DOCTYPE html>");function le(e,t,n,a,l){switch(t){case"select":e.push(oe("select"));var u=null,i=null;for(p in n)if(h.call(n,p)){var s=n[p];if(null!=s)switch(p){case"children":u=s;break;case"dangerouslySetInnerHTML":i=s;break;case"defaultValue":case"value":break;default:G(e,a,p,s)}}return e.push(J),K(e,i,u),u;case"option":i=l.selectedValue,e.push(oe("option"));var c=s=null,f=null,p=null;for(u in n)if(h.call(n,u)){var g=n[u];if(null!=g)switch(u){case"children":s=g;break;case"selected":f=g;break;case"dangerouslySetInnerHTML":p=g;break;case"value":c=g;default:G(e,a,u,g)}}if(null!=i)if(n=null!==c?""+c:function(e){var t="";return r.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(s),_(i)){for(a=0;a<i.length;a++)if(""+i[a]===n){e.push(Q);break}}else""+i===n&&e.push(Q);else f&&e.push(Q);return e.push(J),K(e,p,s),s;case"textarea":for(s in e.push(oe("textarea")),p=i=u=null,n)if(h.call(n,s)&&null!=(c=n[s]))switch(s){case"children":p=c;break;case"value":u=c;break;case"defaultValue":i=c;break;case"dangerouslySetInnerHTML":throw Error(o(91));default:G(e,a,s,c)}if(null===u&&null!==i&&(u=i),e.push(J),null!=p){if(null!=u)throw Error(o(92));if(_(p)&&1<p.length)throw Error(o(93));u=""+p}return"string"==typeof u&&"\n"===u[0]&&e.push(te),null!==u&&e.push(d(F(""+u))),null;case"input":for(i in e.push(oe("input")),c=p=s=u=null,n)if(h.call(n,i)&&null!=(f=n[i]))switch(i){case"children":case"dangerouslySetInnerHTML":throw Error(o(399,"input"));case"defaultChecked":c=f;break;case"defaultValue":s=f;break;case"checked":p=f;break;case"value":u=f;break;default:G(e,a,i,f)}return null!==p?G(e,a,"checked",p):null!==c&&G(e,a,"checked",c),null!==u?G(e,a,"value",u):null!==s&&G(e,a,"value",s),e.push(Y),null;case"menuitem":for(var m in e.push(oe("menuitem")),n)if(h.call(n,m)&&null!=(u=n[m]))switch(m){case"children":case"dangerouslySetInnerHTML":throw Error(o(400));default:G(e,a,m,u)}return e.push(J),null;case"title":for(g in e.push(oe("title")),u=null,n)if(h.call(n,g)&&null!=(i=n[g]))switch(g){case"children":u=i;break;case"dangerouslySetInnerHTML":throw Error(o(434));default:G(e,a,g,i)}return e.push(J),u;case"listing":case"pre":for(c in e.push(oe(t)),i=u=null,n)if(h.call(n,c)&&null!=(s=n[c]))switch(c){case"children":u=s;break;case"dangerouslySetInnerHTML":i=s;break;default:G(e,a,c,s)}if(e.push(J),null!=i){if(null!=u)throw Error(o(60));if("object"!=typeof i||!("__html"in i))throw Error(o(61));null!=(n=i.__html)&&("string"==typeof n&&0<n.length&&"\n"===n[0]?e.push(te,d(n)):e.push(d(""+n)))}return"string"==typeof u&&"\n"===u[0]&&e.push(te),u;case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":for(var b in e.push(oe(t)),n)if(h.call(n,b)&&null!=(u=n[b]))switch(b){case"children":case"dangerouslySetInnerHTML":throw Error(o(399,t));default:G(e,a,b,u)}return e.push(Y),null;case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return ee(e,n,t,a);case"html":return 0===l.insertionMode&&e.push(ae),ee(e,n,t,a);default:if(-1===t.indexOf("-")&&"string"!=typeof n.is)return ee(e,n,t,a);for(f in e.push(oe(t)),i=u=null,n)if(h.call(n,f)&&null!=(s=n[f]))switch(f){case"children":u=s;break;case"dangerouslySetInnerHTML":i=s;break;case"style":H(e,a,s);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":break;default:y(f)&&"function"!=typeof s&&"symbol"!=typeof s&&e.push(U,d(f),W,d(F(s)),Z)}return e.push(J),K(e,i,u),u}}var ue=f("</"),ie=f(">"),se=f('<template id="'),ce=f('"></template>'),de=f("\x3c!--$--\x3e"),fe=f('\x3c!--$?--\x3e<template id="'),pe=f('"></template>'),he=f("\x3c!--$!--\x3e"),ge=f("\x3c!--/$--\x3e"),me=f("<template"),be=f('"'),ye=f(' data-dgst="');f(' data-msg="'),f(' data-stck="');var ve=f("></template>");function Se(e,t,n){if(u(e,fe),null===n)throw Error(o(395));return u(e,n),i(e,pe)}var xe=f('<div hidden id="'),ke=f('">'),we=f("</div>"),Ce=f('<svg aria-hidden="true" style="display:none" id="'),Ee=f('">'),Fe=f("</svg>"),Te=f('<math aria-hidden="true" style="display:none" id="'),Re=f('">'),_e=f("</math>"),Pe=f('<table hidden id="'),Ie=f('">'),Me=f("</table>"),Be=f('<table hidden><tbody id="'),De=f('">'),Ne=f("</tbody></table>"),ze=f('<table hidden><tr id="'),Ve=f('">'),$e=f("</tr></table>"),Le=f('<table hidden><colgroup id="'),Oe=f('">'),Ae=f("</colgroup></table>");var je=f('function $RS(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),qe=f('$RS("'),He=f('","'),Ue=f('")<\/script>'),We=f('function $RC(a,b){a=document.getElementById(a);b=document.getElementById(b);b.parentNode.removeChild(b);if(a){a=a.previousSibling;var f=a.parentNode,c=a.nextSibling,e=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d)if(0===e)break;else e--;else"$"!==d&&"$?"!==d&&"$!"!==d||e++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;b.firstChild;)f.insertBefore(b.firstChild,c);a.data="$";a._reactRetry&&a._reactRetry()}};$RC("'),Ze=f('$RC("'),Xe=f('","'),Ge=f('")<\/script>'),Je=f('function $RX(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};$RX("'),Ye=f('$RX("'),Ke=f('"'),Qe=f(")<\/script>"),et=f(","),tt=/[<\u2028\u2029]/g;function nt(e){return JSON.stringify(e).replace(tt,(function(e){switch(e){case"<":return"\\u003c";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React")}}))}var rt=Object.assign,ot=Symbol.for("react.element"),at=Symbol.for("react.portal"),lt=Symbol.for("react.fragment"),ut=Symbol.for("react.strict_mode"),it=Symbol.for("react.profiler"),st=Symbol.for("react.provider"),ct=Symbol.for("react.context"),dt=Symbol.for("react.forward_ref"),ft=Symbol.for("react.suspense"),pt=Symbol.for("react.suspense_list"),ht=Symbol.for("react.memo"),gt=Symbol.for("react.lazy"),mt=Symbol.for("react.scope"),bt=Symbol.for("react.debug_trace_mode"),yt=Symbol.for("react.legacy_hidden"),vt=Symbol.for("react.default_value"),St=Symbol.iterator;function xt(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case lt:return"Fragment";case at:return"Portal";case it:return"Profiler";case ut:return"StrictMode";case ft:return"Suspense";case pt:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case ct:return(e.displayName||"Context")+".Consumer";case st:return(e._context.displayName||"Context")+".Provider";case dt:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case ht:return null!==(t=e.displayName||null)?t:xt(e.type)||"Memo";case gt:t=e._payload,e=e._init;try{return xt(e(t))}catch(e){}}return null}var kt={};function wt(e,t){if(!(e=e.contextTypes))return kt;var n,r={};for(n in e)r[n]=t[n];return r}var Ct=null;function Et(e,t){if(e!==t){e.context._currentValue=e.parentValue,e=e.parent;var n=t.parent;if(null===e){if(null!==n)throw Error(o(401))}else{if(null===n)throw Error(o(401));Et(e,n)}t.context._currentValue=t.value}}function Ft(e){e.context._currentValue=e.parentValue,null!==(e=e.parent)&&Ft(e)}function Tt(e){var t=e.parent;null!==t&&Tt(t),e.context._currentValue=e.value}function Rt(e,t){if(e.context._currentValue=e.parentValue,null===(e=e.parent))throw Error(o(402));e.depth===t.depth?Et(e,t):Rt(e,t)}function _t(e,t){var n=t.parent;if(null===n)throw Error(o(402));e.depth===n.depth?Et(e,n):_t(e,n),t.context._currentValue=t.value}function Pt(e){var t=Ct;t!==e&&(null===t?Tt(e):null===e?Ft(t):t.depth===e.depth?Et(t,e):t.depth>e.depth?Rt(t,e):_t(t,e),Ct=e)}var It={isMounted:function(){return!1},enqueueSetState:function(e,t){null!==(e=e._reactInternals).queue&&e.queue.push(t)},enqueueReplaceState:function(e,t){(e=e._reactInternals).replace=!0,e.queue=[t]},enqueueForceUpdate:function(){}};function Mt(e,t,n,r){var o=void 0!==e.state?e.state:null;e.updater=It,e.props=n,e.state=o;var a={queue:[],replace:!1};e._reactInternals=a;var l=t.contextType;if(e.context="object"==typeof l&&null!==l?l._currentValue:r,"function"==typeof(l=t.getDerivedStateFromProps)&&(o=null==(l=l(n,o))?o:rt({},o,l),e.state=o),"function"!=typeof t.getDerivedStateFromProps&&"function"!=typeof e.getSnapshotBeforeUpdate&&("function"==typeof e.UNSAFE_componentWillMount||"function"==typeof e.componentWillMount))if(t=e.state,"function"==typeof e.componentWillMount&&e.componentWillMount(),"function"==typeof e.UNSAFE_componentWillMount&&e.UNSAFE_componentWillMount(),t!==e.state&&It.enqueueReplaceState(e,e.state,null),null!==a.queue&&0<a.queue.length)if(t=a.queue,l=a.replace,a.queue=null,a.replace=!1,l&&1===t.length)e.state=t[0];else{for(a=l?t[0]:e.state,o=!0,l=l?1:0;l<t.length;l++){var u=t[l];null!=(u="function"==typeof u?u.call(e,a,n,r):u)&&(o?(o=!1,a=rt({},a,u)):rt(a,u))}e.state=a}else a.queue=null}var Bt={id:1,overflow:""};function Dt(e,t,n){var r=e.id;e=e.overflow;var o=32-Nt(r)-1;r&=~(1<<o),n+=1;var a=32-Nt(t)+o;if(30<a){var l=o-o%5;return a=(r&(1<<l)-1).toString(32),r>>=l,o-=l,{id:1<<32-Nt(t)+o|n<<o|r,overflow:a+e}}return{id:1<<a|n<<o|r,overflow:e}}var Nt=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(zt(e)/Vt|0)|0},zt=Math.log,Vt=Math.LN2;var $t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},Lt=null,Ot=null,At=null,jt=null,qt=!1,Ht=!1,Ut=0,Wt=null,Zt=0;function Xt(){if(null===Lt)throw Error(o(321));return Lt}function Gt(){if(0<Zt)throw Error(o(312));return{memoizedState:null,queue:null,next:null}}function Jt(){return null===jt?null===At?(qt=!1,At=jt=Gt()):(qt=!0,jt=At):null===jt.next?(qt=!1,jt=jt.next=Gt()):(qt=!0,jt=jt.next),jt}function Yt(){Ot=Lt=null,Ht=!1,At=null,Zt=0,jt=Wt=null}function Kt(e,t){return"function"==typeof t?t(e):t}function Qt(e,t,n){if(Lt=Xt(),jt=Jt(),qt){var r=jt.queue;if(t=r.dispatch,null!==Wt&&void 0!==(n=Wt.get(r))){Wt.delete(r),r=jt.memoizedState;do{r=e(r,n.action),n=n.next}while(null!==n);return jt.memoizedState=r,[r,t]}return[jt.memoizedState,t]}return e=e===Kt?"function"==typeof t?t():t:void 0!==n?n(t):t,jt.memoizedState=e,e=(e=jt.queue={last:null,dispatch:null}).dispatch=tn.bind(null,Lt,e),[jt.memoizedState,e]}function en(e,t){if(Lt=Xt(),t=void 0===t?null:t,null!==(jt=Jt())){var n=jt.memoizedState;if(null!==n&&null!==t){var r=n[1];e:if(null===r)r=!1;else{for(var o=0;o<r.length&&o<t.length;o++)if(!$t(t[o],r[o])){r=!1;break e}r=!0}if(r)return n[0]}}return e=e(),jt.memoizedState=[e,t],e}function tn(e,t,n){if(25<=Zt)throw Error(o(301));if(e===Lt)if(Ht=!0,e={action:n,next:null},null===Wt&&(Wt=new Map),void 0===(n=Wt.get(t)))Wt.set(t,e);else{for(t=n;null!==t.next;)t=t.next;t.next=e}}function nn(){throw Error(o(394))}function rn(){}var on={readContext:function(e){return e._currentValue},useContext:function(e){return Xt(),e._currentValue},useMemo:en,useReducer:Qt,useRef:function(e){Lt=Xt();var t=(jt=Jt()).memoizedState;return null===t?(e={current:e},jt.memoizedState=e):t},useState:function(e){return Qt(Kt,e)},useInsertionEffect:rn,useLayoutEffect:function(){},useCallback:function(e,t){return en((function(){return e}),t)},useImperativeHandle:rn,useEffect:rn,useDebugValue:rn,useDeferredValue:function(e){return Xt(),e},useTransition:function(){return Xt(),[!1,nn]},useId:function(){var e=Ot.treeContext,t=e.overflow;e=((e=e.id)&~(1<<32-Nt(e)-1)).toString(32)+t;var n=an;if(null===n)throw Error(o(404));return t=Ut++,e=":"+n.idPrefix+"R"+e,0<t&&(e+="H"+t.toString(32)),e+":"},useMutableSource:function(e,t){return Xt(),t(e._source)},useSyncExternalStore:function(e,t,n){if(void 0===n)throw Error(o(407));return n()}},an=null,ln=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher;function un(e){return console.error(e),null}function sn(){}function cn(e,t,n,r,o,a,l,u){e.allPendingTasks++,null===n?e.pendingRootTasks++:n.pendingTasks++;var i={node:t,ping:function(){var t=e.pingedTasks;t.push(i),1===t.length&&En(e)},blockedBoundary:n,blockedSegment:r,abortSet:o,legacyContext:a,context:l,treeContext:u};return o.add(i),i}function dn(e,t,n,r,o,a){return{status:0,id:-1,index:t,parentFlushed:!1,chunks:[],children:[],formatContext:r,boundary:n,lastPushedText:o,textEmbedded:a}}function fn(e,t){if(null!=(e=e.onError(t))&&"string"!=typeof e)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e}function pn(e,t){var n=e.onShellError;n(t),(n=e.onFatalError)(t),null!==e.destination?(e.status=2,p(e.destination,t)):(e.status=1,e.fatalError=t)}function hn(e,t,n,r,o){for(Lt={},Ot=t,Ut=0,e=n(r,o);Ht;)Ht=!1,Ut=0,Zt+=1,jt=null,e=n(r,o);return Yt(),e}function gn(e,t,n,r){var a=n.render(),l=r.childContextTypes;if(null!=l){var u=t.legacyContext;if("function"!=typeof n.getChildContext)r=u;else{for(var i in n=n.getChildContext())if(!(i in l))throw Error(o(108,xt(r)||"Unknown",i));r=rt({},u,n)}t.legacyContext=r,yn(e,t,a),t.legacyContext=u}else yn(e,t,a)}function mn(e,t){if(e&&e.defaultProps){for(var n in t=rt({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function bn(e,t,n,r,a){if("function"==typeof n)if(n.prototype&&n.prototype.isReactComponent){a=wt(n,t.legacyContext);var l=n.contextType;Mt(l=new n(r,"object"==typeof l&&null!==l?l._currentValue:a),n,r,a),gn(e,t,l,n)}else{a=hn(e,t,n,r,l=wt(n,t.legacyContext));var u=0!==Ut;if("object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof)Mt(a,n,r,l),gn(e,t,a,n);else if(u){r=t.treeContext,t.treeContext=Dt(r,1,0);try{yn(e,t,a)}finally{t.treeContext=r}}else yn(e,t,a)}else{if("string"!=typeof n){switch(n){case yt:case bt:case ut:case it:case lt:case pt:return void yn(e,t,r.children);case mt:throw Error(o(343));case ft:e:{n=t.blockedBoundary,a=t.blockedSegment,l=r.fallback,r=r.children;var i={id:null,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:u=new Set,errorDigest:null},s=dn(0,a.chunks.length,i,a.formatContext,!1,!1);a.children.push(s),a.lastPushedText=!1;var c=dn(0,0,null,a.formatContext,!1,!1);c.parentFlushed=!0,t.blockedBoundary=i,t.blockedSegment=c;try{if(Sn(e,t,r),c.lastPushedText&&c.textEmbedded&&c.chunks.push($),c.status=1,wn(i,c),0===i.pendingTasks)break e}catch(t){c.status=4,i.forceClientRender=!0,i.errorDigest=fn(e,t)}finally{t.blockedBoundary=n,t.blockedSegment=a}t=cn(e,l,n,s,u,t.legacyContext,t.context,t.treeContext),e.pingedTasks.push(t)}return}if("object"==typeof n&&null!==n)switch(n.$$typeof){case dt:if(r=hn(e,t,n.render,r,a),0!==Ut){n=t.treeContext,t.treeContext=Dt(n,1,0);try{yn(e,t,r)}finally{t.treeContext=n}}else yn(e,t,r);return;case ht:return void bn(e,t,n=n.type,r=mn(n,r),a);case st:if(a=r.children,n=n._context,r=r.value,l=n._currentValue,n._currentValue=r,Ct=r={parent:u=Ct,depth:null===u?0:u.depth+1,context:n,parentValue:l,value:r},t.context=r,yn(e,t,a),null===(e=Ct))throw Error(o(403));return r=e.parentValue,e.context._currentValue=r===vt?e.context._defaultValue:r,e=Ct=e.parent,void(t.context=e);case ct:return void yn(e,t,r=(r=r.children)(n._currentValue));case gt:return void bn(e,t,n=(a=n._init)(n._payload),r=mn(n,r),void 0)}throw Error(o(130,null==n?n:typeof n,""))}switch(l=le((a=t.blockedSegment).chunks,n,r,e.responseState,a.formatContext),a.lastPushedText=!1,u=a.formatContext,a.formatContext=function(e,t,n){switch(t){case"select":return V(1,null!=n.value?n.value:n.defaultValue);case"svg":return V(2,null);case"math":return V(3,null);case"foreignObject":return V(1,null);case"table":return V(4,null);case"thead":case"tbody":case"tfoot":return V(5,null);case"colgroup":return V(7,null);case"tr":return V(6,null)}return 4<=e.insertionMode||0===e.insertionMode?V(1,null):e}(u,n,r),Sn(e,t,l),a.formatContext=u,n){case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"input":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":break;default:a.chunks.push(ue,d(n),ie)}a.lastPushedText=!1}}function yn(e,t,n){if(t.node=n,"object"==typeof n&&null!==n){switch(n.$$typeof){case ot:return void bn(e,t,n.type,n.props,n.ref);case at:throw Error(o(257));case gt:var r=n._init;return void yn(e,t,n=r(n._payload))}if(_(n))return void vn(e,t,n);if(null===n||"object"!=typeof n?r=null:r="function"==typeof(r=St&&n[St]||n["@@iterator"])?r:null,r&&(r=r.call(n))){if(!(n=r.next()).done){var a=[];do{a.push(n.value),n=r.next()}while(!n.done);vn(e,t,a)}return}throw e=Object.prototype.toString.call(n),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(n).join(", ")+"}":e))}"string"==typeof n?(r=t.blockedSegment).lastPushedText=L(t.blockedSegment.chunks,n,e.responseState,r.lastPushedText):"number"==typeof n&&((r=t.blockedSegment).lastPushedText=L(t.blockedSegment.chunks,""+n,e.responseState,r.lastPushedText))}function vn(e,t,n){for(var r=n.length,o=0;o<r;o++){var a=t.treeContext;t.treeContext=Dt(a,r,o);try{Sn(e,t,n[o])}finally{t.treeContext=a}}}function Sn(e,t,n){var r=t.blockedSegment.formatContext,o=t.legacyContext,a=t.context;try{return yn(e,t,n)}catch(i){if(Yt(),"object"!=typeof i||null===i||"function"!=typeof i.then)throw t.blockedSegment.formatContext=r,t.legacyContext=o,t.context=a,Pt(a),i;n=i;var l=t.blockedSegment,u=dn(0,l.chunks.length,null,l.formatContext,l.lastPushedText,!0);l.children.push(u),l.lastPushedText=!1,e=cn(e,t.node,t.blockedBoundary,u,t.abortSet,t.legacyContext,t.context,t.treeContext).ping,n.then(e,e),t.blockedSegment.formatContext=r,t.legacyContext=o,t.context=a,Pt(a)}}function xn(e){var t=e.blockedBoundary;(e=e.blockedSegment).status=3,Cn(this,t,e)}function kn(e,t,n){var r=e.blockedBoundary;e.blockedSegment.status=3,null===r?(t.allPendingTasks--,2!==t.status&&(t.status=2,null!==t.destination&&t.destination.close())):(r.pendingTasks--,r.forceClientRender||(r.forceClientRender=!0,e=void 0===n?Error(o(432)):n,r.errorDigest=t.onError(e),r.parentFlushed&&t.clientRenderedBoundaries.push(r)),r.fallbackAbortableTasks.forEach((function(e){return kn(e,t,n)})),r.fallbackAbortableTasks.clear(),t.allPendingTasks--,0===t.allPendingTasks&&(r=t.onAllReady)())}function wn(e,t){if(0===t.chunks.length&&1===t.children.length&&null===t.children[0].boundary){var n=t.children[0];n.id=t.id,n.parentFlushed=!0,1===n.status&&wn(e,n)}else e.completedSegments.push(t)}function Cn(e,t,n){if(null===t){if(n.parentFlushed){if(null!==e.completedRootSegment)throw Error(o(389));e.completedRootSegment=n}e.pendingRootTasks--,0===e.pendingRootTasks&&(e.onShellError=sn,(t=e.onShellReady)())}else t.pendingTasks--,t.forceClientRender||(0===t.pendingTasks?(n.parentFlushed&&1===n.status&&wn(t,n),t.parentFlushed&&e.completedBoundaries.push(t),t.fallbackAbortableTasks.forEach(xn,e),t.fallbackAbortableTasks.clear()):n.parentFlushed&&1===n.status&&(wn(t,n),1===t.completedSegments.length&&t.parentFlushed&&e.partialBoundaries.push(t)));e.allPendingTasks--,0===e.allPendingTasks&&(e=e.onAllReady)()}function En(e){if(2!==e.status){var t=Ct,n=ln.current;ln.current=on;var r=an;an=e.responseState;try{var o,a=e.pingedTasks;for(o=0;o<a.length;o++){var l=a[o],u=e,i=l.blockedSegment;if(0===i.status){Pt(l.context);try{yn(u,l,l.node),i.lastPushedText&&i.textEmbedded&&i.chunks.push($),l.abortSet.delete(l),i.status=1,Cn(u,l.blockedBoundary,i)}catch(e){if(Yt(),"object"==typeof e&&null!==e&&"function"==typeof e.then){var s=l.ping;e.then(s,s)}else{l.abortSet.delete(l),i.status=4;var c=l.blockedBoundary,d=e,f=fn(u,d);if(null===c?pn(u,d):(c.pendingTasks--,c.forceClientRender||(c.forceClientRender=!0,c.errorDigest=f,c.parentFlushed&&u.clientRenderedBoundaries.push(c))),u.allPendingTasks--,0===u.allPendingTasks)(0,u.onAllReady)()}}}}a.splice(0,o),null!==e.destination&&In(e,e.destination)}catch(t){fn(e,t),pn(e,t)}finally{an=r,ln.current=n,n===on&&Pt(t)}}}function Fn(e,t,n){switch(n.parentFlushed=!0,n.status){case 0:var r=n.id=e.nextSegmentId++;return n.lastPushedText=!1,n.textEmbedded=!1,e=e.responseState,u(t,se),u(t,e.placeholderPrefix),u(t,e=d(r.toString(16))),i(t,ce);case 1:n.status=2;var a=!0;r=n.chunks;var l=0;n=n.children;for(var s=0;s<n.length;s++){for(a=n[s];l<a.index;l++)u(t,r[l]);a=Tn(e,t,a)}for(;l<r.length-1;l++)u(t,r[l]);return l<r.length&&(a=i(t,r[l])),a;default:throw Error(o(390))}}function Tn(e,t,n){var r=n.boundary;if(null===r)return Fn(e,t,n);if(r.parentFlushed=!0,r.forceClientRender)r=r.errorDigest,i(t,he),u(t,me),r&&(u(t,ye),u(t,d(F(r))),u(t,be)),i(t,ve),Fn(e,t,n);else if(0<r.pendingTasks){r.rootSegmentID=e.nextSegmentId++,0<r.completedSegments.length&&e.partialBoundaries.push(r);var a=e.responseState,l=a.nextSuspenseID++;a=f(a.boundaryPrefix+l.toString(16)),r=r.id=a,Se(t,e.responseState,r),Fn(e,t,n)}else if(r.byteSize>e.progressiveChunkSize)r.rootSegmentID=e.nextSegmentId++,e.completedBoundaries.push(r),Se(t,e.responseState,r.id),Fn(e,t,n);else{if(i(t,de),1!==(n=r.completedSegments).length)throw Error(o(391));Tn(e,t,n[0])}return i(t,ge)}function Rn(e,t,n){return function(e,t,n,r){switch(n.insertionMode){case 0:case 1:return u(e,xe),u(e,t.segmentPrefix),u(e,d(r.toString(16))),i(e,ke);case 2:return u(e,Ce),u(e,t.segmentPrefix),u(e,d(r.toString(16))),i(e,Ee);case 3:return u(e,Te),u(e,t.segmentPrefix),u(e,d(r.toString(16))),i(e,Re);case 4:return u(e,Pe),u(e,t.segmentPrefix),u(e,d(r.toString(16))),i(e,Ie);case 5:return u(e,Be),u(e,t.segmentPrefix),u(e,d(r.toString(16))),i(e,De);case 6:return u(e,ze),u(e,t.segmentPrefix),u(e,d(r.toString(16))),i(e,Ve);case 7:return u(e,Le),u(e,t.segmentPrefix),u(e,d(r.toString(16))),i(e,Oe);default:throw Error(o(397))}}(t,e.responseState,n.formatContext,n.id),Tn(e,t,n),function(e,t){switch(t.insertionMode){case 0:case 1:return i(e,we);case 2:return i(e,Fe);case 3:return i(e,_e);case 4:return i(e,Me);case 5:return i(e,Ne);case 6:return i(e,$e);case 7:return i(e,Ae);default:throw Error(o(397))}}(t,n.formatContext)}function _n(e,t,n){for(var r=n.completedSegments,a=0;a<r.length;a++)Pn(e,t,n,r[a]);if(r.length=0,e=e.responseState,r=n.id,n=n.rootSegmentID,u(t,e.startInlineScript),e.sentCompleteBoundaryFunction?u(t,Ze):(e.sentCompleteBoundaryFunction=!0,u(t,We)),null===r)throw Error(o(395));return n=d(n.toString(16)),u(t,r),u(t,Xe),u(t,e.segmentPrefix),u(t,n),i(t,Ge)}function Pn(e,t,n,r){if(2===r.status)return!0;var a=r.id;if(-1===a){if(-1===(r.id=n.rootSegmentID))throw Error(o(392));return Rn(e,t,r)}return Rn(e,t,r),u(t,(e=e.responseState).startInlineScript),e.sentCompleteSegmentFunction?u(t,qe):(e.sentCompleteSegmentFunction=!0,u(t,je)),u(t,e.segmentPrefix),u(t,a=d(a.toString(16))),u(t,He),u(t,e.placeholderPrefix),u(t,a),i(t,Ue)}function In(e,t){a=new Uint8Array(512),l=0;try{var n=e.completedRootSegment;if(null!==n&&0===e.pendingRootTasks){Tn(e,t,n),e.completedRootSegment=null;var r=e.responseState.bootstrapChunks;for(n=0;n<r.length-1;n++)u(t,r[n]);n<r.length&&i(t,r[n])}var c,f=e.clientRenderedBoundaries;for(c=0;c<f.length;c++){var p=f[c];r=t;var h=e.responseState,g=p.id,m=p.errorDigest,b=p.errorMessage,y=p.errorComponentStack;if(u(r,h.startInlineScript),h.sentClientRenderFunction?u(r,Ye):(h.sentClientRenderFunction=!0,u(r,Je)),null===g)throw Error(o(395));if(u(r,g),u(r,Ke),(m||b||y)&&(u(r,et),u(r,d(nt(m||"")))),(b||y)&&(u(r,et),u(r,d(nt(b||"")))),y&&(u(r,et),u(r,d(nt(y)))),!i(r,Qe))return e.destination=null,c++,void f.splice(0,c)}f.splice(0,c);var v=e.completedBoundaries;for(c=0;c<v.length;c++)if(!_n(e,t,v[c]))return e.destination=null,c++,void v.splice(0,c);v.splice(0,c),s(t),a=new Uint8Array(512),l=0;var S=e.partialBoundaries;for(c=0;c<S.length;c++){var x=S[c];e:{f=e,p=t;var k=x.completedSegments;for(h=0;h<k.length;h++)if(!Pn(f,p,x,k[h])){h++,k.splice(0,h);var w=!1;break e}k.splice(0,h),w=!0}if(!w)return e.destination=null,c++,void S.splice(0,c)}S.splice(0,c);var C=e.completedBoundaries;for(c=0;c<C.length;c++)if(!_n(e,t,C[c]))return e.destination=null,c++,void C.splice(0,c);C.splice(0,c)}finally{s(t),0===e.allPendingTasks&&0===e.pingedTasks.length&&0===e.clientRenderedBoundaries.length&&0===e.completedBoundaries.length&&t.close()}}function Mn(e,t){try{var n=e.abortableTasks;n.forEach((function(n){return kn(n,e,t)})),n.clear(),null!==e.destination&&In(e,e.destination)}catch(t){fn(e,t),pn(e,t)}}t.renderToReadableStream=function(e,t){return new Promise((function(n,r){var o,a,l=new Promise((function(e,t){a=e,o=t})),u=function(e,t,n,r,o,a,l,u,i){var s=[],c=new Set;return(n=dn(t={destination:null,responseState:t,progressiveChunkSize:void 0===r?12800:r,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:c,pingedTasks:s,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:void 0===o?un:o,onAllReady:void 0===a?sn:a,onShellReady:void 0===l?sn:l,onShellError:void 0===u?sn:u,onFatalError:void 0===i?sn:i},0,null,n,!1,!1)).parentFlushed=!0,e=cn(t,e,null,n,c,kt,null,Bt),s.push(e),t}(e,function(e,t,n,r,o){e=void 0===e?"":e,t=void 0===t?P:f('<script nonce="'+F(t)+'">');var a=[];if(void 0!==n&&a.push(t,d((""+n).replace(N,z)),I),void 0!==r)for(n=0;n<r.length;n++)a.push(M,d(F(r[n])),D);if(void 0!==o)for(r=0;r<o.length;r++)a.push(B,d(F(o[r])),D);return{bootstrapChunks:a,startInlineScript:t,placeholderPrefix:f(e+"P:"),segmentPrefix:f(e+"S:"),boundaryPrefix:e+"B:",idPrefix:e,nextSuspenseID:0,sentCompleteSegmentFunction:!1,sentCompleteBoundaryFunction:!1,sentClientRenderFunction:!1}}(t?t.identifierPrefix:void 0,t?t.nonce:void 0,t?t.bootstrapScriptContent:void 0,t?t.bootstrapScripts:void 0,t?t.bootstrapModules:void 0),function(e){return V("http://www.w3.org/2000/svg"===e?2:"http://www.w3.org/1998/Math/MathML"===e?3:0,null)}(t?t.namespaceURI:void 0),t?t.progressiveChunkSize:void 0,t?t.onError:void 0,a,(function(){var e=new ReadableStream({type:"bytes",pull:function(e){if(1===u.status)u.status=2,p(e,u.fatalError);else if(2!==u.status&&null===u.destination){u.destination=e;try{In(u,e)}catch(e){fn(u,e),pn(u,e)}}},cancel:function(){Mn(u)}},{highWaterMark:0});e.allReady=l,n(e)}),(function(e){l.catch((function(){})),r(e)}),o);if(t&&t.signal){var i=t.signal,s=function(){Mn(u,i.reason),i.removeEventListener("abort",s)};i.addEventListener("abort",s)}En(u)}))},t.version="18.3.1"},65848:(e,t,n)=>{var r,o;r=n(17633),o=n(72911),t.version=r.version,t.renderToString=r.renderToString,t.renderToStaticMarkup=r.renderToStaticMarkup,t.renderToNodeStream=r.renderToNodeStream,t.renderToStaticNodeStream=r.renderToStaticNodeStream,t.renderToReadableStream=o.renderToReadableStream}}]);