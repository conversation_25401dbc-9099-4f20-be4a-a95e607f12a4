/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
"use strict";(globalThis.webpackChunkNutrientViewer=globalThis.webpackChunkNutrientViewer||[]).push([[329],{11005:(e,t,n)=>{n.d(t,{default:()=>i});var r=n(55512),o=n.n(r);function i(){return o()('/*!\n * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)\n *\n * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.\n *\n * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW\n * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.\n * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.\n * This notice may not be removed from this file.\n *\n * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/\n */(()=>{var e={4601:(e,t,n)=>{"use strict";var r=n(8420),o=n(3838),i=TypeError;e.exports=function(e){if(r(e))return e;throw new i(o(e)+" is not a function")}},3938:(e,t,n)=>{"use strict";var r=n(5335),o=String,i=TypeError;e.exports=function(e){if(r(e))return e;throw new i(o(e)+" is not an object")}},8186:(e,t,n)=>{"use strict";var r=n(5476),o=n(6539),i=n(3493),a=function(e){return function(t,n,a){var s=r(t),c=i(s);if(0===c)return!e&&-1;var u,l=o(a,c);if(e&&n!=n){for(;c>l;)if((u=s[l++])!=u)return!0}else for(;c>l;l++)if((e||l in s)&&s[l]===n)return e||l||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},9609:(e,t,n)=>{"use strict";var r=n(281);e.exports=r([].slice)},8569:(e,t,n)=>{"use strict";var r=n(281),o=r({}.toString),i=r("".slice);e.exports=function(e){return i(o(e),8,-1)}},4361:(e,t,n)=>{"use strict";var r=n(6490),o=n(5816),i=n(7632),a=n(3610);e.exports=function(e,t,n){for(var s=o(t),c=a.f,u=i.f,l=0;l<s.length;l++){var d=s[l];r(e,d)||n&&r(n,d)||c(e,d,u(t,d))}}},7712:(e,t,n)=>{"use strict";var r=n(5077),o=n(3610),i=n(6843);e.exports=r?function(e,t,n){return o.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},6843:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7485:(e,t,n)=>{"use strict";var r=n(8420),o=n(3610),i=n(8218),a=n(9430);e.exports=function(e,t,n,s){s||(s={});var c=s.enumerable,u=void 0!==s.name?s.name:t;if(r(n)&&i(n,u,s),s.global)c?e[t]=n:a(t,n);else{try{s.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=n:o.f(e,t,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return e}},9430:(e,t,n)=>{"use strict";var r=n(1301),o=Object.defineProperty;e.exports=function(e,t){try{o(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},5077:(e,t,n)=>{"use strict";var r=n(2074);e.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3262:(e,t,n)=>{"use strict";var r=n(1301),o=n(5335),i=r.document,a=o(i)&&o(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},290:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},4003:(e,t,n)=>{"use strict";var r=n(8340);e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},620:(e,t,n)=>{"use strict";var r=n(7006);e.exports="NODE"===r},8340:(e,t,n)=>{"use strict";var r=n(1301).navigator,o=r&&r.userAgent;e.exports=o?String(o):""},1928:(e,t,n)=>{"use strict";var r,o,i=n(1301),a=n(8340),s=i.process,c=i.Deno,u=s&&s.versions||c&&c.version,l=u&&u.v8;l&&(o=(r=l.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&a&&(!(r=a.match(/Edge\\/(\\d+)/))||r[1]>=74)&&(r=a.match(/Chrome\\/(\\d+)/))&&(o=+r[1]),e.exports=o},7006:(e,t,n)=>{"use strict";var r=n(1301),o=n(8340),i=n(8569),a=function(e){return o.slice(0,e.length)===e};e.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===i(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"},1605:(e,t,n)=>{"use strict";var r=n(1301),o=n(7632).f,i=n(7712),a=n(7485),s=n(9430),c=n(4361),u=n(4977);e.exports=function(e,t){var n,l,d,f,p,m=e.target,g=e.global,h=e.stat;if(n=g?r:h?r[m]||s(m,{}):r[m]&&r[m].prototype)for(l in t){if(f=t[l],d=e.dontCallGetSet?(p=o(n,l))&&p.value:n[l],!u(g?l:m+(h?".":"#")+l,e.forced)&&void 0!==d){if(typeof f==typeof d)continue;c(f,d)}(e.sham||d&&d.sham)&&i(f,"sham",!0),a(n,l,f,e)}}},2074:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},9070:(e,t,n)=>{"use strict";var r=n(8823),o=Function.prototype,i=o.apply,a=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(i):function(){return a.apply(i,arguments)})},6885:(e,t,n)=>{"use strict";var r=n(3091),o=n(4601),i=n(8823),a=r(r.bind);e.exports=function(e,t){return o(e),void 0===t?e:i?a(e,t):function(){return e.apply(t,arguments)}}},8823:(e,t,n)=>{"use strict";var r=n(2074);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},2368:(e,t,n)=>{"use strict";var r=n(8823),o=Function.prototype.call;e.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},2071:(e,t,n)=>{"use strict";var r=n(5077),o=n(6490),i=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=o(i,"name"),c=s&&"something"===function(){}.name,u=s&&(!r||r&&a(i,"name").configurable);e.exports={EXISTS:s,PROPER:c,CONFIGURABLE:u}},3091:(e,t,n)=>{"use strict";var r=n(8569),o=n(281);e.exports=function(e){if("Function"===r(e))return o(e)}},281:(e,t,n)=>{"use strict";var r=n(8823),o=Function.prototype,i=o.call,a=r&&o.bind.bind(i,i);e.exports=r?a:function(e){return function(){return i.apply(e,arguments)}}},6492:(e,t,n)=>{"use strict";var r=n(1301),o=n(8420);e.exports=function(e,t){return arguments.length<2?(n=r[e],o(n)?n:void 0):r[e]&&r[e][t];var n}},6457:(e,t,n)=>{"use strict";var r=n(4601),o=n(8406);e.exports=function(e,t){var n=e[t];return o(n)?void 0:r(n)}},1301:function(e,t,n){"use strict";var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},6490:(e,t,n)=>{"use strict";var r=n(281),o=n(2612),i=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(o(e),t)}},7708:e=>{"use strict";e.exports={}},8890:(e,t,n)=>{"use strict";var r=n(6492);e.exports=r("document","documentElement")},7694:(e,t,n)=>{"use strict";var r=n(5077),o=n(2074),i=n(3262);e.exports=!r&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},8664:(e,t,n)=>{"use strict";var r=n(281),o=n(2074),i=n(8569),a=Object,s=r("".split);e.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===i(e)?s(e,""):a(e)}:a},9965:(e,t,n)=>{"use strict";var r=n(281),o=n(8420),i=n(9310),a=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return a(e)}),e.exports=i.inspectSource},9206:(e,t,n)=>{"use strict";var r,o,i,a=n(8369),s=n(1301),c=n(5335),u=n(7712),l=n(6490),d=n(9310),f=n(5904),p=n(7708),m="Object already initialized",g=s.TypeError,h=s.WeakMap;if(a||d.state){var y=d.state||(d.state=new h);y.get=y.get,y.has=y.has,y.set=y.set,r=function(e,t){if(y.has(e))throw new g(m);return t.facade=e,y.set(e,t),t},o=function(e){return y.get(e)||{}},i=function(e){return y.has(e)}}else{var b=f("state");p[b]=!0,r=function(e,t){if(l(e,b))throw new g(m);return t.facade=e,u(e,b,t),t},o=function(e){return l(e,b)?e[b]:{}},i=function(e){return l(e,b)}}e.exports={set:r,get:o,has:i,enforce:function(e){return i(e)?o(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!c(t)||(n=o(t)).type!==e)throw new g("Incompatible receiver, "+e+" required");return n}}}},8420:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4977:(e,t,n)=>{"use strict";var r=n(2074),o=n(8420),i=/#|\\.prototype\\./,a=function(e,t){var n=c[s(e)];return n===l||n!==u&&(o(t)?r(t):!!t)},s=a.normalize=function(e){return String(e).replace(i,".").toLowerCase()},c=a.data={},u=a.NATIVE="N",l=a.POLYFILL="P";e.exports=a},8406:e=>{"use strict";e.exports=function(e){return null==e}},5335:(e,t,n)=>{"use strict";var r=n(8420);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},6926:e=>{"use strict";e.exports=!1},2328:(e,t,n)=>{"use strict";var r=n(6492),o=n(8420),i=n(7658),a=n(5225),s=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return o(t)&&i(t.prototype,s(e))}},3493:(e,t,n)=>{"use strict";var r=n(3747);e.exports=function(e){return r(e.length)}},8218:(e,t,n)=>{"use strict";var r=n(281),o=n(2074),i=n(8420),a=n(6490),s=n(5077),c=n(2071).CONFIGURABLE,u=n(9965),l=n(9206),d=l.enforce,f=l.get,p=String,m=Object.defineProperty,g=r("".slice),h=r("".replace),y=r([].join),b=s&&!o((function(){return 8!==m((function(){}),"length",{value:8}).length})),w=String(String).split("String"),v=e.exports=function(e,t,n){"Symbol("===g(p(t),0,7)&&(t="["+h(p(t),/^Symbol\\(([^)]*)\\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!a(e,"name")||c&&e.name!==t)&&(s?m(e,"name",{value:t,configurable:!0}):e.name=t),b&&n&&a(n,"arity")&&e.length!==n.arity&&m(e,"length",{value:n.arity});try{n&&a(n,"constructor")&&n.constructor?s&&m(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var r=d(e);return a(r,"source")||(r.source=y(w,"string"==typeof t?t:"")),e};Function.prototype.toString=v((function(){return i(this)&&f(this).source||u(this)}),"toString")},9830:e=>{"use strict";var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?n:t)(r)}},3610:(e,t,n)=>{"use strict";var r=n(5077),o=n(7694),i=n(4491),a=n(3938),s=n(6032),c=TypeError,u=Object.defineProperty,l=Object.getOwnPropertyDescriptor,d="enumerable",f="configurable",p="writable";t.f=r?i?function(e,t,n){if(a(e),t=s(t),a(n),"function"==typeof e&&"prototype"===t&&"value"in n&&p in n&&!n[p]){var r=l(e,t);r&&r[p]&&(e[t]=n.value,n={configurable:f in n?n[f]:r[f],enumerable:d in n?n[d]:r[d],writable:!1})}return u(e,t,n)}:u:function(e,t,n){if(a(e),t=s(t),a(n),o)try{return u(e,t,n)}catch(e){}if("get"in n||"set"in n)throw new c("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},7632:(e,t,n)=>{"use strict";var r=n(5077),o=n(2368),i=n(9304),a=n(6843),s=n(5476),c=n(6032),u=n(6490),l=n(7694),d=Object.getOwnPropertyDescriptor;t.f=r?d:function(e,t){if(e=s(e),t=c(t),l)try{return d(e,t)}catch(e){}if(u(e,t))return a(!o(i.f,e,t),e[t])}},4789:(e,t,n)=>{"use strict";var r=n(6347),o=n(290).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,o)}},8916:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},7658:(e,t,n)=>{"use strict";var r=n(281);e.exports=r({}.isPrototypeOf)},6347:(e,t,n)=>{"use strict";var r=n(281),o=n(6490),i=n(5476),a=n(8186).indexOf,s=n(7708),c=r([].push);e.exports=function(e,t){var n,r=i(e),u=0,l=[];for(n in r)!o(s,n)&&o(r,n)&&c(l,n);for(;t.length>u;)o(r,n=t[u++])&&(~a(l,n)||c(l,n));return l}},9304:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!n.call({1:2},1);t.f=o?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},9751:(e,t,n)=>{"use strict";var r=n(2368),o=n(8420),i=n(5335),a=TypeError;e.exports=function(e,t){var n,s;if("string"===t&&o(n=e.toString)&&!i(s=r(n,e)))return s;if(o(n=e.valueOf)&&!i(s=r(n,e)))return s;if("string"!==t&&o(n=e.toString)&&!i(s=r(n,e)))return s;throw new a("Can\'t convert object to primitive value")}},5816:(e,t,n)=>{"use strict";var r=n(6492),o=n(281),i=n(4789),a=n(8916),s=n(3938),c=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=i.f(s(e)),n=a.f;return n?c(t,n(e)):t}},1229:(e,t,n)=>{"use strict";var r=n(8406),o=TypeError;e.exports=function(e){if(r(e))throw new o("Can\'t call method on "+e);return e}},8827:(e,t,n)=>{"use strict";var r,o=n(1301),i=n(9070),a=n(8420),s=n(7006),c=n(8340),u=n(9609),l=n(6589),d=o.Function,f=/MSIE .\\./.test(c)||"BUN"===s&&((r=o.Bun.version.split(".")).length<3||"0"===r[0]&&(r[1]<3||"3"===r[1]&&"0"===r[2]));e.exports=function(e,t){var n=t?2:1;return f?function(r,o){var s=l(arguments.length,1)>n,c=a(r)?r:d(r),f=s?u(arguments,n):[],p=s?function(){i(c,this,f)}:c;return t?e(p,o):e(p)}:e}},5904:(e,t,n)=>{"use strict";var r=n(2),o=n(665),i=r("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},9310:(e,t,n)=>{"use strict";var r=n(6926),o=n(1301),i=n(9430),a="__core-js_shared__",s=e.exports=o[a]||i(a,{});(s.versions||(s.versions=[])).push({version:"3.42.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"})},2:(e,t,n)=>{"use strict";var r=n(9310);e.exports=function(e,t){return r[e]||(r[e]=t||{})}},2072:(e,t,n)=>{"use strict";var r=n(1928),o=n(2074),i=n(1301).String;e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},4922:(e,t,n)=>{"use strict";var r,o,i,a,s=n(1301),c=n(9070),u=n(6885),l=n(8420),d=n(6490),f=n(2074),p=n(8890),m=n(9609),g=n(3262),h=n(6589),y=n(4003),b=n(620),w=s.setImmediate,v=s.clearImmediate,_=s.process,O=s.Dispatch,F=s.Function,x=s.MessageChannel,S=s.String,I=0,E={},A="onreadystatechange";f((function(){r=s.location}));var j=function(e){if(d(E,e)){var t=E[e];delete E[e],t()}},D=function(e){return function(){j(e)}},P=function(e){j(e.data)},k=function(e){s.postMessage(S(e),r.protocol+"//"+r.host)};w&&v||(w=function(e){h(arguments.length,1);var t=l(e)?e:F(e),n=m(arguments,1);return E[++I]=function(){c(t,void 0,n)},o(I),I},v=function(e){delete E[e]},b?o=function(e){_.nextTick(D(e))}:O&&O.now?o=function(e){O.now(D(e))}:x&&!y?(a=(i=new x).port2,i.port1.onmessage=P,o=u(a.postMessage,a)):s.addEventListener&&l(s.postMessage)&&!s.importScripts&&r&&"file:"!==r.protocol&&!f(k)?(o=k,s.addEventListener("message",P,!1)):o=A in g("script")?function(e){p.appendChild(g("script"))[A]=function(){p.removeChild(this),j(e)}}:function(e){setTimeout(D(e),0)}),e.exports={set:w,clear:v}},6539:(e,t,n)=>{"use strict";var r=n(9328),o=Math.max,i=Math.min;e.exports=function(e,t){var n=r(e);return n<0?o(n+t,0):i(n,t)}},5476:(e,t,n)=>{"use strict";var r=n(8664),o=n(1229);e.exports=function(e){return r(o(e))}},9328:(e,t,n)=>{"use strict";var r=n(9830);e.exports=function(e){var t=+e;return t!=t||0===t?0:r(t)}},3747:(e,t,n)=>{"use strict";var r=n(9328),o=Math.min;e.exports=function(e){var t=r(e);return t>0?o(t,9007199254740991):0}},2612:(e,t,n)=>{"use strict";var r=n(1229),o=Object;e.exports=function(e){return o(r(e))}},874:(e,t,n)=>{"use strict";var r=n(2368),o=n(5335),i=n(2328),a=n(6457),s=n(9751),c=n(1602),u=TypeError,l=c("toPrimitive");e.exports=function(e,t){if(!o(e)||i(e))return e;var n,c=a(e,l);if(c){if(void 0===t&&(t="default"),n=r(c,e,t),!o(n)||i(n))return n;throw new u("Can\'t convert object to primitive value")}return void 0===t&&(t="number"),s(e,t)}},6032:(e,t,n)=>{"use strict";var r=n(874),o=n(2328);e.exports=function(e){var t=r(e,"string");return o(t)?t:t+""}},3838:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},665:(e,t,n)=>{"use strict";var r=n(281),o=0,i=Math.random(),a=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+i,36)}},5225:(e,t,n)=>{"use strict";var r=n(2072);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},4491:(e,t,n)=>{"use strict";var r=n(5077),o=n(2074);e.exports=r&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},6589:e=>{"use strict";var t=TypeError;e.exports=function(e,n){if(e<n)throw new t("Not enough arguments");return e}},8369:(e,t,n)=>{"use strict";var r=n(1301),o=n(8420),i=r.WeakMap;e.exports=o(i)&&/native code/.test(String(i))},1602:(e,t,n)=>{"use strict";var r=n(1301),o=n(2),i=n(6490),a=n(665),s=n(2072),c=n(5225),u=r.Symbol,l=o("wks"),d=c?u.for||u:u&&u.withoutSetter||a;e.exports=function(e){return i(l,e)||(l[e]=s&&i(u,e)?u[e]:d("Symbol."+e)),l[e]}},1857:(e,t,n)=>{"use strict";var r=n(1605),o=n(1301),i=n(4922).clear;r({global:!0,bind:!0,enumerable:!0,forced:o.clearImmediate!==i},{clearImmediate:i})},5417:(e,t,n)=>{"use strict";n(1857),n(9708)},9708:(e,t,n)=>{"use strict";var r=n(1605),o=n(1301),i=n(4922).set,a=n(8827),s=o.setImmediate?a(i,!1):i;r({global:!0,bind:!0,enumerable:!0,forced:o.setImmediate!==s},{setImmediate:s})},5480:()=>{},5920:e=>{e.exports="91696f29627bd391"},1211:e=>{e.exports="78d812816c38375a"}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";n(5417);"object"==typeof window&&(window._babelPolyfill=!1);const e=function e(t){let n;return n=t instanceof Error?t:new Error(t),Object.setPrototypeOf(n,e.prototype),n};e.prototype=Object.create(Error.prototype,{name:{value:"PSPDFKitError",enumerable:!1}});const t=e;function r(e,n){if(!e)throw new t(`Assertion failed: ${n||"Condition not met"}\\n\\nFor further assistance, please go to: https://pspdfkit.com/support/request`)}function o(e){console.log(e)}function i(){console.warn(...arguments)}function a(e){console.error(e)}["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[contenteditable]",\'[tabindex]:not([tabindex^="-"])\'].join(",");function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}function u(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}new WeakMap;const l="function"==typeof Buffer,d=("function"==typeof TextDecoder&&new TextDecoder,"function"==typeof TextEncoder&&new TextEncoder,Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")),f=(e=>{let t={};return e.forEach(((e,n)=>t[e]=n)),t})(d),p=/^(?:[A-Za-z\\d+\\/]{4})*?(?:[A-Za-z\\d+\\/]{2}(?:==)?|[A-Za-z\\d+\\/]{3}=?)?$/,m=String.fromCharCode.bind(String),g="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),h=e=>e.replace(/[^A-Za-z0-9\\+\\/]/g,""),y=e=>{let t,n,r,o,i="";const a=e.length%3;for(let a=0;a<e.length;){if((n=e.charCodeAt(a++))>255||(r=e.charCodeAt(a++))>255||(o=e.charCodeAt(a++))>255)throw new TypeError("invalid character found");t=n<<16|r<<8|o,i+=d[t>>18&63]+d[t>>12&63]+d[t>>6&63]+d[63&t]}return a?i.slice(0,a-3)+"===".substring(a):i},b=e=>{if(e=e.replace(/\\s+/g,""),!p.test(e))throw new TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,n,r,o="";for(let i=0;i<e.length;)t=f[e.charAt(i++)]<<18|f[e.charAt(i++)]<<12|(n=f[e.charAt(i++)])<<6|(r=f[e.charAt(i++)]),o+=64===n?m(t>>16&255):64===r?m(t>>16&255,t>>8&255):m(t>>16&255,t>>8&255,255&t);return o},w="function"==typeof atob?e=>atob(h(e)):l?e=>Buffer.from(e,"base64").toString("binary"):b,v=l?e=>g(Buffer.from(e,"base64")):e=>g(w(e).split("").map((e=>e.charCodeAt(0)))),_=e=>v(O(e)),O=e=>h(e.replace(/[-_]/g,(e=>"-"==e?"+":"/"))),F=_;function x(e){return{name:e.name,scale:{unitFrom:e.scale.unitFrom,unitTo:e.scale.unitTo,from:Number(e.scale.fromValue),to:Number(e.scale.toValue)},precision:e.precision}}var S=n(5920),I=n.n(S),E=n(1211),A=n.n(E),j=n(5480),D=n.n(j);let P;P="object"==typeof process&&"object"==typeof process.versions&&void 0!==process.versions.node?D():function(e){return"object"==typeof window?new Promise(((t,n)=>{const o=document.createElement("script");o.type="text/javascript",o.async=!0,o.onload=()=>t(window.PSPDFModuleInit),o.onerror=n,o.src=e;const{documentElement:i}=document;r(i),i.appendChild(o)})):(self.importScripts(e),Promise.resolve(self.PSPDFModuleInit))};const k=P,C="nutrient-viewer-lib/",T=`${C}nutrient-viewer${"-"+A()}.wasm.js`,$=`${C}nutrient-viewer${"-"+I()}.wasm`;function N(e,t){let n,r,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:globalThis;return new Promise((async c=>{n=function(e,t){let n;if("object"==typeof e){n=e.wasm;return{wasmBinaryFile:$,locateFile:e=>e,wasmBinary:n}}const r={wasmBinaryFile:e+$,locateFile:e=>e,wasmBinary:n};{const e=fetch(r.wasmBinaryFile,{credentials:"same-origin"}),n=13987964;e.then((e=>{const t=e.headers?.get("content-length");return t&&parseInt(t)>=n&&i("The WASM binary file is being served without compression. Due to its size, it\'s recommended to configure the server so application/wasm files are served with compression.\\n\\nFind more details in our Standalone performance guides: https://www.nutrient.io/guides/web/best-practices/caching-on-the-web/#gzip-and-brotli"),e})).catch((e=>{throw new Error(`Failed to load WASM binary file: ${e}`)})),r.instantiateWasm=(n,i)=>((async()=>{o(`Start ${r.wasmBinaryFile} download.`);const s=Date.now();let c,u;const l=!t&&"function"==typeof WebAssembly.instantiateStreaming;function d(){return e.then((e=>{if(!e.ok)throw new Error(`Error loading ${r.wasmBinaryFile}: ${e.statusText}`);return e.arrayBuffer()})).then((e=>(u=Date.now(),o(`Download complete, took: ${u-s}ms`),WebAssembly.instantiate(new Uint8Array(e),n))))}c=l?WebAssembly.instantiateStreaming(e,n).then((e=>(u=Date.now(),o(`Download and Instantiation complete, took: ${Date.now()-s}ms`),e))).catch((e=>{if(/mime.*type/i.test(e.message))return a(e.message),null;throw e})):d();let f=await c;null===f&&(a("Streaming instantiation failed! Falling back to classic instantiation. This might result in slower initialization time therefore we highly recommend to follow the troubleshooting instructions in our guides to fix this error: https://www.nutrient.io/guides/web/current/troubleshooting/common-issues/#response-has-unsupported-mime-type-error."),f=await d()),!l&&u&&o(`Compilation and Instantiation complete, took: ${Date.now()-u}ms`),i(f.instance,f.module)})(),{})}return r}(e,t),"string"==typeof e&&(r=await k(`${e}${T}`)),o("Using WASM method"),function(e){e.PSPDFLoggingServices={error(e,t){a(`[${e}] ${t}`)},warn(e,t){i(`[${e}] ${t}`)},info(e,t){0},debug(e,t){0},trace(e,t){0}},e.PSPDFUnicodeServices={stripDiacritics(e){const t={Á:"A",Ă:"A",Ắ:"A",Ặ:"A",Ằ:"A",Ẳ:"A",Ẵ:"A",Ǎ:"A",Â:"A",Ấ:"A",Ậ:"A",Ầ:"A",Ẩ:"A",Ẫ:"A",Ä:"A",Ǟ:"A",Ȧ:"A",Ǡ:"A",Ạ:"A",Ȁ:"A",À:"A",Ả:"A",Ȃ:"A",Ā:"A",Ą:"A",Å:"A",Ǻ:"A",Ḁ:"A",Ⱥ:"A",Ã:"A",Ꜳ:"AA",Æ:"AE",Ǽ:"AE",Ǣ:"AE",Ꜵ:"AO",Ꜷ:"AU",Ꜹ:"AV",Ꜻ:"AV",Ꜽ:"AY",Ḃ:"B",Ḅ:"B",Ɓ:"B",Ḇ:"B",Ƀ:"B",Ƃ:"B",Ć:"C",Č:"C",Ç:"C",Ḉ:"C",Ĉ:"C",Ċ:"C",Ƈ:"C",Ȼ:"C",Ď:"D",Ḑ:"D",Ḓ:"D",Ḋ:"D",Ḍ:"D",Ɗ:"D",Ḏ:"D",ǲ:"D",ǅ:"D",Đ:"D",Ƌ:"D",Ǳ:"DZ",Ǆ:"DZ",É:"E",Ĕ:"E",Ě:"E",Ȩ:"E",Ḝ:"E",Ê:"E",Ế:"E",Ệ:"E",Ề:"E",Ể:"E",Ễ:"E",Ḙ:"E",Ë:"E",Ė:"E",Ẹ:"E",Ȅ:"E",È:"E",Ẻ:"E",Ȇ:"E",Ē:"E",Ḗ:"E",Ḕ:"E",Ę:"E",Ɇ:"E",Ẽ:"E",Ḛ:"E",Ꝫ:"ET",Ḟ:"F",Ƒ:"F",Ǵ:"G",Ğ:"G",Ǧ:"G",Ģ:"G",Ĝ:"G",Ġ:"G",Ɠ:"G",Ḡ:"G",Ǥ:"G",Ḫ:"H",Ȟ:"H",Ḩ:"H",Ĥ:"H",Ⱨ:"H",Ḧ:"H",Ḣ:"H",Ḥ:"H",Ħ:"H",Í:"I",Ĭ:"I",Ǐ:"I",Î:"I",Ï:"I",Ḯ:"I",İ:"I",Ị:"I",Ȉ:"I",Ì:"I",Ỉ:"I",Ȋ:"I",Ī:"I",Į:"I",Ɨ:"I",Ĩ:"I",Ḭ:"I",Ꝺ:"D",Ꝼ:"F",Ᵹ:"G",Ꞃ:"R",Ꞅ:"S",Ꞇ:"T",Ꝭ:"IS",Ĵ:"J",Ɉ:"J",Ḱ:"K",Ǩ:"K",Ķ:"K",Ⱪ:"K",Ꝃ:"K",Ḳ:"K",Ƙ:"K",Ḵ:"K",Ꝁ:"K",Ꝅ:"K",Ĺ:"L",Ƚ:"L",Ľ:"L",Ļ:"L",Ḽ:"L",Ḷ:"L",Ḹ:"L",Ⱡ:"L",Ꝉ:"L",Ḻ:"L",Ŀ:"L",Ɫ:"L",ǈ:"L",Ł:"L",Ǉ:"LJ",Ḿ:"M",Ṁ:"M",Ṃ:"M",Ɱ:"M",Ń:"N",Ň:"N",Ņ:"N",Ṋ:"N",Ṅ:"N",Ṇ:"N",Ǹ:"N",Ɲ:"N",Ṉ:"N",Ƞ:"N",ǋ:"N",Ñ:"N",Ǌ:"NJ",Ó:"O",Ŏ:"O",Ǒ:"O",Ô:"O",Ố:"O",Ộ:"O",Ồ:"O",Ổ:"O",Ỗ:"O",Ö:"O",Ȫ:"O",Ȯ:"O",Ȱ:"O",Ọ:"O",Ő:"O",Ȍ:"O",Ò:"O",Ỏ:"O",Ơ:"O",Ớ:"O",Ợ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ȏ:"O",Ꝋ:"O",Ꝍ:"O",Ō:"O",Ṓ:"O",Ṑ:"O",Ɵ:"O",Ǫ:"O",Ǭ:"O",Ø:"O",Ǿ:"O",Õ:"O",Ṍ:"O",Ṏ:"O",Ȭ:"O",Ƣ:"OI",Ꝏ:"OO",Ɛ:"E",Ɔ:"O",Ȣ:"OU",Ṕ:"P",Ṗ:"P",Ꝓ:"P",Ƥ:"P",Ꝕ:"P",Ᵽ:"P",Ꝑ:"P",Ꝙ:"Q",Ꝗ:"Q",Ŕ:"R",Ř:"R",Ŗ:"R",Ṙ:"R",Ṛ:"R",Ṝ:"R",Ȑ:"R",Ȓ:"R",Ṟ:"R",Ɍ:"R",Ɽ:"R",Ꜿ:"C",Ǝ:"E",Ś:"S",Ṥ:"S",Š:"S",Ṧ:"S",Ş:"S",Ŝ:"S",Ș:"S",Ṡ:"S",Ṣ:"S",Ṩ:"S",Ť:"T",Ţ:"T",Ṱ:"T",Ț:"T",Ⱦ:"T",Ṫ:"T",Ṭ:"T",Ƭ:"T",Ṯ:"T",Ʈ:"T",Ŧ:"T",Ɐ:"A",Ꞁ:"L",Ɯ:"M",Ʌ:"V",Ꜩ:"TZ",Ú:"U",Ŭ:"U",Ǔ:"U",Û:"U",Ṷ:"U",Ü:"U",Ǘ:"U",Ǚ:"U",Ǜ:"U",Ǖ:"U",Ṳ:"U",Ụ:"U",Ű:"U",Ȕ:"U",Ù:"U",Ủ:"U",Ư:"U",Ứ:"U",Ự:"U",Ừ:"U",Ử:"U",Ữ:"U",Ȗ:"U",Ū:"U",Ṻ:"U",Ų:"U",Ů:"U",Ũ:"U",Ṹ:"U",Ṵ:"U",Ꝟ:"V",Ṿ:"V",Ʋ:"V",Ṽ:"V",Ꝡ:"VY",Ẃ:"W",Ŵ:"W",Ẅ:"W",Ẇ:"W",Ẉ:"W",Ẁ:"W",Ⱳ:"W",Ẍ:"X",Ẋ:"X",Ý:"Y",Ŷ:"Y",Ÿ:"Y",Ẏ:"Y",Ỵ:"Y",Ỳ:"Y",Ƴ:"Y",Ỷ:"Y",Ỿ:"Y",Ȳ:"Y",Ɏ:"Y",Ỹ:"Y",Ź:"Z",Ž:"Z",Ẑ:"Z",Ⱬ:"Z",Ż:"Z",Ẓ:"Z",Ȥ:"Z",Ẕ:"Z",Ƶ:"Z",Ĳ:"IJ",Œ:"OE",ᴀ:"A",ᴁ:"AE",ʙ:"B",ᴃ:"B",ᴄ:"C",ᴅ:"D",ᴇ:"E",ꜰ:"F",ɢ:"G",ʛ:"G",ʜ:"H",ɪ:"I",ʁ:"R",ᴊ:"J",ᴋ:"K",ʟ:"L",ᴌ:"L",ᴍ:"M",ɴ:"N",ᴏ:"O",ɶ:"OE",ᴐ:"O",ᴕ:"OU",ᴘ:"P",ʀ:"R",ᴎ:"N",ᴙ:"R",ꜱ:"S",ᴛ:"T",ⱻ:"E",ᴚ:"R",ᴜ:"U",ᴠ:"V",ᴡ:"W",ʏ:"Y",ᴢ:"Z",á:"a",ă:"a",ắ:"a",ặ:"a",ằ:"a",ẳ:"a",ẵ:"a",ǎ:"a",â:"a",ấ:"a",ậ:"a",ầ:"a",ẩ:"a",ẫ:"a",ä:"a",ǟ:"a",ȧ:"a",ǡ:"a",ạ:"a",ȁ:"a",à:"a",ả:"a",ȃ:"a",ā:"a",ą:"a",ᶏ:"a",ẚ:"a",å:"a",ǻ:"a",ḁ:"a",ⱥ:"a",ã:"a",ꜳ:"aa",æ:"ae",ǽ:"ae",ǣ:"ae",ꜵ:"ao",ꜷ:"au",ꜹ:"av",ꜻ:"av",ꜽ:"ay",ḃ:"b",ḅ:"b",ɓ:"b",ḇ:"b",ᵬ:"b",ᶀ:"b",ƀ:"b",ƃ:"b",ɵ:"o",ć:"c",č:"c",ç:"c",ḉ:"c",ĉ:"c",ɕ:"c",ċ:"c",ƈ:"c",ȼ:"c",ď:"d",ḑ:"d",ḓ:"d",ȡ:"d",ḋ:"d",ḍ:"d",ɗ:"d",ᶑ:"d",ḏ:"d",ᵭ:"d",ᶁ:"d",đ:"d",ɖ:"d",ƌ:"d",ı:"i",ȷ:"j",ɟ:"j",ʄ:"j",ǳ:"dz",ǆ:"dz",é:"e",ĕ:"e",ě:"e",ȩ:"e",ḝ:"e",ê:"e",ế:"e",ệ:"e",ề:"e",ể:"e",ễ:"e",ḙ:"e",ë:"e",ė:"e",ẹ:"e",ȅ:"e",è:"e",ẻ:"e",ȇ:"e",ē:"e",ḗ:"e",ḕ:"e",ⱸ:"e",ę:"e",ᶒ:"e",ɇ:"e",ẽ:"e",ḛ:"e",ꝫ:"et",ḟ:"f",ƒ:"f",ᵮ:"f",ᶂ:"f",ǵ:"g",ğ:"g",ǧ:"g",ģ:"g",ĝ:"g",ġ:"g",ɠ:"g",ḡ:"g",ᶃ:"g",ǥ:"g",ḫ:"h",ȟ:"h",ḩ:"h",ĥ:"h",ⱨ:"h",ḧ:"h",ḣ:"h",ḥ:"h",ɦ:"h",ẖ:"h",ħ:"h",ƕ:"hv",í:"i",ĭ:"i",ǐ:"i",î:"i",ï:"i",ḯ:"i",ị:"i",ȉ:"i",ì:"i",ỉ:"i",ȋ:"i",ī:"i",į:"i",ᶖ:"i",ɨ:"i",ĩ:"i",ḭ:"i",ꝺ:"d",ꝼ:"f",ᵹ:"g",ꞃ:"r",ꞅ:"s",ꞇ:"t",ꝭ:"is",ǰ:"j",ĵ:"j",ʝ:"j",ɉ:"j",ḱ:"k",ǩ:"k",ķ:"k",ⱪ:"k",ꝃ:"k",ḳ:"k",ƙ:"k",ḵ:"k",ᶄ:"k",ꝁ:"k",ꝅ:"k",ĺ:"l",ƚ:"l",ɬ:"l",ľ:"l",ļ:"l",ḽ:"l",ȴ:"l",ḷ:"l",ḹ:"l",ⱡ:"l",ꝉ:"l",ḻ:"l",ŀ:"l",ɫ:"l",ᶅ:"l",ɭ:"l",ł:"l",ǉ:"lj",ſ:"s",ẜ:"s",ẛ:"s",ẝ:"s",ḿ:"m",ṁ:"m",ṃ:"m",ɱ:"m",ᵯ:"m",ᶆ:"m",ń:"n",ň:"n",ņ:"n",ṋ:"n",ȵ:"n",ṅ:"n",ṇ:"n",ǹ:"n",ɲ:"n",ṉ:"n",ƞ:"n",ᵰ:"n",ᶇ:"n",ɳ:"n",ñ:"n",ǌ:"nj",ó:"o",ŏ:"o",ǒ:"o",ô:"o",ố:"o",ộ:"o",ồ:"o",ổ:"o",ỗ:"o",ö:"o",ȫ:"o",ȯ:"o",ȱ:"o",ọ:"o",ő:"o",ȍ:"o",ò:"o",ỏ:"o",ơ:"o",ớ:"o",ợ:"o",ờ:"o",ở:"o",ỡ:"o",ȏ:"o",ꝋ:"o",ꝍ:"o",ⱺ:"o",ō:"o",ṓ:"o",ṑ:"o",ǫ:"o",ǭ:"o",ø:"o",ǿ:"o",õ:"o",ṍ:"o",ṏ:"o",ȭ:"o",ƣ:"oi",ꝏ:"oo",ɛ:"e",ᶓ:"e",ɔ:"o",ᶗ:"o",ȣ:"ou",ṕ:"p",ṗ:"p",ꝓ:"p",ƥ:"p",ᵱ:"p",ᶈ:"p",ꝕ:"p",ᵽ:"p",ꝑ:"p",ꝙ:"q",ʠ:"q",ɋ:"q",ꝗ:"q",ŕ:"r",ř:"r",ŗ:"r",ṙ:"r",ṛ:"r",ṝ:"r",ȑ:"r",ɾ:"r",ᵳ:"r",ȓ:"r",ṟ:"r",ɼ:"r",ᵲ:"r",ᶉ:"r",ɍ:"r",ɽ:"r",ↄ:"c",ꜿ:"c",ɘ:"e",ɿ:"r",ś:"s",ṥ:"s",š:"s",ṧ:"s",ş:"s",ŝ:"s",ș:"s",ṡ:"s",ṣ:"s",ṩ:"s",ʂ:"s",ᵴ:"s",ᶊ:"s",ȿ:"s",ɡ:"g",ᴑ:"o",ᴓ:"o",ᴝ:"u",ť:"t",ţ:"t",ṱ:"t",ț:"t",ȶ:"t",ẗ:"t",ⱦ:"t",ṫ:"t",ṭ:"t",ƭ:"t",ṯ:"t",ᵵ:"t",ƫ:"t",ʈ:"t",ŧ:"t",ᵺ:"th",ɐ:"a",ᴂ:"ae",ǝ:"e",ᵷ:"g",ɥ:"h",ʮ:"h",ʯ:"h",ᴉ:"i",ʞ:"k",ꞁ:"l",ɯ:"m",ɰ:"m",ᴔ:"oe",ɹ:"r",ɻ:"r",ɺ:"r",ⱹ:"r",ʇ:"t",ʌ:"v",ʍ:"w",ʎ:"y",ꜩ:"tz",ú:"u",ŭ:"u",ǔ:"u",û:"u",ṷ:"u",ü:"u",ǘ:"u",ǚ:"u",ǜ:"u",ǖ:"u",ṳ:"u",ụ:"u",ű:"u",ȕ:"u",ù:"u",ủ:"u",ư:"u",ứ:"u",ự:"u",ừ:"u",ử:"u",ữ:"u",ȗ:"u",ū:"u",ṻ:"u",ų:"u",ᶙ:"u",ů:"u",ũ:"u",ṹ:"u",ṵ:"u",ᵫ:"ue",ꝸ:"um",ⱴ:"v",ꝟ:"v",ṿ:"v",ʋ:"v",ᶌ:"v",ⱱ:"v",ṽ:"v",ꝡ:"vy",ẃ:"w",ŵ:"w",ẅ:"w",ẇ:"w",ẉ:"w",ẁ:"w",ⱳ:"w",ẘ:"w",ẍ:"x",ẋ:"x",ᶍ:"x",ý:"y",ŷ:"y",ÿ:"y",ẏ:"y",ỵ:"y",ỳ:"y",ƴ:"y",ỷ:"y",ỿ:"y",ȳ:"y",ẙ:"y",ɏ:"y",ỹ:"y",ź:"z",ž:"z",ẑ:"z",ʑ:"z",ⱬ:"z",ż:"z",ẓ:"z",ȥ:"z",ẕ:"z",ᵶ:"z",ᶎ:"z",ʐ:"z",ƶ:"z",ɀ:"z",ﬀ:"ff",ﬃ:"ffi",ﬄ:"ffl",ﬁ:"fi",ﬂ:"fl",ĳ:"ij",œ:"oe",ﬆ:"st",ₐ:"a",ₑ:"e",ᵢ:"i",ⱼ:"j",ₒ:"o",ᵣ:"r",ᵤ:"u",ᵥ:"v",ₓ:"x"};if(!e.normalize)return e;const n=e.replace(/[^A-Za-z0-9[\\] ]/g,(function(e){return t[e]||e}));return e.normalize("NFC").length==n.normalize("NFC").length?n:""},escapeRegExp:e=>e.replace(/[.*+?^${}()|[\\]\\\\]/g,"\\\\$&"),match(e,t,n){const r=new RegExp(t,n?"g":"gi"),o=[];let i=r.exec(e);for(;null!=i;)o.push([i.index,i[0].length]),i=r.exec(e);return o},exec:(e,t)=>new RegExp(e).exec(t)}}(s);c({nativeModule:await r(n)})}))}function B(e){if("arrayBuffer"in e)return e.arrayBuffer().then((e=>new Uint8Array(e)));const t=new FileReader;return new Promise(((n,r)=>{t.onerror=e=>{r(new Error(e))},t.onload=e=>{n(new Uint8Array(e.target?.result))},t.readAsArrayBuffer(e)}))}const U="text",M="documentA",R="documentB",L="result",J=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;const z=function(e){return"string"==typeof e&&J.test(e)};const W=function(e){if(!z(e))throw TypeError("Invalid UUID");let t;return Uint8Array.of((t=parseInt(e.slice(0,8),16))>>>24,t>>>16&255,t>>>8&255,255&t,(t=parseInt(e.slice(9,13),16))>>>8,255&t,(t=parseInt(e.slice(14,18),16))>>>8,255&t,(t=parseInt(e.slice(19,23),16))>>>8,255&t,(t=parseInt(e.slice(24,36),16))/1099511627776&255,t/4294967296&255,t>>>24&255,t>>>16&255,t>>>8&255,255&t)},q=[];for(let e=0;e<256;++e)q.push((e+256).toString(16).slice(1));function V(e,t=0){return(q[e[t+0]]+q[e[t+1]]+q[e[t+2]]+q[e[t+3]]+"-"+q[e[t+4]]+q[e[t+5]]+"-"+q[e[t+6]]+q[e[t+7]]+"-"+q[e[t+8]]+q[e[t+9]]+"-"+q[e[t+10]]+q[e[t+11]]+q[e[t+12]]+q[e[t+13]]+q[e[t+14]]+q[e[t+15]]).toLowerCase()}new Uint8Array(16);function K(e){return 14+(e+64>>>9<<4)+1}function G(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function Z(e,t,n,r,o,i){return G((a=G(G(t,e),G(r,i)))<<(s=o)|a>>>32-s,n);var a,s}function Y(e,t,n,r,o,i,a){return Z(t&n|~t&r,e,t,o,i,a)}function H(e,t,n,r,o,i,a){return Z(t&r|n&~r,e,t,o,i,a)}function X(e,t,n,r,o,i,a){return Z(t^n^r,e,t,o,i,a)}function Q(e,t,n,r,o,i,a){return Z(n^(t|~r),e,t,o,i,a)}const ee=function(e){return function(e){const t=new Uint8Array(4*e.length);for(let n=0;n<4*e.length;n++)t[n]=e[n>>2]>>>n%4*8&255;return t}(function(e,t){const n=new Uint32Array(K(t)).fill(0);n.set(e),n[t>>5]|=128<<t%32,n[n.length-1]=t,e=n;let r=1732584193,o=-271733879,i=-1732584194,a=271733878;for(let t=0;t<e.length;t+=16){const n=r,s=o,c=i,u=a;r=Y(r,o,i,a,e[t],7,-680876936),a=Y(a,r,o,i,e[t+1],12,-389564586),i=Y(i,a,r,o,e[t+2],17,606105819),o=Y(o,i,a,r,e[t+3],22,-1044525330),r=Y(r,o,i,a,e[t+4],7,-176418897),a=Y(a,r,o,i,e[t+5],12,1200080426),i=Y(i,a,r,o,e[t+6],17,-1473231341),o=Y(o,i,a,r,e[t+7],22,-45705983),r=Y(r,o,i,a,e[t+8],7,1770035416),a=Y(a,r,o,i,e[t+9],12,-1958414417),i=Y(i,a,r,o,e[t+10],17,-42063),o=Y(o,i,a,r,e[t+11],22,-1990404162),r=Y(r,o,i,a,e[t+12],7,1804603682),a=Y(a,r,o,i,e[t+13],12,-40341101),i=Y(i,a,r,o,e[t+14],17,-1502002290),o=Y(o,i,a,r,e[t+15],22,1236535329),r=H(r,o,i,a,e[t+1],5,-165796510),a=H(a,r,o,i,e[t+6],9,-1069501632),i=H(i,a,r,o,e[t+11],14,643717713),o=H(o,i,a,r,e[t],20,-373897302),r=H(r,o,i,a,e[t+5],5,-701558691),a=H(a,r,o,i,e[t+10],9,38016083),i=H(i,a,r,o,e[t+15],14,-660478335),o=H(o,i,a,r,e[t+4],20,-405537848),r=H(r,o,i,a,e[t+9],5,568446438),a=H(a,r,o,i,e[t+14],9,-1019803690),i=H(i,a,r,o,e[t+3],14,-187363961),o=H(o,i,a,r,e[t+8],20,1163531501),r=H(r,o,i,a,e[t+13],5,-1444681467),a=H(a,r,o,i,e[t+2],9,-51403784),i=H(i,a,r,o,e[t+7],14,1735328473),o=H(o,i,a,r,e[t+12],20,-1926607734),r=X(r,o,i,a,e[t+5],4,-378558),a=X(a,r,o,i,e[t+8],11,-2022574463),i=X(i,a,r,o,e[t+11],16,1839030562),o=X(o,i,a,r,e[t+14],23,-35309556),r=X(r,o,i,a,e[t+1],4,-1530992060),a=X(a,r,o,i,e[t+4],11,1272893353),i=X(i,a,r,o,e[t+7],16,-155497632),o=X(o,i,a,r,e[t+10],23,-1094730640),r=X(r,o,i,a,e[t+13],4,681279174),a=X(a,r,o,i,e[t],11,-358537222),i=X(i,a,r,o,e[t+3],16,-722521979),o=X(o,i,a,r,e[t+6],23,76029189),r=X(r,o,i,a,e[t+9],4,-640364487),a=X(a,r,o,i,e[t+12],11,-421815835),i=X(i,a,r,o,e[t+15],16,530742520),o=X(o,i,a,r,e[t+2],23,-995338651),r=Q(r,o,i,a,e[t],6,-198630844),a=Q(a,r,o,i,e[t+7],10,1126891415),i=Q(i,a,r,o,e[t+14],15,-1416354905),o=Q(o,i,a,r,e[t+5],21,-57434055),r=Q(r,o,i,a,e[t+12],6,1700485571),a=Q(a,r,o,i,e[t+3],10,-1894986606),i=Q(i,a,r,o,e[t+10],15,-1051523),o=Q(o,i,a,r,e[t+1],21,-2054922799),r=Q(r,o,i,a,e[t+8],6,1873313359),a=Q(a,r,o,i,e[t+15],10,-30611744),i=Q(i,a,r,o,e[t+6],15,-1560198380),o=Q(o,i,a,r,e[t+13],21,1309151649),r=Q(r,o,i,a,e[t+4],6,-145523070),a=Q(a,r,o,i,e[t+11],10,-1120210379),i=Q(i,a,r,o,e[t+2],15,718787259),o=Q(o,i,a,r,e[t+9],21,-343485551),r=G(r,n),o=G(o,s),i=G(i,c),a=G(a,u)}return Uint32Array.of(r,o,i,a)}(function(e){if(0===e.length)return new Uint32Array;const t=new Uint32Array(K(8*e.length)).fill(0);for(let n=0;n<e.length;n++)t[n>>2]|=(255&e[n])<<n%4*8;return t}(e),8*e.length))};const te="6ba7b810-9dad-11d1-80b4-00c04fd430c8",ne="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function re(e,t,n,r,o,i){const a="string"==typeof n?function(e){e=unescape(encodeURIComponent(e));const t=new Uint8Array(e.length);for(let n=0;n<e.length;++n)t[n]=e.charCodeAt(n);return t}(n):n,s="string"==typeof r?W(r):r;if("string"==typeof r&&(r=W(r)),16!==r?.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let c=new Uint8Array(16+a.length);if(c.set(s),c.set(a,s.length),c=t(c),c[6]=15&c[6]|e,c[8]=63&c[8]|128,o){i=i||0;for(let e=0;e<16;++e)o[i+e]=c[e];return o}return V(c)}function oe(e,t,n,r){return re(48,ee,e,t,n,r)}oe.DNS=te,oe.URL=ne;"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto);function ie(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function ae(e,t){return e<<t|e>>>32-t}const se=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520],r=new Uint8Array(e.length+1);r.set(e),r[e.length]=128;const o=(e=r).length/4+2,i=Math.ceil(o/16),a=new Array(i);for(let t=0;t<i;++t){const n=new Uint32Array(16);for(let r=0;r<16;++r)n[r]=e[64*t+4*r]<<24|e[64*t+4*r+1]<<16|e[64*t+4*r+2]<<8|e[64*t+4*r+3];a[t]=n}a[i-1][14]=8*(e.length-1)/Math.pow(2,32),a[i-1][14]=Math.floor(a[i-1][14]),a[i-1][15]=8*(e.length-1)&4294967295;for(let e=0;e<i;++e){const r=new Uint32Array(80);for(let t=0;t<16;++t)r[t]=a[e][t];for(let e=16;e<80;++e)r[e]=ae(r[e-3]^r[e-8]^r[e-14]^r[e-16],1);let o=n[0],i=n[1],s=n[2],c=n[3],u=n[4];for(let e=0;e<80;++e){const n=Math.floor(e/20),a=ae(o,5)+ie(n,i,s,c)+u+t[n]+r[e]>>>0;u=c,c=s,s=ae(i,30)>>>0,i=o,o=a}n[0]=n[0]+o>>>0,n[1]=n[1]+i>>>0,n[2]=n[2]+s>>>0,n[3]=n[3]+c>>>0,n[4]=n[4]+u>>>0}return Uint8Array.of(n[0]>>24,n[0]>>16,n[0]>>8,n[0],n[1]>>24,n[1]>>16,n[1]>>8,n[1],n[2]>>24,n[2]>>16,n[2]>>8,n[2],n[3]>>24,n[3]>>16,n[3]>>8,n[3],n[4]>>24,n[4]>>16,n[4]>>8,n[4])};function ce(e,t,n,r){return re(80,se,e,t,n,r)}ce.DNS=te,ce.URL=ne;const ue="Maui_Android",le="Maui_iOS",de="Maui_MacCatalyst",fe="Maui_Windows",pe="FlutterForWeb",me="Electron",ge="cms",he="unableToShape",ye="requiresMoreFonts";async function be(e){try{const n=await fetch(e).catch((n=>{throw new t(`Error fetching dynamic fonts file ${e}. ${n}`)}));if(200!==n.status)throw new t(`Error fetching dynamic fonts file ${e}. Status code: ${n.status}`);return n}catch(e){throw e}}function we(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:globalThis.navigator?.userAgent??"";return e.indexOf("Trident/")>-1?"trident":e.indexOf("Edge/")>-1?"edge":e.indexOf("Chrome/")>-1?"blink":e.indexOf("AppleWebKit/")>-1?"webkit":e.indexOf("Gecko/")>-1?"gecko":"unknown"}function ve(e,t){const n=new RegExp(` ${t}/(\\\\d+)\\\\.*`);let r;return(r=e.match(n))?Number(r[1]):0}function _e(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:globalThis.navigator?.userAgent??"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:globalThis.navigator?.platform??"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:globalThis.navigator?.maxTouchPoints??0;return t.indexOf("MacIntel")>-1&&n>1?"ios":e.indexOf("Win")>-1?"windows":e.indexOf("iPhone")>-1||e.indexOf("iPad")>-1||"iPad"===t?"ios":e.indexOf("Mac")>-1?"macos":e.indexOf("Android")>-1?"android":e.indexOf("Linux")>-1?"linux":"unknown"}we();const Oe=_e(),Fe=(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:globalThis.navigator?.userAgent;switch(we(e)){case"trident":return ve(e,"Trident");case"edge":return ve(e,"Edge");case"blink":return ve(e,"Chrome");case"webkit":return ve(e,"Version");case"gecko":return ve(e,"Firefox");default:;}}(),function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_e(),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:we();"ios"===e||"android"===e||xe(t)}(),"ios"===Oe||xe(),"ios"===Oe);function xe(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:we();return("undefined"==typeof window||!window.PSPDFKIT_PLAYWRIGHT_TEST)&&("webkit"===e&&"undefined"!=typeof TouchEvent)}let Se;"undefined"!=typeof window&&(window.addEventListener("mousemove",(function e(){Se=!1,window.removeEventListener("mousemove",e)})),window.addEventListener("pointermove",(function e(t){"mouse"!==t.pointerType&&"pen"!==t.pointerType||(Se=!1),window.removeEventListener("pointermove",e)})));/Mac/i.test(globalThis.navigator?.platform);function Ie(){return"object"==typeof navigator&&"string"==typeof navigator.userAgent&&navigator.userAgent.indexOf("Electron/")>=0}const Ee="/create.pdf",Ae="/save.pdf",je="/embedded.pdf",De="WebAssembly module not loaded.";let Pe=null,ke=!1,Ce=!1,Te=null;const $e={password:void 0,initialPageIndex:void 0};let Ne=[],Be=$e,Ue=null,Me=null;const Re=new Set,Le="/fonts";let Je="";const ze=new Set;let We=!1;var qe=function(e){return e[e.Daemon=0]="Daemon",e[e.WASM=1]="WASM",e}(qe||{});const Ve=[];const Ke=["configurePDFJavaScriptSupport","closeDocument","setFormValues","openDocument","saveDocument","importXFDF","importInstantDocumentJSON"],Ge={annotationsAndForms:"annotations_and_forms",assemble:"assemble",extract:"extract",extractAccessibility:"extract_accessibility",fillForms:"fill_forms",modification:"modification",printHighQuality:"print_high_quality",printing:"printing"};function Ze(e){r(Pe,"WebAssembly module not loaded."),We&&performance.mark("exec");for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];const i=n.map((e=>function(e){return Array.isArray(e)&&0===e.length}(e)?JSON.stringify(e):e));Ke.includes(e)&&at();const a=Pe[e](...i)||\'{ "success": true }\',s=JSON.parse(a);if(We&&Ve.push({type:qe.WASM,name:e,args:i,time:performance.measure("exec - RECORD_CALLS","exec").duration}),!s.success)throw new Error(s.error);return s}const Ye=["run_pdf_formatting_scripts","run_pdf_javascript","set_form_values_get_script_changes","edit_document","prepare_sign","sign","on_keystroke_event","save_document","update_annotation","import_document_json"];function He(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;r(Pe,"WebAssembly module not loaded.");const o=JSON.stringify({type:e,...t});let i;if(We&&performance.mark("daemonExec"),Ye.includes(e)&&at(),n)try{const e="string"==typeof n?new Uint8Array((a=n,(new TextEncoder).encode(a).buffer)):new Uint8Array(n),t=Pe.allocateMemory(e.byteLength);try{t.view.set(e);const n=Pe.memoryHandleToVector(t);try{i=Pe.dispatchCommandWithBinary(o,n)}finally{n.delete()}}finally{t.delete()}}catch(e){throw e}else i=Pe.dispatchCommand(o);var a;if(i.hasError()){const t=new Error(i.getErrorMessage()||"There was an error while executing the command: "+e);throw i.delete(),t}const s=[];for(let e=0;e<i.getRepliesCount();e++)i.hasJSONReply(e)&&s.push(JSON.parse(i.getJSONReply(e))),i.hasBinaryReply(e)&&s.push(i.getBinaryReply(e).slice(0));return We&&Ve.push({type:qe.Daemon,name:e,args:[o],time:performance.measure("daemonExec: RECORD_CALLS","daemonExec").duration}),i.delete(),s}function Xe(e){try{const t=(new TextDecoder).decode(e);return JSON.parse(t)}catch(e){throw e}}function Qe(e,t){return`${e}/${t}.pdf`}async function et(e,t){const n=Math.random().toString(36).slice(-5),o=await Promise.all(Object.entries(t).map((async t=>{let[r,o]=t;return e.forEach((e=>{"document"in e&&e.document===r&&"importDocument"===e.type?(!1===e.treatImportedDocumentAsOnePage&&(e.treatImportedDocumentAsOnePage=void 0),e.document=Qe(n,r)):("dataFilePath"in e&&e.dataFilePath===r&&"applyInstantJson"===e.type||"dataFilePath"in e&&e.dataFilePath===r&&"applyXfdf"===e.type)&&(e.dataFilePath=Qe(n,r))})),{basename:r,buffer:await B(o)}})));return o.forEach((e=>{!function(e,t,n){r(Pe,De),Pe.FS.analyzePath(e).exists||Pe.FS.mkdir(e),Pe.FS.writeFile(Qe(e,t),n)}(n,e.basename,e.buffer)})),function(){o.forEach((e=>{!function(e,t){r(Pe,De),Pe.FS.unlink(Qe(e,t))}(n,e.basename)}))}}function tt(){Ce=!0;const e=Ze("configurePDFJavaScriptSupport",!0);return r(e.success,"An error occurred while executing the document level JavaScript."),e.changes||[]}function nt(e,t){try{He("edit_document",{save_path:t,operations:e})}catch(e){throw new Error(`Error applying operations to document: ${e.message}`)}}let rt=null,ot=!1,it=!1;function at(){ot||(rt=null,it=!1)}function st(e){ot&&!e&&(ot=!1,rt&&ut(rt)),ot=e}function ct(){return null===rt&&(rt=He("read_form_json_objects",{include_line_height_factor:!1}),it=!1),rt}function ut(e){if(ot){if(null===e)throw ot=!1,new Error("Error enqueuing form JSON objects: form fields JSON is null.");return rt&&!it&&(it=!0),void(rt=e)}if(it||!ot)try{at(),He("apply_form_json_objects",{form_fields_with_widgets:e})}catch(e){throw new Error("Error applying form JSON objects to "+Ee+": "+e.message)}}function lt(e,t,n,o,i,a,s){const c=ft(i,{width:n,height:o});if(r("number"==typeof e.pageIndex,"Annotation must have a pageIndex"),!Re.has(e.pdfObjectId))try{r("number"==typeof e.pdfObjectId,"Cannot call renderAnnotation() for an annotation without pdfObjectId.");const t=He("render_annotation",{page:e.pageIndex,annotation_id:e.pdfObjectId,format:c,bitmap_width:n,bitmap_height:o,appearance_stream_type:a||"normal",render_form_highlight:s??!0});if("bitmap"===c)return t[0];{const e=new Blob([t[0]],{type:`image/${c}`});return URL.createObjectURL(e)}}catch(e){return void 0}}const dt={SharePoint:"SPO",Salesforce:"SF",[le]:"MauiIOS",[ue]:"MauiAndroid",[de]:"MauiMacCatalyst",[fe]:"MauiWindows",[pe]:"FlutterForWeb",NodeJS:"NodeJS",Electron:"Electron"};function ft(e,t){let{width:n,height:r}=t;return"webp"===e&&(n>16383||r>16383)||Fe&&n*r>16777216&&"bitmap"===e?"png":e}function pt(e){return e.map((e=>Ge[e]))}function mt(e,t){return e.pdfObjectId===t.pdfObjectId}const gt=new class{constructor(){u(this,"_pdfObjectIdsForIds",{}),u(this,"comparisonDocuments",{}),u(this,"lastOpenedComparisonDocument",null),u(this,"persistedOpenDocument",null),u(this,"persistedOpenDocumentConfiguration",$e),u(this,"files",new Map),u(this,"fileDescriptor",0)}loadNativeModule(e,t){let{disableWebAssemblyStreaming:n,enableAutomaticLinkExtraction:r,overrideMemoryLimit:o}=t;return Ue=Date.now(),ke=r,Te=o,N(e,n).then((e=>{let{nativeModule:t}=e;Pe=t;const n=Ze("PSPDFKitVersion").version;if(1!==n)throw new Error(`Native version mismatch. Please update the dependencies. Expected 1 but got ${n}.`)}))}corePBProtoBridge(e,t,n){const r=Pe.allocateMemory(n.byteLength);let o=null,i=null;try{r.view.set(n),o=Pe.corePBProtoBridge(e,t,r),i=o.result;const a={success:o.success,result:i.view.slice()};return Promise.resolve(a)}finally{r.delete(),o?.result?.delete(),o?.delete(),i?.delete()}}async writeFile(e,t){r(Pe,De),Pe.FS.writeFile(e,new Uint8Array(t))}async deleteFile(e){r(Pe,De),Pe.FS.unlink(e)}async readFile(e){return r(Pe,De),Pe.FS.readFile(e)}async renderLinearized(e){const t=Pe.allocateMemory(e.byteLength);let n=null;try{return t.view.set(e),n=Pe.renderLinearized(t),Promise.resolve(n.view.slice())}finally{t.delete(),n?.delete()}}async load(e,t,n){let i,{mainThreadOrigin:a,customFonts:s,dynamicFonts:c,productId:u,featureFlags:l}=n;r(Pe,De);try{if(Pe.FS.analyzePath(Le).exists||Pe.FS.mkdir(Le),Je=s?`${Le}/custom-fonts`:"",s&&!Pe.FS.analyzePath(Je).exists&&function(e,t){r(Pe,De),Pe.FS.analyzePath(t).exists||Pe.FS.mkdir(t),Pe.FS.mount(Pe.FS.filesystems.WORKERFS,{blobs:e},t)}(s,Je),Ie()&&(r(null==u||u===me,`PSPDFKit is running on Electron, but has been setup to be used with ${u}Please contact support to resolve the issue.`),u=me),i=Pe.initPSPDFKit(null==t?"":t,a,Le,"",u&&void 0!==dt[u]?dt[u]:"",JSON.stringify(l??[])),c){const e=await be(c);He("dynamic_font_loading/set_metadata_file",void 0,await e.arrayBuffer()),Me=c.split("/").slice(0,-1).join("/")}}catch(e){throw e}const d=JSON.parse(i);if(Ue&&o(`Native initialization complete, took: ${Date.now()-Ue}ms`),!d.success)throw new Error("Failed to initialize PSPDFKit: "+d.error);return d}getNativeModule(){return Pe}async openDocument(e,t){try{return r(Pe,De),null!==Te&&Ze("overrideMemoryLimit",Te),Pe.FS.writeFile(Ee,new Uint8Array(e)),this.openAndReturnDocumentInfo(t??$e)}finally{this._pdfObjectIdsForIds={}}}async openCreateFilePathDocument(e){try{return null!==Te&&Ze("overrideMemoryLimit",Te),this.openAndReturnDocumentInfo(e??$e)}finally{this._pdfObjectIdsForIds={}}}async getSuggestedLineHeightFactor(e,t){return He("get_suggested_line_height_factor",{annotation_ids:[e],page:t})[0].line_height_factors[e.toString()]}async getAvailableFontFaces(e){if(!e)return[];const t=He("get_available_font_faces");return t[0]?.font_faces.reduce(((t,n)=>{const r=e.find((e=>e.name===n.path.replace(`${Je}/`,"")));return r?t.concat({readableName:n.fullName,name:r.name}):t}),[])}async setFontSubstitutions(e){try{He("set_font_substitutions",{substitutions:e})}catch(e){throw new Error("Error setting fonts substitutions: "+e.message)}}async getClosestSnapPoint(e,t){return He("get_closest_snap",{q:[e,t]})[0]}async configureSnapper(e){He("configure_snapper",{page:e})}async reloadDocument(){try{return st(!1),Ze("closeDocument"),this.openAndReturnDocumentInfo(Be)}finally{this._pdfObjectIdsForIds={}}}async openAndReturnDocumentInfo(e){Be=e,Ze("openDocument",Ee,JSON.stringify(e)),Ce&&tt(),He(ke?"enable_automatic_link_extraction":"disable_automatic_link_extraction");const t=Xe(He("document_info",{requested_info:["ID"]})[0]);if(t.pageCount<=0)return t;let n=[];if("number"==typeof e?.initialPageIndex){const r=await this.getPageInfo(e.initialPageIndex);for(let o=0;o<t.pageCount;o++){const t={...r,pageIndex:o,pageLabel:e.initialPageIndex===o?r.pageLabel:String(o+1)};n.push(t)}}else n=await this.getAllPageInfos(t.pageCount);return t.pages=n,t}async getPageInfo(e){try{const t=He("page_info",{query:"page_info",page:e});r(1===t.length,"expected page_info result to return 1 result when specifying index.");const n=(new TextDecoder).decode(t[0]);return JSON.parse(n).pageInfo}catch(t){return a(`Dimensional information for page ${e} unavailable, page will not be displayed.`),{height:0,matrix:[0,0,0,0,0,0],pageLabel:"",reverseMatrix:[0,0,0,0,0,0],transformedBBox:[0,0,0,0],untransformedBBox:[0,0,0,0],width:0,pageIndex:e,rawPdfBoxes:{bleedBox:null,cropBox:null,mediaBox:null,trimBox:null}}}}async preflightRenderAnnotationText(e,t,n,o){const i=()=>"number"==typeof t&&"number"==typeof n?He("dynamic_font_loading/preflight_annotation",{text:e,annotation_id:t,page:n})[0]:(r(o,`annotation not set: ${o}`),He("dynamic_font_loading/preflight_annotation",{text:e,annotation_json:o})[0]);try{let e=i();e.preflight_result===ye&&(await this.addDynamicFonts(e.required_fonts),e=i()),"number"==typeof t&&(e.preflight_result===he?Re.add(t):Re.delete(t))}catch(n){a(`There was an error testing rendering for annotation ${t} of this text: ${e}: ${n.message}`)}}async addDynamicFonts(e){let t=[];try{t=e.filter((e=>!ze.has(e)));!function(e){r(Pe,De),e.forEach((e=>{Pe.FS.analyzePath(`${Le}/${e.name}`).exists||Pe.FS.writeFile(`${Le}/${e.name}`,new Uint8Array(e.data))}))}((await Promise.all(t.map((e=>fetch(`${Me}/${e}`).then((e=>e.arrayBuffer())))))).map(((e,n)=>({name:t[n],data:e})))),t.forEach((e=>{ze.add(e)}))}catch(e){t.forEach((e=>{ze.delete(e)})),a(`There was an error loading a font: ${e}`)}await He("dynamic_font_loading/notify_fonts_added",{added_fonts:t.map((e=>({remote_file_path:e,local_file_name:e})))})}async getAllPageInfos(e){const t=[];try{const n=He("page_info",{query:"page_info",page:"all"});r(n.length===e,"expected the same length of page info response to page count.");for(let e=0;e<n.length;e++){const r=(new TextDecoder).decode(n[e]),o=JSON.parse(r);t.push(o.pageInfo)}}catch(n){a("There was an error retrieving page information for all pages from core. Reverting to individual queries.");for(let n=0;n<e;n++)try{const e=He("page_info",{query:"page_info",page:n});r(1===e.length,"expected page_info result to return 1 result when specifying index.");const o=(new TextDecoder).decode(e[0]),i=JSON.parse(o);t.push(i.pageInfo)}catch(e){a(`Dimensional information for page ${n} unavailable, page will not be displayed.`);const r={height:0,matrix:[0,0,0,0,0,0],pageLabel:"",reverseMatrix:[0,0,0,0,0,0],transformedBBox:[0,0,0,0],untransformedBBox:[0,0,0,0],width:0,pageIndex:n,rawPdfBoxes:{bleedBox:null,cropBox:null,mediaBox:null,trimBox:null}};t.push(r)}}return t}async enablePDFJavaScriptSupport(){return tt()}async runPDFFormattingScripts(e,t){let n;r(Ce,"PDF Formatting Scripts can only run after JavaScript is enabled.");try{n=He("run_pdf_formatting_scripts",{form_fqns:e,only_if_no_ap_stream:t})}catch(e){throw new Error("An error occurred while executing the document level JavaScript formatting.\\n\\n"+e.message)}return n[0].changes||[]}async openDocumentAt(){throw new Error("Should never be called")}async getBookmarks(){const e=He("get_bookmarks");return r(1===e.length,"expected only one response for getBookmarks"),e[0].bookmarks||[]}async getFormJSON(){return He("get_form_json")[0]}async getFormValues(){return He("get_form_values")}async setFormValues(e){const t=ct();let n=!1;const r=[],o=t.map((t=>{const o=e.find((e=>e.name===t.formField.name));return o?o.value===t.value||Array.isArray(o.value)&&Array.isArray(t.value)&&o.value.every(((e,n)=>e===t.value[n]))?t:("string"==typeof o.value&&o.value.length>0?r.push({widgets:t.widgets,text:o.value}):t.widgets.forEach((e=>{"number"==typeof e.pdfObjectId&&Re.delete(e.pdfObjectId)})),n=!0,{...t,value:o.value}):t}));if(n){if(Me)for(let e=0;e<r.length;e++){const t=r[e].widgets;for(let n=0;n<t.length;n++)await this.preflightWidgetAnnotation(t[n],t[n].pdfObjectId,r[e].text)}ut(o)}}async setFormFieldValue(e){await this.setFormValues([e])}async applyOperations(e,t){const n=await et(e,t);nt(e,Ee),n()}async exportPDFWithOperations(e,t){const n=await et(e,t);let o;r(Pe,De);try{nt(e,Ae),o=Pe.FS.readFile(Ae).buffer}catch(e){throw new Error("Error applying operations: "+e.message)}return n(),o}async getSignaturesInfo(){try{return He("get_signatures",{certificate_check_time:"current_time"})[0]}catch(e){throw new Error(`Error getting signatures info: ${e.message}`)}}async getComments(){try{return He("get_comments")}catch(e){throw new Error(`Error getting comments: ${e.message}`)}}async applyComments(e){try{return He("apply_comments",{comments:e})}catch(e){throw new Error(`Error applying comments: ${e.message}`)}}async prepareSign(e,t,n,o,i,a){let s;const c=!1,u=!1;try{s=He("get_signatures",{certificate_check_time:"current_time"})[0];const e="not_signed"!==s.status;He("save_document",{file_path:Ae,flatten_annotations:!1,save_incrementally:e,apply_redactions:c,save_for_printing:u,format:"pdf",preserve_change_tracker_state:!1}),Ze("openDocument",Ae,JSON.stringify(Be))}catch(e){throw new Error(`Error saving document backup for invisible signing: ${e}`)}try{const e=!n&&"not_signed"!==s.status;He("save_document",{file_path:Ee,flatten_annotations:n,save_incrementally:e,apply_redactions:c,save_for_printing:u,format:"pdf",preserve_change_tracker_state:!1}),n&&(Ze("openDocument",Ee,JSON.stringify(Be)),Ce&&tt())}catch(e){throw new Error(`Error saving document for invisible signing: ${e}`)}let l;try{l=He("prepare_sign",{signer_data_source:{...e,field_name:o,position:i,appearance:a,type:"pspdfkit/signer-data-source"},signature_metadata:{...t,type:"pspdfkit/signature-metadata"}})}catch(e){throw new Error(`Error preparing document for signing: ${e}`)}r(Pe,De);let d=null;return e&&e?.signatureType!==ge||(d=Pe.FS.readFile(l[0].result.file_contents).buffer),Pe.FS.unlink(l[0].result.file_contents),{file:l[0].result.file,hash:l[0].result.hash,signatureFormFieldName:l[0].result.signature_form_fqn,dataToBeSigned:l[0].result.data_to_be_signed,fileContents:d}}async getTimestampRequest(e,t){try{const[n]=He("get_timestamp_http_request",{timestamp_authority_info:{url:t.url,authenticationInfo:{username:t.username,password:t.password}},data_to_timestamp:e});return{url:n.url,method:n.method,requestData:n.request_data,contentType:n.content_type,token:n.token,username:n.user,password:n.password}}catch(e){throw new Error(`Error getting timestamp request: ${e.message}`)}}async getRevocationRequests(e){try{return He("get_revocation_http_requests",{signing_certificates:e})}catch(e){throw new Error(`Error getting revocations requests: ${e.message}`)}}async setSignaturesLTV(e){try{const t=He("get_signatures",{certificate_check_time:"current_time",revocation_responses:e})[0];return He("save_document",{file_path:Ee,flatten_annotations:!1,save_incrementally:!0,apply_redactions:!1,save_for_printing:!1,format:"pdf",preserve_change_tracker_state:!1}),t}catch(e){throw new Error(`Error getting signatures info: ${e.message}`)}}async sign(e,t,n,r,o,i,a,s,c){let u;try{let l;const d=F(o).buffer,f=a?"pkcs7"===a:function(e){try{const t=new Uint8Array(e);return!!t.length&&48===t[0]}catch(e){throw new Error(`Error checking if the signature is in PKCS#7 format: ${e.message}`)}}(d);f||(l=He("create_signature",{signatureType:r,hash:n,signed_data:o,certificates:i,timestamp_response:s})),u=He("sign",{password:Be?.password,save_path:Ee,file_path:e,signature_form_fqn:t,pkcs7_container:l?.[0].result||o,revocation_responses:c??[]}),Ze("openDocument",Ee,JSON.stringify(Be)),Ce&&tt()}catch(e){throw new Error(`Error signing document: ${e}`)}return u[0].result}async restoreToOriginalState(){try{Ze("openDocument",Ae,JSON.stringify(Be)),Ce&&tt();const e="not_signed"!==He("get_signatures",{certificate_check_time:"current_time"})[0].status;He("save_document",{file_path:Ee,flatten_annotations:!1,save_incrementally:e,apply_redactions:!1,save_for_printing:!1,format:"pdf",preserve_change_tracker_state:!1}),Ze("openDocument",Ee,JSON.stringify(Be)),Ce&&tt()}catch(e){throw new Error(`Could not restore backup document: ${e}`)}}async evalFormValuesActions(e){const t=ct().reduce(((t,n)=>e.find((e=>{let{name:t}=e;return n.formField.name===t}))&&"pspdfkit/form-field/text"===n.formField.type?t.concat(n):t),[]);if(Me)for(let n=0;n<t.length;n++){const r=t[n].widgets;for(let n=0;n<r.length;n++)await this.preflightWidgetAnnotation(r[n],r[n].pdfObjectId,e.find((e=>t[n].formField.name===e.name))?.value)}const n=He("set_form_values_get_script_changes",{form_values:e});return r(1===n.length,"expected only one response for evalFormValuesActions"),this._processChanges(n[0].changes)}async readFormJSONObjects(){return ct()}async setFormJSONUpdateBatchMode(e){await st(e)}_processChanges(e){return e.map((e=>"pspdfkit/javascript/effects/importIcon"===e.object.type?{...e,object:{...e.object,id:Object.entries(this._pdfObjectIdsForIds).find((t=>{let[,n]=t;return n===e.object.annotationID}))?.[0]}}:e))}async evalScript(e,t,n){const o=He("run_pdf_javascript",{pdf_javascript_contents:e,pdf_javascript_trigger_event:t,pdf_javascript_form_fqn:n});return r(1===o.length,"expected only one response for evalScript"),this._processChanges(o[0].changes)}async closeDocument(){try{return Ne=[],Be={password:void 0,initialPageIndex:void 0},Ce=!1,rt=null,ot=!1,it=!1,Ze("closeDocument")}finally{this._pdfObjectIdsForIds={}}}async renderAnnotation(e,t,n,r,o,i,a){return lt({...e,pdfObjectId:this._getPdfObjectIdForObject(e)},t&&await B(t),n,r,o,i,a)}async renderPageAnnotations(e,t,n,r,o){return t.map(((t,i)=>lt({pdfObjectId:t,pageIndex:e},0,n[i],r[i],ft(o,{width:n[i],height:r[i]}))))}async renderDetachedAnnotation(e,t,n,o,i){r(Pe,De),!Me||"pspdfkit/stamp"!==e.type&&"pspdfkit/text"!==e.type||await this.preflightAnnotation(e,e.pdfObjectId);const a=t?await B(t):null,s=t?t.type:null;return He("render_annotation",{annotation_json:{...e,pdfObjectId:this._getPdfObjectIdForObject(e),pageIndex:0},format:i,bitmap_width:n,bitmap_height:o,content_type:s||void 0},a?a.buffer:void 0)[0]}async loadCertificates(e){if(He("load_certificates",{certificates:e}).length>0)throw new t("Internal error while loading certificates")}async getAttachment(e){let t,n;const r=await this.getEmbeddedFilesList(),o=r?.map((e=>e.id)),i=o?.includes(e);if(i)try{He("extract_embedded_file",{id:e,file_path:je}),t=Pe.FS.readFile(je).buffer}finally{Pe.FS.analyzePath(je)?.exists&&Pe.FS.unlink(je)}else{const r=He("get_annotation_attachment",{attachment_id:e});n=r[0].encoding,t=r[1]}return[t,n]}async annotationsForPageIndex(e){const t=Ze("annotationsForPageIndex",e);return["rollover","down"].forEach((e=>{t.apstream_variants?.[e]?.forEach((n=>{const r=t.annotations.find((e=>e.pdfObjectId===n));r?r[e]=!0:t.annotations.push({pdfObjectId:n,[e]:!0})}))})),t.annotations}async getTabOrder(e){return He("get_annotation_tab_order",{page:e})[0].order.map((e=>{const t=Object.entries(this._pdfObjectIdsForIds).find((t=>{let[n,r]=t;return r===e}));return{id:t?.[0]||String(e),pdfObjectId:e}}))}async setTabOrder(e,t){He("set_annotation_tab_order",{page:e,order:t.map((e=>this._getPdfObjectIdForObject(e)))})}async createAnnotation(e,t){r(Pe,De),r("number"==typeof e.pageIndex,"Annotation must have a pageIndex");const n=e.pdfObjectId,o=t?await B(t):null;"pspdfkit/widget"===e.type&&(st(!1),at());const i=He("add_annotation",{annotation:{...("pspdfkit/shape/ellipse"===e.type||"pspdfkit/shape/rectangle"===e.type)&&e.measurementScale?{...e,measurementBBox:e.bbox}:e,pdfObjectId:void 0}},o?o.buffer:void 0);"number"==typeof n&&"number"==typeof this._pdfObjectIdsForIds[n.toString()]&&delete this._pdfObjectIdsForIds[n.toString()];const a=i[0].annotation_id;return this._pdfObjectIdsForIds[e.id||a.toString()]=a,e&&this.canPreflightAnnotation(e)&&Me&&await this.preflightAnnotation(e,a),a}async updateAnnotation(e){const t={...e,pdfObjectId:this._getPdfObjectIdForObject(e)};if(r(t.id,"Annotation must have an ID"),r("number"==typeof t.pageIndex,"Annotation must have a pageIndex"),"pspdfkit/widget"===t.type){const e=ct(),n="number"==typeof t.pdfObjectId?t.pdfObjectId.toString():t.id;ut(e.map((e=>e.formField.annotationIds.includes(n)||e.formField.annotationIds.includes(String(t.pdfObjectId))?{...e,widgets:e.widgets.map((e=>e.id===n||String(e.pdfObjectId)===n?t:e))}:e)))}else e&&this.canPreflightAnnotation(e)&&Me&&await this.preflightAnnotation(e,t.pdfObjectId),He("update_annotation",{annotation:t})}async updateButtonIcon(e,t,n){He("update_annotation",{annotation:{...e,pdfObjectId:this._getPdfObjectIdForObject(e),contentType:n}},t)}async deleteAnnotation(e){if(e.APStreamCache&&await this.updateAnnotation(e),"pspdfkit/widget"===e.type)try{const t=e.id;ut(ct().map((n=>{if(n.formField.annotationIds.includes(t)||n.formField.annotationIds.includes(String(e.pdfObjectId))){let r,o=n.value;if(("pspdfkit/form-field/checkbox"===n.formField.type||"pspdfkit/form-field/radio"===n.formField.type)&&n.formField.options.length===n.formField.annotationIds.length){const i=n.formField.annotationIds.includes(t)?n.formField.annotationIds.indexOf(t):n.formField.annotationIds.indexOf(String(e.pdfObjectId));r=n.formField.options.filter(((e,t)=>t!==i)),r.some((e=>e.value===o))||(o="")}const i=n.widgets.filter((e=>t!==e.id)),a=n.formField.annotationIds.filter((n=>n!==t&&n!==String(e.pdfObjectId)));return i.length>0&&a.length>0?{...n,formField:{...n.formField,annotationIds:a,...r?{options:r}:null},widgets:i,value:o}:null}return n})).filter(Boolean))}catch(e){throw e}else try{He("remove_annotations",{annotation_ids:[this._getPdfObjectIdForObject(e)]})}catch(t){i(`Removing annotation failed for annotation: ${JSON.stringify(e)}`)}delete this._pdfObjectIdsForIds[e.id||e.pdfObjectId?.toString()],Re.delete(e.pdfObjectId)}async createFormField(e,t){const n=ct().concat([{type:"pspdfkit/form-field-with-widgets",formField:e,widgets:t}]),r=ot;ot&&st(!1),ut(n);const o=ct();r&&st(!0);const i=o.find((t=>t.formField.name===e.name));if(!i)throw new Error(`Error creating new form field in ${Ee}: created form field not found. ${JSON.stringify({type:"pspdfkit/form-field-with-widgets",formField:e,widgets:t})}`);const a=i.widgets.map((e=>(this._pdfObjectIdsForIds[e.id]=e.pdfObjectId,e.pdfObjectId)));return"number"==typeof e.pdfObjectId&&"number"==typeof this._pdfObjectIdsForIds[e.pdfObjectId.toString()]&&delete this._pdfObjectIdsForIds[e.pdfObjectId.toString()],this._pdfObjectIdsForIds[e.id||i.formField.pdfObjectId.toString()]=i.formField.pdfObjectId,a}async updateFormField(e,t){let n={...e,pdfObjectId:this._pdfObjectIdsForIds[e.id]||this._getPdfObjectIdForObject(e)};const r=ct(),o=r.find((t=>mt(t.formField,n)||t.formField.name===e.name));if(!o)throw new Error(`Error updating form field with name "${e.name}" in ${Ee}: form field not found. ${JSON.stringify(r)}`);const i=o.formField.name!==e.name;let a=null;if(i&&"pspdfkit/form-field/radio"===n.type){const o=e.name,i=r.find((e=>e.formField.name===o));i&&"pspdfkit/form-field/radio"===i.formField.type&&(n={...n,annotationIds:[...n.annotationIds,...i.formField.annotationIds],options:[...n.options,...i.formField.options]},t=[...t,...i.widgets],a=i)}ut(r.map((r=>{if(a&&a.formField.pdfObjectId===r.formField.pdfObjectId)return null;if(mt(r.formField,n)||r.formField.name===e.name){const o={type:"pspdfkit/form-field-with-widgets",formField:{...r.formField,...n},widgets:t.reduce(((e,t)=>[...e,{...r.widgets.find((e=>e.id===t.id||e.pdfObjectId===t.pdfObjectId)),...t}]),[]),...void 0!==r.value?{value:r.value}:null};return e.flags||delete o.formField.flags,o}return r})).filter(Boolean));const s=ct().find((t=>t.formField.name===e.name));if(!s)throw new Error(`Error updating form field "${e.name}" in ${Ee}: updated form field not found. ${JSON.stringify({type:"pspdfkit/form-field-with-widgets",formField:e,widgets:t})}`);s.formField.pdfObjectId!==n.pdfObjectId&&(this._pdfObjectIdsForIds[n.id]=s.formField.pdfObjectId),o?.widgets.forEach((e=>{t.some((t=>t.id===e.id))||delete this._pdfObjectIdsForIds[e.id]}))}async deleteFormField(e){const t=ct(),n=t.find((t=>t.formField.name===e.name));if(!n)return;ut(t.filter((t=>t.formField.name!==e.name)));const r=ct().find((t=>t.formField.name===e.name));if(r)throw new Error(`Error deleting form field with name "${e.name}" in ${Ee}: form field still present. ${JSON.stringify(r)}`);n.widgets.forEach((e=>{delete this._pdfObjectIdsForIds[e.id]}))}async deleteFormFieldValue(e){ut(ct().map((t=>t.formField.name===e?{formField:t.formField,widgets:t.widgets,type:t.type}:t)))}canPreflightAnnotation(e){return"pspdfkit/text"===e.type||"pspdfkit/stamp"===e.type}async preflightAnnotation(e,t){if(!this.canPreflightAnnotation(e))return;const n="pspdfkit/stamp"===e.type?e.title:e.text?.value;"number"!=typeof t||n?n&&await this.preflightRenderAnnotationText(n,t,e.pageIndex,{...e,pdfObjectId:this._getPdfObjectIdForObject(e),pageIndex:0}):Re.delete(t)}async preflightWidgetAnnotation(e,t,n){n||"number"!=typeof t?n&&await this.preflightRenderAnnotationText(n,t,e.pageIndex):Re.delete(t)}async createBookmark(e){try{He("append_bookmarks",{bookmarks:[e]})}catch(e){throw new Error(`Error creating new bookmark in ${Ee}: ${e.message}`)}}async updateBookmark(e){try{He("remove_bookmarks",{bookmarkIds:[e.id??e.pdfBookmarkId]}),He("append_bookmarks",{bookmarks:[e]})}catch(e){throw new Error(`Error updating bookmark in ${Ee}: ${e.message}`)}}async deleteBookmark(e){try{He("remove_bookmarks",{bookmarkIds:[e]})}catch(e){throw new Error(`Error deleting bookmark in ${Ee}: ${e.message}`)}}async getTextFromRects(e,t){return He("get_text_from_rects",{page:e,rects:t.map((e=>[e.left,e.top,e.width,e.height]))})[0].text}async search(e,t,n,r){return He("search",{query:e,start_index:t,limit:n,case_sensitive:r,search_type:arguments.length>4&&void 0!==arguments[4]?arguments[4]:U})}async parseXFDF(e,t){try{return Xe(He("convert_xfdf_to_instant_json",{ignore_page_rotation:t},e)[0])}catch(e){throw e}}async getEmbeddedFilesList(){return He("list_embedded_files")[0].embeddedFiles||null}async getMeasurementSnappingPoints(e){const t=He("get_snapping_points",{page:e});return t[0].snappingPoints?function(e){const t=[...e.endpoints,...e.intersections,...e.midpoints],n=[];for(let e=0;e<t.length;e+=2){const r=[t[e],t[e+1]];n.push(r)}return n.sort(((e,t)=>e[0]==t[0]?e[1]-t[1]:e[0]-t[0]))}(t[0].snappingPoints):null}async getSecondaryMeasurementUnit(){try{return He("get_secondary_measurement_unit")[0]||null}catch(e){throw new Error(`Error getting secondary measurement: ${e.message}`)}}async setSecondaryMeasurementUnit(e){try{He("set_secondary_measurement_unit",e?{unitTo:e?.unitTo,precision:e?.precision}:null)}catch(e){i("Error setting secondary measurement unit")}}async getMeasurementScales(){try{return He("get_measurement_content_formats")[0]||null}catch(e){throw new Error(`Error getting measurement scales: ${e.message}`)}}async removeMeasurementScale(e){try{He("remove_measurement_content_format",{measurementContentFormat:x(e)})}catch(t){throw new Error(`Error removing ${e.name} measurement scale: ${t.message}`)}}async addMeasurementScale(e){try{He("add_measurement_content_format",{measurementContentFormat:x(e)})}catch(t){throw new Error(`Error adding ${e.name} measurement scale: ${t.message}`)}}async getAnnotationsByScale(e){try{return He("get_annotations_for_measurement_content_format",{measurementContentFormat:x(e)})||null}catch(e){throw e}}async exportFile(e,t,n,o,i,a,s,c){let u,l={mimeType:"application/pdf",extension:"pdf"};try{const r=!1;u=He("save_document",{file_path:Ee,format:o,flatten_annotations:e,save_incrementally:t,apply_redactions:r,save_for_printing:n,remove_all_annotations:i,preserve_change_tracker_state:a,...s?{user_password:s.userPassword,owner_password:s.ownerPassword,document_permissions:pt(s.documentPermissions)}:null}),c&&(c.forEach((e=>{let{pageIndex:t,id:n,pdfObjectId:r}=e;He("electronic_signatures/flatten",{page:t,annotation_id:this._getPdfObjectIdForObject({id:n,pdfObjectId:r})})})),u=He("save_document",{file_path:Ae,format:o,flatten_annotations:e,save_incrementally:t,apply_redactions:r,save_for_printing:n,remove_all_annotations:i,preserve_change_tracker_state:a,...s?{user_password:s.userPassword,owner_password:s.ownerPassword,document_permissions:pt(s.documentPermissions)}:null}))}catch(e){throw new Error(`Error saving to ${Ae}: ${e.message}`)}const d=u[0];d.format&&(l={mimeType:d.format.mime_type,extension:d.format.extension}),r(Pe,De);const f=Pe.FS.readFile(c?Ae:Ee).buffer;return c&&(Ze("closeDocument"),Ze("openDocument",Ee,JSON.stringify(Be))),[f,l]}async importXFDF(e,t,n){Ne.push({source:e,keepCurrentAnnotations:t}),t||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.push("pspdfkit/widget"),He("remove_all_annotations",{exclude_annotation_types:e,exclude_annotation_ids:t})}(),He("import_xfdf",{ignore_page_rotation:n},e)}async exportXFDF(e){const t=He("export_xfdf",{ignore_page_rotation:e});return(new TextDecoder).decode(t[0])}async importInstantJSON(e){He("import_document_json",void 0,JSON.stringify(e))}async exportInstantJSON(e){const t=ot;ot&&st(!1);const n=He("export_document_json","number"==typeof e?{version:{annotations:e}}:void 0);return t&&st(!0),Xe(n[0])}async getDocumentOutline(){const e=He("get_outline");return r(1===e.length,"expected only one response for getDocumentOutline"),e[0].outline||[]}async setDocumentOutline(e){return He("set_outline",{outline:e})}async getPageGlyphs(e){return He("get_glyphs",{page:e})[0].glyphs||[]}async onKeystrokeEvent(e){const t=He("on_keystroke_event",{pdf_javascript_event:e});r(1===t.length,"expected only one response for onKeystrokeEvent");const n=t[0].changes||[];return r(n.length>0&&4===n[0].change_type,"expected onKeystrokeEvent to return at least one JavaScript Event change."),n}async version(){return Ze("PSPDFKitVersion").version}getImportedXFDF(){return Ne}async applyRedactions(){try{He("save_document",{file_path:Ee,format:"pdf",flatten_annotations:!1,save_incrementally:!1,apply_redactions:!0,save_for_printing:!1,remove_all_annotations:!1})}catch(e){throw new Error(`Error applying redactions and saving to ${Ee}: ${e.message}`)}}async clearAPStreamCache(){Ze("clearAPStreamCache")}async setComparisonDocument(e,t){this.comparisonDocuments[e]=t||(await this.exportFile(!1,!1,!1,"pdf",!1,!1))[0]}async openComparisonDocument(e){return r(this.comparisonDocuments[e]),e===this.lastOpenedComparisonDocument||e===M&&null===this.lastOpenedComparisonDocument&&this.persistedOpenDocument instanceof ArrayBuffer?null:(this.lastOpenedComparisonDocument=e,this.openDocument(this.comparisonDocuments[e],this.persistedOpenDocument===e?this.persistedOpenDocumentConfiguration:$e))}async documentCompareAndOpen(e){r(this.comparisonDocuments[M]&&this.comparisonDocuments[R],"One or both comparison input documents are missing.");const t=`${M}.pdf`,n=`${R}.pdf`;Pe.FS.writeFile(t,new Uint8Array(this.comparisonDocuments[M])),Pe.FS.writeFile(n,new Uint8Array(this.comparisonDocuments[R]));return He("comparison",{documentA:{strokeColor:e.documentA.strokeColor,pageIndex:e.documentA.pageIndex,filePath:t},documentB:{strokeColor:e.documentB.strokeColor,pageIndex:e.documentB.pageIndex,filePath:n},options:{...e.options,outputFilePath:"output.pdf"}}),await this.closeDocument(),this.comparisonDocuments[L]=Pe.FS.readFile("output.pdf").buffer,this.lastOpenedComparisonDocument=L,this.openDocument(this.comparisonDocuments[L],$e)}async persistOpenDocument(e){if(e)this.persistedOpenDocument=e;else try{this.persistedOpenDocument=(await this.exportFile(!1,!1,!1,"pdf",!1,!0))[0]}catch(e){throw new Error(`Error trying to persist a copy of the currently opened document: ${e.message}`)}this.persistedOpenDocumentConfiguration=Be}async cleanupDocumentComparison(){r(this.persistedOpenDocument,"No persisted previous document key or array buffer is available.");const e=this.persistedOpenDocument instanceof ArrayBuffer?this.persistedOpenDocument:this.comparisonDocuments[this.persistedOpenDocument];r(e,"No persisted previous array buffer is available.");try{await this.closeDocument()}catch(e){throw new Error(`Error trying to close the current document: ${e.message}`)}const t=this.openDocument(e,this.persistedOpenDocumentConfiguration);return this.persistedOpenDocument=null,this.persistedOpenDocumentConfiguration=$e,this.lastOpenedComparisonDocument=null,this.comparisonDocuments={},t}_getPdfObjectIdForObject(e){return"number"==typeof e.pdfObjectId?e.pdfObjectId:this._pdfObjectIdsForIds[e.id]}async getOCGs(){return He("ocg/get_ocgs")[0].ocgs}async getOCGVisibilityState(){return{visibleLayerIds:He("ocg/get_visibility_state")[0].state.visible}}async setOCGVisibilityState(e){return He("ocg/set_visibility_state",{state:{visible:e.visibleLayerIds}})}async dispatchCommand(e){let t=null;We&&performance.mark("dispatchCommand");try{if(t=await Pe.dispatchCommand(e),null===t)throw Error("dispatchCommand returned null");if(t.hasError())return{success:!1,message:t.getErrorMessage(),code:t.getErrorReason()};const n={success:!0,values:[]};for(let e=0;e<t.getRepliesCount();e++)t.hasJSONReply(e)&&n.values.push(JSON.parse(t.getJSONReply(e))),t.hasBinaryReply(e)&&n.values.push(t.getBinaryReply(e).slice(0));if(We){const t=JSON.parse(e);Ve.push({type:qe.Daemon,name:t.type,args:t,time:performance.measure("dispatchCommand","dispatchCommand").duration})}return n}finally{t&&t.delete()}}async getCoreCallRecording(){return Ve}async enableCoreCallRecording(e){We=e}openFile(e,t){const n=Pe.FS.open(e,t);return this.files.set(this.fileDescriptor,n),Promise.resolve(this.fileDescriptor++)}closeFile(e){const t=this.files.get(e);return t&&(Pe.FS.close(t),this.files.delete(e)),Promise.resolve()}writeData(e,t,n){const r=this.files.get(e);if(!r)throw Error(`Trying to write to a file with descriptor ${e}, but the file is not open.`);return Pe.FS.write(r,new Uint8Array(t),0,t.byteLength,n),Promise.resolve()}recycle(){}destroy(){}},ht=self;ht.global=ht,ht.module={},ht.onmessage=async e=>{let t,n,{data:r}=e;try{const e=await gt[r.action](...r.args);if(t={id:r.id,result:e},Array.isArray(e)){const t=e.filter((e=>e instanceof ArrayBuffer));t.length>0&&(n=t)}e instanceof ArrayBuffer&&(n=[e])}catch(e){const o=[...r.args].filter((e=>e instanceof ArrayBuffer));o.length>0&&(n=o),t={id:r.id,error:e.message||e.toString(),callArgs:r.args}}ht.postMessage(t,n)}})()})();',"Worker",void 0,void 0)}},55512:e=>{e.exports=function(e,t,n,r){var o=self||window;try{try{var i;try{i=new o.Blob([e])}catch(t){(i=new(o.BlobBuilder||o.WebKitBlobBuilder||o.MozBlobBuilder||o.MSBlobBuilder)).append(e),i=i.getBlob()}var a=o.URL||o.webkitURL,s=a.createObjectURL(i),c=new o[t](s,n);return a.revokeObjectURL(s),c}catch(r){return new o[t]("data:application/javascript,".concat(encodeURIComponent(e)),n)}}catch(e){if(!r)throw Error("Inline worker is not supported");return new o[t](r,n)}}}}]);