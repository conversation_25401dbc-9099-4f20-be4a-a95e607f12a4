/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
"use strict";(globalThis.webpackChunkNutrientViewer=globalThis.webpackChunkNutrientViewer||[]).push([[351],{53010:e=>{e.exports=JSON.parse('{"thumbnails":"Thumbnails","pageXofY":"Page {arg0} of {arg1}","XofY":"{arg0} of {arg1}","prevPage":"Previous Page","nextPage":"Next Page","goToPage":"Go to Page","gotoPageX":"Go to Page {arg0}","pageX":"Page {arg0}","pageLayout":"Page Layout","pageMode":"Page Mode","pageModeSingle":"Single","pageModeDouble":"Double","pageModeAutomatic":"Automatic","pageTransition":"Page Transition","pageTransitionContinuous":"Continuous","pageTransitionJump":"Jump","pageRotation":"Page Rotation","pageRotationLeft":"Rotate Left","pageRotationRight":"Rotate Right","zoomIn":"Zoom In","zoomOut":"Zoom Out","marqueeZoom":"Marquee Zoom","panMode":"Pan Mode","fitPage":"Fit Page","fitWidth":"Fit Width","annotations":"Annotations","noAnnotations":"No Annotations","bookmark":"Bookmark","bookmarks":"Bookmarks","noBookmarks":"No Bookmarks","newBookmark":"New Bookmark","addBookmark":"Add Bookmark","removeBookmark":"Remove Bookmark","loadingBookmarks":"Loading Bookmarks","deleteBookmarkConfirmMessage":"Are you sure you want to delete this bookmark?","deleteBookmarkConfirmAccessibilityLabel":"Confirm bookmark deletion","annotation":"Annotation","noteAnnotation":"Note","textAnnotation":"Text","inkAnnotation":"Drawing","highlightAnnotation":"Text Highlight","underlineAnnotation":"Underline","squiggleAnnotation":"Squiggle","strikeOutAnnotation":"Strikethrough","print":"Print","printPrepare":"Preparing document for printing…","searchDocument":"Search Document","searchPreviousMatch":"Previous","searchNextMatch":"Next","searchResultOf":"{arg0} of {arg1}","accessibilityLabelDropdownGroupToggle":"{arg0} tools, toggle menu","save":"Save","edit":"Edit","delete":"Delete","close":"Close","cancel":"Cancel","ok":"OK","done":"Done","clear":"Clear","date":"Date","time":"Time","name":"Name","color":"Colour","black":"Black","white":"White","blue":"Blue","red":"Red","green":"Green","orange":"Orange","lightOrange":"Light Orange","yellow":"Yellow","lightYellow":"Light Yellow","lightBlue":"Light Blue","lightRed":"Light Red","lightGreen":"Light Green","fuchsia":"Fuchsia","purple":"Purple","pink":"Pink","mauve":"Mauve","lightGrey":"Light Grey","grey":"Gray","darkGrey":"Dark Grey","noColor":"None","transparent":"Transparent","darkBlue":"Dark Blue","opacity":"Opacity","thickness":"Thickness","size":"Size","numberInPt":"{arg0} pt","font":"Font","fonts":"Fonts","allFonts":"All Fonts","alignment":"Alignment","alignmentLeft":"Left","alignmentRight":"Right","alignmentCenter":"Centre","verticalAlignment":"Vertical Alignment","horizontalAlignment":"Horizontal Alignment","top":"Top","bottom":"Bottom","deleteAnnotationConfirmMessage":"Are you sure you want to delete this annotation?","deleteAnnotationConfirmAccessibilityLabel":"Confirm annotation deletion","fontFamilyUnsupported":"{arg0} (not supported)","sign":"Sign","signed":"Signed","signatures":"Signatures","addSignature":"Add Signature","clearSignature":"Clear Signature","storeSignature":"Store Signature","pleaseSignHere":"Please sign here","signing":"Signing…","password":"Password","unlock":"Unlock","passwordRequired":"Password Required","unlockThisDocument":"You have to unlock this document in order to view it. Please enter the password in the field below.","incorrectPassword":"The password you entered isn’t correct. Please try again.","blendMode":"Blend Mode","normal":"Normal","multiply":"Multiply","screenBlend":"Screen","overlay":"Overlay","darken":"Darken","lighten":"Lighten","colorDodge":"Colour Dodge","colorBurn":"Colour Burn","hardLight":"Hard Light","softLight":"Soft Light","difference":"Difference","exclusion":"Exclusion","multiple":"Multiple","linecaps-dasharray":"Line Style","dasharray":"Line Style","startLineCap":"Line Start","strokeDashArray":"Line Style","endLineCap":"Line End","lineAnnotation":"Line","rectangleAnnotation":"Rectangle","ellipseAnnotation":"Ellipse","polygonAnnotation":"Polygon","polylineAnnotation":"Polyline","solid":"Solid","narrowDots":"Narrow Dots","wideDots":"Wide Dots","narrowDashes":"Narrow Dashes","wideDashes":"Wide Dashes","none":"None","square":"Square","circle":"Circle","diamond":"Diamond","openArrow":"Open Arrow","closedArrow":"Closed Arrow","butt":"Butt","reverseOpenArrow":"Reverse Open Arrow","reverseClosedArrow":"Reverse Closed Arrow","slash":"Slash","fillColor":"Fill Colour","cloudy":"Cloudy","arrow":"Arrow","filePath":"File Path","unsupportedImageFormat":"Unsupported type for image annotation: {arg0}. Please use a JPEG or a PNG.","noOutline":"No Outline","outline":"Outline","imageAnnotation":"Image","selectImage":"Select Image","stampAnnotation":"Stamp","highlighter":"Freeform Highlight","textHighlighter":"Text Highlighter","pen":"Drawing","eraser":"Eraser","export":"Export","useAnExistingStampDesign":"Use an existing stamp design","createStamp":"Create Stamp","stampText":"Stamp Text","chooseColor":"Choose Colour","rejected":"Rejected","accepted":"Accepted","approved":"Approved","notApproved":"Not Approved","draft":"Draft","final":"Final","completed":"Completed","confidential":"Confidential","forPublicRelease":"For Public Release","notForPublicRelease":"Not For Public Release","forComment":"For Comment","void":"Void","preliminaryResults":"Preliminary Results","informationOnly":"Information Only","initialHere":"Initial Here","signHere":"Sign Here","witness":"Witness","asIs":"As Is","departmental":"Departmental","experimental":"Experimental","expired":"Expired","sold":"Sold","topSecret":"Top Secret","revised":"Revised","custom":"Custom","customStamp":"Custom Stamp","icon":"Icon","iconRightPointer":"Right Pointer","iconRightArrow":"Right Arrow","iconCheck":"Checkmark","iconCircle":"Ellipse","iconCross":"Cross","iconInsert":"Insert Text","iconNewParagraph":"New Paragraph","iconNote":"Text Note","iconComment":"Comment","iconParagraph":"Paragraph","iconHelp":"Help","iconStar":"Star","iconKey":"Key","documentEditor":"Document Editor","newPage":"New Page","removePage":"Delete Pages","duplicatePage":"Duplicate","rotatePageLeft":"Rotate Left","rotatePageRight":"Rotate Right","moveBefore":"Move Before","moveAfter":"Move After","selectNone":"Select None","selectAll":"Select All","saveAs":"Save As…","mergeDocument":"Import Document","undo":"Undo","redo":"Redo","openMoveDialog":"Move","move":"Move","extractPages":"Extract Pages","instantModifiedWarning":"The document was modified and is now in read-only mode. Reload the page to fix this.","documentMergedHere":"Document will be merged here","digitalSignaturesAllValid":"The document has been digitally signed and all signatures are valid.","digitalSignaturesDocModified":"The document has been digitally signed, but it has been modified since it was signed.","digitalSignaturesSignatureWarning":"The document has been digitally signed, but at least one signature has problems.","digitalSignaturesSignatureWarningDocModified":"The document has been digitally signed, but it has been modified since it was signed and at least one signature has problems.","digitalSignaturesSignatureError":"The document has been digitally signed, but at least one signature is invalid.","digitalSignaturesSignatureErrorDocModified":"The document has been digitally signed, but it has been modified since it was signed and at least one signature is invalid.","signingInProgress":"Signing in progress","signingModalDesc":"Indicates that the current document is being signed","discardChanges":"Discard Changes","commentEditorLabel":"Add your comment…","reply":"Reply","comment":"Comment","comments":"Comments","showMore":"Show more","showLess":"Show less","deleteComment":"Delete Comment","deleteCommentConfirmMessage":"Are you sure you want to delete this comment?","deleteCommentConfirmAccessibilityLabel":"Confirm comment deletion","editContent":"Edit Content","commentOptions":"Comment Options","areaRedaction":"Area Redaction","textRedaction":"Text Redaction","redactionAnnotation":"Redaction","applyingRedactions":"Applying redactions","overlayTextPlaceholder":"Insert overlay text","outlineColor":"Outline Colour","overlayText":"Overlay Text","repeatText":"Repeat Text","preview":"Preview","applyRedactions":"Apply Redactions","markupAnnotationToolbar":"Markup annotation toolbar","documentViewport":"Document viewport","redactionInProgress":"Redaction in progress","redactionModalDesc":"Indicates that the current document is being redacted","commentAction":"Comment","printProgressModalDesc":"Indicates that a document is being prepared for printing","printProgressModal":"Printing in progress","documentEditorDesc":"Make changes to the current document","reloadDocumentDialog":"Confirm document reload","reloadDocumentDialogDesc":"Dialogue prompting the user to confirm reloading the document.","signatureDialog":"Signature","signatureDialogDesc":"This dialogue lets you select an ink signature to insert into the document. If you don\'t have stored signatures, you can create one using the canvas view.","stampAnnotationTemplatesDialog":"Stamp Annotation Templates","stampAnnotationTemplatesDialogDesc":"This dialogue lets you select a stamp annotation to insert into the document or create a custom stamp annotation with your own text.","selectedAnnotation":"Selected {arg0}","commentThread":"Comment thread","selectedAnnotationWithText":"Selected {arg0} with {arg1} as content","signature":"Signature","ElectronicSignatures_SignHereTypeHint":"Type Your Signature Above","ElectronicSignatures_SignHereDrawHint":"Sign Here","selectDragImage":"Select or Drag Image","replaceImage":"Replace Image","draw":"Draw","image":"Image","type":"Type","saveSignature":"Save Signature","loading":"Loading","selectedItem":"{arg0}, selected.","annotationDeleted":"Annotation deleted.","newAnnotationCreated":"New {arg0} annotation created.","bookmarkCreated":"Created bookmark.","bookmarkEdited":"Edited bookmark.","bookmarkDeleted":"Deleted bookmark.","cancelledEditingBookmark":"Cancelled bookmark editing.","selectAFileForImage":"Select a file for the new image annotation.","deleteAnnotationConfirmAccessibilityDescription":"Dialogue allowing you to confirm or cancel deleting the annotation.","deleteBookmarkConfirmAccessibilityDescription":"Dialogue allowing you to confirm or cancel deleting the bookmark.","deleteCommentConfirmAccessibilityDescription":"Dialogue allowing you to confirm or cancel deleting the comment.","resize":"Resize","resizeHandleTop":"Top","resizeHandleBottom":"Bottom","resizeHandleRight":"Right","resizeHandleLeft":"Left","cropCurrentPage":"Crop Current Page","cropCurrent":"Crop Current","cropAllPages":"Crop All Pages","cropAll":"Crop All","documentCrop":"Document Crop","Comparison_alignButtonTouch":"Align","Comparison_selectPoint":"Select Point","Comparison_documentOldTouch":"Old","Comparison_documentNewTouch":"New","Comparison_result":"Comparison","UserHint_description":"Select three points on both documents for manual alignment. For best results, choose points near the corners of the documents, ensuring the points are in the same order on both documents.","UserHint_dismissMessage":"Dismiss","Comparison_alignButton":"Align Documents","documentComparison":"Document Comparison","Comparison_documentOld":"Old Document","Comparison_documentNew":"New Document","Comparison_resetButton":"Reset","UserHint_Select":"Selecting Points","numberValidationBadFormat":"The value entered doesn’t match the format of the field [{arg0}]","dateValidationBadFormat":"Invalid date/time: Please ensure that the date/time exists. Field [{arg0}] should match format {arg1}","insertAfterPage":"Insert after page","docEditorMoveBeginningHint":"Type “0” to move the selected page(s) to the beginning of the document.","cloudyRectangleAnnotation":"Cloudy Rectangle","dashedRectangleAnnotation":"Dashed Rectangle","cloudyEllipseAnnotation":"Cloudy Ellipse","dashedEllipseAnnotation":"Dashed Ellipse","cloudyPolygonAnnotation":"Cloudy Polygon","dashedPolygonAnnotation":"Dashed Polygon","cloudAnnotation":"Cloud","rotateCounterclockwise":"Rotate anticlockwise","rotateClockwise":"Rotate clockwise","enterDescriptionHere":"Enter description here","addOption":"Add option","formDesignerPopoverTitle":"{formFieldType} Properties","formFieldNameExists":"A form field named {formFieldName} already exists. Please choose a different name.","styleSectionLabel":"{formFieldType} Style","formFieldName":"Form Field Name","defaultValue":"Default Value","multiLine":"Multiline","radioButtonFormFieldNameWarning":"To group the radio buttons, make sure they have the same form field names.","advanced":"Advanced","creatorName":"Creator Name","note":"Note","customData":"Custom Data","required":"Required","readOnly":"Read Only","createdAt":"Created At","updatedAt":"Updated At","customDataErrorMessage":"Must be a plain JSON-serializable object","borderColor":"Border Colour","borderWidth":"Border Width","borderStyle":"Border Style","solidBorder":"Solid","dashed":"Dashed","beveled":"Beveled","inset":"Inset","underline":"Underlined","textStyle":"Text Style","fontSize":"Font Size","fontColor":"Font Colour","button":"Button Field","textField":"Text Field","radioField":"Radio Field","checkboxField":"Checkbox Field","comboBoxField":"Combo Box Field","listBoxField":"List Box Field","signatureField":"Signature Field","formDesigner":"Form Creator","buttonText":"Button Text","notAvailable":"N/A","label":"Label","value":"Value","cannotEditOnceCreated":"Cannot be edited once created.","formFieldNameNotEmpty":"Form field name cannot be left empty.","mediaAnnotation":"Media Annotation","mediaFormatNotSupported":"This browser doesn’t support embedded video or audio.","distanceMeasurement":"Distance","perimeterMeasurement":"Perimeter","polygonAreaMeasurement":"Polygon Area","rectangularAreaMeasurement":"Rectangle Area","ellipseAreaMeasurement":"Ellipse Area","distanceMeasurementSettings":"Distance Measurement Settings","perimeterMeasurementSettings":"Perimeter Measurement Settings","polygonAreaMeasurementSettings":"Polygon Area Measurement Settings","rectangularAreaMeasurementSettings":"Rectangle Area Measurement Settings","ellipseAreaMeasurementSettings":"Ellipse Area Measurement Settings","measurementScale":"Scale","measurementCalibrateLength":"Calibrate Length","measurementPrecision":"Precision","measurementSnapping":"Snapping","formCreator":"Form Creator","group":"Group","ungroup":"Ungroup","bold":"Bold","italic":"Italic","addLink":"Add Link","removeLink":"Remove Link","editLink":"Edit Link","anonymous":"Anonymous","saveAndClose":"Save & Close","ceToggleFontMismatchTooltip":"Toggle font mismatch tooltip","ceFontMismatch":"The {arg0} font isn’t available or can’t be used to edit content in this document. Added or changed content will revert to a default font.","multiAnnotationsSelection":"Select Multiple Annotations","linkSettingsPopoverTitle":"Link Settings","linkTo":"Link To","uriLink":"Website","pageLink":"Page","invalidPageNumber":"Enter a valid page number.","invalidPageLink":"Enter a valid page link.","linkAnnotation":"Link","targetPageLink":"Page Number","rotation":"Rotation","unlockDocumentDescription":"You have to unlock this document to view it. Please enter the password in the field below.","commentDialogClosed":"Open comment thread started by {arg0}: \\\\“{arg1}\\\\”","moreComments":"More comments","setScale":"Set Scale","useCalibrationTool":"Use Calibration Tool","calibrateScale":"Calibrate Scale","unit":"Unit","scaleAnnotationsDeleteWarning":"Deleting the scale will also delete measurements using the scale.","halves":"Half Inches","quarters":"Quarter Inches","eighths":"Eighths of an Inch","sixteenths":"Sixteenths of an Inch","addTextBox":"Add Text Box","deleteTextBlockConfirmMessage":"Are you sure you want to delete this text box?","deleteTextBlockConfirmAccessibilityLabel":"Confirm text box deletion","deleteTextBlockConfirmAccessibilityDescription":"Dialogue allowing the user to confirm or cancel deleting the text box","noSignatures":"No Signatures","scaleName":"Scale Name","calibrate":"Calibrate","scaleAlreadyAddedWarning":"A scale with these settings has already been defined. Please choose a different scale.","displaySecondaryUnit":"Display Secondary Unit","editAddNewScale":"Edit or add new scale","addScale":"Add Scale","calloutAnnotation":"Callout","layers":"Layers","noLayers":"No Layers","contentEditor":"Content Editor","secondaryUnits":"Secondary Units","measurement":"Measurements","reloadDocument":"Reload Document","backgroundColor":"Background Colour","help":"Help","cut":"Cut","copy":"Copy","paste":"Paste","openLink":"Open Link","linkToPage":"Link to Page","ContentEditing_DownloadRequiredTitle":"Download Required","ContentEditing_DownloadRequiredMessage":"To enable content editing directly in your browser, your document needs to be downloaded.","ContentEditing_DownloadRequiredButton":"Download Document","ContentEditing_CannotSaveChangesTitle":"Cannot Save Changes","ContentEditing_CannotSaveChangesMessage":"This file was edited by another user at the same time and their changes have already been uploaded. Your edits cannot be saved. Review your edits and try again if necessary, or exit without saving any changes.","ContentEditing_CannotSaveChangesButton":"Discard","ContentEditing_CannotSaveChangesReviewButton":"Review","ContentEditing_DownloadingDocument":"Preparing document for editing…","ContentEditing_Saving":"Saving the document…","documentDownloading":"Downloading document. Once it’s completely downloaded, you’ll be able to fully interact with it.","attachments":"Attachments","linesCount":"{arg0, plural,\\none {{arg0} Line}\\nother {{arg0} Lines}\\n}","annotationsCount":"{arg0, plural,\\n=0 {No Annotations}\\none {{arg0} Annotation}\\nother {{arg0} Annotations}\\n}","pagesSelected":"{arg0, plural,\\none {{arg0} Page Selected}\\nother {{arg0} Pages Selected}\\n}","commentsCount":"{arg0, plural,\\none {{arg0} comment}\\nother {{arg0} comments}\\n}","deleteNComments":"{arg0, plural,\\none {{arg0} Comment}\\nother {{arg0} Comments}\\n}","nMoreComments":"{arg0, plural,\\none {{arg0} more comment}\\nother {{arg0} more comments}\\n}"}')}}]);