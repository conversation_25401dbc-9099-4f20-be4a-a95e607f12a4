/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
(globalThis.webpackChunkNutrientViewer=globalThis.webpackChunkNutrientViewer||[]).push([[750],{1033:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>H});var n=r(67136),s=r(49568),i=r(85409),o=r(45646),a=r(63097),l=r(99589),c=r(55994),h=r(34551),u=r(47159),d=r(5293),p=r(97881),m=r(14631),f=r(54781),E=r(85553);class g extends(s.mS({authPayload:null,serverUrl:null,hostedBaseUrl:null,documentId:null,layerName:null,sourcePdfSha256:null,backendPermissions:null,documentURL:null,imageToken:null,instantSettings:null,token:null,features:(0,s.B8)(),signatureFeatureAvailability:E.g.NONE,isFormsEnabled:!0,minSearchQueryLength:1,documentHandle:null,isDocumentHandleOutdated:!1,digitalSignatures:null,defaultGroup:void 0,hasCollaborationPermissions:!1,forceLegacySignaturesFeature:!1,disableWebAssemblyStreaming:!1,overrideMemoryLimit:null,baseCoreUrl:null,useDeprecatedRestProvider:!1})){}var w=r(14511),y=r(15168),$=r(28650),R=r(85410),v=r(9627);const S="The image can not be rendered because of an unknown error.";class I{constructor(e){let{identifier:t,url:r,token:n,payload:s,doNotRequestWebP:i=!1}=e;this.identifier=t,this.url=r,this.token=n,this.payload=s,this.doNotRequestWebP=i}abort(){this.httpRequest?.abort()}request(){return new Promise(((e,t)=>{const r=new XMLHttpRequest;this.httpRequest=r,r.open(this.payload?"POST":"GET",this.url,!0),r.setRequestHeader("X-PSPDFKit-Image-Token",this.token),r.setRequestHeader("PSPDFKit-Platform","web"),r.setRequestHeader("PSPDFKit-Version",(0,R._q)()),y.xd&&!this.doNotRequestWebP&&r.setRequestHeader("Accept","image/webp,*/*"),r.responseType="blob",r.onreadystatechange=(async()=>{if(4!==r.readyState)return;if(r.response&&r.response.type.startsWith("application/json")){const n=new FileReader;return n.onload=r=>{const n=JSON.parse(r.target?.result);n.attachments_not_found?e({attachmentsNotFound:n.attachments_not_found}):n.error?"initialization_error"===n.error?e(null):t(new i.uE(`The server could not render the requested image (${n.error})`)):t(new i.uE(S))},n.onerror=()=>t(new i.uE(S)),void n.readAsText(r.response)}if(!(0,w.Nk)(r.status))return void t(new i.uE(S));const n=r.response,s=URL.createObjectURL(n),o=new Image;o.onerror=()=>t(new i.uE(S)),o.src=s;const a=o.decode();try{await a}catch(e){if(!v.H8)throw new i.uE(`The image could not be decoded: ${e.message}`);await new Promise((e=>setTimeout(e,200)))}e(new $.A(o,(()=>URL.revokeObjectURL(s))))}).bind(this),r.send(this.payload)}))}}var P=r(37361),A=r(89574),L=r(41477),_=r(51946),b=r(30026);async function N(e,t,r){const n=await fetch(`${e}/auth`,{method:"POST",headers:{"Content-Type":"application/json","PSPDFKit-Platform":"web","PSPDFKit-Version":(0,R._q)()},body:JSON.stringify({jwt:t.jwt,password:r}),credentials:"include"});return n.ok?n.json():n.text().then((e=>{throw"INVALID_PASSWORD"===e?new i.uE(e):new i.uE(`An error occurred while connecting to Nutrient Document Engine: ${e||n.statusText}`)}))}var T=r(25888),O=r(4824),F=r(12219),C=r(37094),D=r(72262),x=r(40927),U=r(9939),j=r(83720),k=r(64337),M=r(89055),G=r(20546),V=r(37506),B=r(96419),X=r(77270),q=r(20873);class H extends D.K{constructor(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;super(),t=this,(0,n.A)(this,"_password",null),(0,n.A)(this,"type","SERVER"),(0,n.A)(this,"_requestRenderAnnotation",((e,r,n,s,o,l)=>{const c=`${this._state.documentURL}/render_annotation`,h=`render-annotation-${l?(0,a.Z0)():e.id}`,u=JSON.stringify({data:(0,p.eq)(e),width:s,height:o,detached:l||void 0,formFieldValue:r?(0,p.cA)(r):void 0});let d=!1,m=[];const f=(0,X.yl)();let E;if((0,A.cu)(e)){if(E=(0,A.sS)(e),this.isVisuallyIdenticalAnnotationCached({annotation:e,providedVisualAnnotationIndex:E,width:s,height:o}))return this.cachedRenderedAnnotationDiscardablePromise(E,f);this._addCachedRenderedAnnotation(E,{width:s,height:o,noZoom:e.noZoom,APStreamPromise:f.promise})}const g=function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const a=new FormData;a.append("render",u),r.length>0&&"imageAttachmentId"in e&&e.imageAttachmentId&&n&&a.append(e.imageAttachmentId,n);const l=new I({identifier:h,url:c,token:t._state.imageToken,payload:a,doNotRequestWebP:s>P.HI||o>P.HI}),p=t._requestQueue.enqueue(l,!1);p.promise.then((r=>{if(!d)if(r?.attachmentsNotFound)g(r.attachmentsNotFound);else{if(!r?.attachmentsNotFound)return(0,A.cu)(e)&&t._makeEnqueuedRelease(r,E),f.resolve(r),r;f.reject(new i.uE("Attachment could not be found."))}})).catch((e=>{const r=t._cachedRenderedAnnotations.get(E);r&&r.timeout&&clearTimeout(r.timeout),t._cachedRenderedAnnotations.delete(E),d?f.resolve(void 0):f.reject(e)})),m.push(p)};return g(),{promise:f.promise,cancel:()=>{d=!0,m.forEach((e=>{e.cancel()}))}}})),(0,n.A)(this,"_requestRenderAnnotations",((e,t,r,n,s)=>{const i=`${this._state.documentURL}/render_annotations`,o=JSON.stringify({annotations:t.map(((t,s)=>({pageIndex:e,pdfObjectId:t,width:r[s],height:n[s]}))),formFieldValues:s});let a,l,c=!1;const h=new Promise(((e,t)=>{a=e,l=t}));return this._fetch(i,{method:"post",body:o,credentials:"include",headers:{"X-PSPDFKit-Image-Token":this._state.imageToken,"Content-Type":"application/json",Accept:"multipart/form-data"}}).then((e=>e.formData())).then((e=>{c||a(Array.from(e.values()))})).catch((e=>{c||l(e)})),{promise:h,cancel:()=>{c=!0}}})),(0,n.A)(this,"handleDocumentHandleConflict",(()=>{this._state=this._state.set("isDocumentHandleOutdated",!0),this.cancelRequests(),this._destroyProvider()}));let s=null;if("session"in e){if("string"!=typeof e.session)throw new i.uE("The supplied session token is invalid. Must be a string.");s={jwt:e.session},e.instant=!("instant"in e)||"boolean"!=typeof e.instant&&!(0,i.Qd)(e.instant)||e.instant}else"authPayload"in e&&(s=e.authPayload);if("object"!=typeof s)throw new i.uE("authPayload must be an object that contains the `jwt`. For example: `authPayload: { jwt: 'xxx.xxx.xxx'}`");const l=s?.accessToken;let c=null,h=null,u="documentId"in e?e.documentId:null,d=null;if(l)d=e.hostedBaseUrl||"https://api.pspdfkit.com/",(0,w.Me)(d),(0,m.P1)(l);else{if(!s||"object"!=typeof s||!("jwt"in s)||"string"!=typeof s.jwt)throw new i.uE("authPayload must be an object that contains the `jwt`. For example: `authPayload: { jwt: 'xxx.xxx.xxx'}`");const{serverUrl:t,documentId:n}=(0,m.Gm)(s.jwt);if(c=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:window;const n=e.serverUrl||t||(0,R.$_)(r.document);if("/"!==n.substr(-1))throw new i.uE("`serverUrl` must have a slash at the end (e.g. `https://pspdfkit.example.com/`).");if(!e.serverUrl&&n===(0,w.ln)(r.location.origin))throw new i.uE('PSPDFKit automatically infers the URL of Nutrient Document Engine from the current `<script>` tag.\nIn the current case, this URL is set to the same as the current browser\'s location.\nThis can happen when you bundle nutrient-viewer.js with your custom JavaScript for example.\n\nTo make sure everything works as expected, please set the `serverUrl` to the URL of Nutrient Document Engine:\n\nPSPDFKit.load({\n  serverUrl: "https://pspdfkit-server.example.com/",\n  ...,\n});');return n}(e,t,r),u=u||n,"string"!=typeof u)throw new i.uE("`documentId` must be of type string.");h=`${c}i/d/${u}`}!function(e){let t="";if("boolean"!=typeof e&&((0,i.Qd)(e)?(e.hasOwnProperty("clientsPresenceEnabled")&&"boolean"!=typeof e.clientsPresenceEnabled&&(t+="`clientsPresenceEnabled` in instance settings is not valid. Must be `true` or `false`.\n"),e.hasOwnProperty("listenToServerChangesEnabled")&&"boolean"!=typeof e.listenToServerChangesEnabled&&(t+="`listenToServerChangesEnabled` in instance settings is not valid. Must be `true` or `false`.\n")):t="`instant` flag must either be set to `true` or `false`\n",t))throw new i.uE(`${t}\nFor more information about PSPDFKit Instant please visit:\nhttps://pspdfkit.com/guides/web/current/instant/overview/`)}(e.instant);let f=null;if(e.instant)if((0,i.Qd)(e.instant)){const t=e.instant;f={clientsPresenceEnabled:!1!==t.clientsPresenceEnabled,listenToServerChangesEnabled:!1!==t.listenToServerChangesEnabled}}else f=O.S;this._requestQueue=new o.L(P.LB);const E=!!e.electronicSignatures&&Boolean(e.electronicSignatures.forceLegacySignaturesFeature),y=e.baseUrl||(0,R.$_)(window.document),$=e.baseCoreUrl||y;this._state=new g({serverUrl:c,hostedBaseUrl:d,documentId:u,instantSettings:f,documentURL:h,authPayload:s,isFormsEnabled:!e.disableForms,forceLegacySignaturesFeature:E,disableWebAssemblyStreaming:e.disableWebAssemblyStreaming,overrideMemoryLimit:e.overrideMemoryLimit,baseCoreUrl:$,useDeprecatedRestProvider:e.useDeprecatedRestProvider}),"trustedCAsCallback"in e&&e.trustedCAsCallback&&(0,i.R8)("PSPDFKit.Configuration#trustedCAsCallback is only used on Standalone deployments. On a Server-Backed deployment, please follow the instructions at https://pspdfkit.com/guides/web")}isUsingInstantProvider(){return!this._state.useDeprecatedRestProvider}hasClientsPresence(){const e=this._state.instantSettings;return null!=e&&!1!==e.clientsPresenceEnabled}async load(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};await this.tryAuthenticateHostedViewer();const{imageToken:t,token:r,permissions:n,features:o,signatureFeatureAvailability:a,hasPassword:h,minSearchQueryLength:u,layerHandle:d,allowedTileScales:m,digitalSignatures:f,defaultGroup:g,collaborationPermissions:w,creatorName:y,documentEngineVersion:$,layerName:v,sourcePdfSha256:S}=await N(`${this._state.serverUrl}i/d/${this._state.documentId}`,this._state.authPayload,e.password);this._password=e.password;const I=(0,l.valid)((0,l.coerce)($));if(this.documentEngineVersion=I,!this.satisfiesDocumentEngineVersion(">=1.5.6"))throw new i.uE(`Document Engine version ${$} does not meet minimum required version requirement >=1.5.6.`);if(this._state.instantSettings&&!o.includes(k.Y.INSTANT))throw new i.uE("Instant feature is not enabled on this server. Please set `instant` to `false`.\n\nFor more information about PSPDFKit Instant please visit:\nhttps://pspdfkit.com/guides/web/current/instant/overview/");if(this._state.useDeprecatedRestProvider&&this._state.instantSettings)throw new i.uE("Instant feature can't be enabled together with using deprecated REST provider. Please set either `instant` or `useDeprecatedRestProvider` to `false`.\n\nFor more information about PSPDFKit Instant please visit:\nhttps://pspdfkit.com/guides/web/current/instant/overview/");const P=!o.includes(k.Y.INSTANT)&&!this.satisfiesDocumentEngineVersion(">=1.7.0"),A=a===E.g.ELECTRONIC_SIGNATURES&&(0,M.UX)(o)&&this._state.forceLegacySignaturesFeature?E.g.LEGACY_SIGNATURES:a;this._state=this._state.withMutations((e=>e.set("imageToken",t).set("token",r).set("features",(0,s.B8)(o)).set("signatureFeatureAvailability",A).set("backendPermissions",new b.A({readOnly:-1===n.indexOf("write"),downloadingAllowed:n.indexOf("download")>=0})).set("documentURL",`${this._state.serverUrl}i/d/${this._state.documentId}/h/${d}`).set("documentHandle",d).set("isDocumentHandleOutdated",!1).set("digitalSignatures",(0,p.N5)(f)).set("layerName",v).set("sourcePdfSha256",S).set("useDeprecatedRestProvider",P||e.useDeprecatedRestProvider)));if(this.corePDFDocument=new B.N({wasmClientGetter:async()=>{const t=q.mG.checkOut().object,r=(0,R.f)(null),n=await N(`${this._state.serverUrl}i/d/${this._state.documentId}`,this._state.authPayload,e.password);return(0,i.V1)(n.wasmToken,"No wasmToken was returned from the server."),(0,i.V1)(this._state.baseCoreUrl),await t.loadNativeModule(this._state.baseCoreUrl,{mainThreadOrigin:r,disableWebAssemblyStreaming:this._state.disableWebAssemblyStreaming,enableAutomaticLinkExtraction:!1,overrideMemoryLimit:this._state.overrideMemoryLimit,workerSpawnerFn:()=>c.pj(!0)}).then((async()=>t.load("",n.wasmToken,{mainThreadOrigin:r,productId:null}))),t}},{type:"serverDocument",documentId:this._state.documentId,serverInfo:{url:this._state.serverUrl,token:this._state.token,imageToken:this._state.imageToken,layerHandle:d,headers:{"PSPDFKit-Platform":"web","PSPDFKit-Version":(0,R._q)()}},satifiesVersion:e=>this.satisfiesDocumentEngineVersion(e)}),w&&!this._state.instantSettings)throw new i.uE("Collaboration Permissions is not supported when `instant` is disabled. Please make sure `configuration#instant` is set to `true`.");return this._state=this._state.withMutations((e=>{e.defaultGroup=g,e.hasCollaborationPermissions=Boolean(w)})),this.provider&&this.provider.destroy(),this.provider=await this._initProvider(),{features:this._state.features,signatureFeatureAvailability:this._state.signatureFeatureAvailability,hasPassword:h,password:this._password,minSearchQueryLength:u,allowedTileScales:m,creatorName:y,defaultGroup:g}}satisfiesDocumentEngineVersion(e){return!!this.documentEngineVersion&&(0,l.satisfies)(this.documentEngineVersion,e)}async tryAuthenticateHostedViewer(){if("accessToken"in this._state.authPayload){const{hostedBaseUrl:e}=this._state,t=this._state.authPayload.accessToken,{serverUrl:r,serverId:n,jwt:s}=await async function(e,t){const r=await fetch(`${e}i/documents/auth`,{method:"POST",headers:{Accept:"application/json","Content-Type":"application/json","PSPDFKit-Platform":"web","PSPDFKit-Version":"cloud-protocol=1, server-protocol=5, client=1.4.1, client-git=d1c008c497"},body:JSON.stringify({accessToken:t})});if(r.ok)return r.json();throw new Error(`An error occurred while connecting to PSPDFKit API: ${await r.text()}`)}(e,t);this._state=this._state.withMutations((e=>{e.set("serverUrl",r).set("documentId",n).set("documentURL",`${r}i/d/${n}`).set("authPayload",{jwt:s})}))}}async _initProvider(){if(this._state.useDeprecatedRestProvider){const e=this._state.isFormsEnabled&&this._state.features.includes(k.Y.FORMS),{RESTProvider:t}=await r.e(269).then(r.bind(r,33393));return new t(this._state.documentURL,{token:this._state.token},{isFormsEnabled:e})}{const e=`${this._state.serverUrl}i/d/${this._state.documentId}/h/${this._state.documentHandle}`,{InstantProvider:t}=await r.e(534).then(r.bind(r,15124)),n=new t(`${this._state.serverUrl}i/d/${this._state.documentId}`,e,{auth_token:this._state.token},this._state.instantSettings||O.z);return n.setDocumentHandleConflictCallback(this.handleDocumentHandleConflict),n}}destroy(){this._destroyProvider(),this._requestQueue&&this._requestQueue.destroy()}documentInfo(){return this._fetch(`${this._state.documentURL}/document.json`).then((e=>e.json())).then((e=>e.data))}getFormJSON(){return this._fetch(`${this._state.documentURL}/form.json`).then((e=>403===e.status?{v:1,type:"pspdfkit/form",annotations:[],fields:[]}:e.json().then((e=>e.data))))}async evalFormValuesActions(){throw new Error("not implemented")}async evalScript(){throw new Error("not implemented")}async setFormJSONUpdateBatchMode(){throw new Error("not implemented")}compareDocuments(){throw new Error("not implemented")}updateButtonIcon(){throw new Error("not implemented")}permissions(){return Promise.resolve(this._state.backendPermissions)}getDefaultGroup(){return this._state.defaultGroup}isCollaborationPermissionsEnabled(){return this._state.hasCollaborationPermissions}textForPageIndex(e){return this.corePDFDocument.getTextLines(e)}async getSuggestedLineHeightFactor(){return 1}async getAnnotationsByScale(){return[]}getAvailableFontFaces(){throw new i.uE("Custom fonts need to be mounted on the server in Server-Backed deployments.")}getContentTreeForPageIndex(e){return this.corePDFDocument.getContentTree(e)}getTextFromRects(e,t){const r=encodeURIComponent(JSON.stringify(t.map(d.w).toArray()));return this._fetch(`${this._state.documentURL}/page-${e}-highlighted?rects=${r}`).then((e=>e.json())).then((e=>e.text))}_getJSONRequestHandler(){return f.A}renderAnnotation(e,t,r,n,s){return this._requestRenderAnnotation(e,t,r,n,s)}async renderPageAnnotations(e,t,r){const n=this.provider,s=t.some((e=>e instanceof _.sb));s&&await n._setReadStateCallbacksPromise;const i=[],o=t.filter((e=>{const t=s?n._readStateCallbacks.getAnnotationWithFormField(e.id):null,r=t?.formField,o=(0,A.lG)(e,r);if(o&&r&&"number"==typeof e.pdfObjectId){i.find((e=>e.name===r.name))||i.push((0,p.cA)((0,j.Af)(r)))}return o&&"number"==typeof e.pdfObjectId}));if(0===o.size&&0===i.length)return Promise.resolve();const a=new Promise(((t,n)=>{const s=o.filter((e=>0!==Math.floor(e.boundingBox.width*r)&&0!==Math.floor(e.boundingBox.height*r))),{promise:a,cancel:l}=this._requestRenderAnnotations(e,s.map((e=>e.pdfObjectId)).toArray(),s.map((e=>Math.floor(e.boundingBox.width*r))).toArray(),s.map((e=>Math.floor(e.boundingBox.height*r))).toArray(),i);a.then((e=>{const r=e.map((e=>e&&(0,U.BJ)(e)));r.forEach((async(e,t)=>{const r=await e,n=s.get(t);if(n){const e=this.annotationAPStreamPromises.get(n.id);e&&(this.annotationAPStreamPromises=this.annotationAPStreamPromises.delete(n.id),e(r)),r&&this.cacheAPStream(r,n)}})),Promise.all(r).then((()=>t()))})).catch((e=>{l(),n(e)}))}));return this.pageAPStreamsPromises=this.pageAPStreamsPromises.set(e,a),a}renderDetachedAnnotation(e,t,r,n){return this._requestRenderAnnotation(e,null,t,r,n,!0)}async getAttachment(e){try{const t=await this._fetch(`${this._state.documentURL}/attachments/${e}`);switch(t.status){case 404:throw new i.uE("Attachment not Found.");case 200:return await t.blob();default:throw new i.uE("Bad Request.")}}catch(e){throw new i.uE(`Could not fetch attachment from Nutrient Document Engine. ${e}`)}}async search(e,t,r,n){let s=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:C.n.TEXT;const o=`q=${i===C.n.PRESET?e.replace(/_/g,"-"):encodeURIComponent(e)}&start=${t}&limit=${r}&type=${i}&include_annotations=${s.toString()}&case_sensitive=${n.toString()}`,a=`${this._state.documentURL}/search?${o}`,l=await new f.A(a,this._state.token).request();return(0,u.g)(l.data)}async getMeasurementSnappingPoints(e){}async searchAndRedact(e,t){const{searchType:r,annotationPreset:n,searchInAnnotations:i,caseSensitive:o,startPageIndex:a,pageRange:l}=t,{color:c,fillColor:h,outlineColor:u,...d}=n,p=await this._fetch(`${this._state.documentURL}/redactions`,{method:"post",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({strategy:r,strategyOptions:{[r]:r===C.n.PRESET?e.replace(/_/g,"-"):e,includeAnnotations:i,caseSensitive:o,...void 0!==a&&{start:a},...void 0!==l&&{limit:l}},content:{...d,color:c&&c.toHex(),fillColor:h&&h.toHex(),outlineColor:u&&u.toHex()}})}),{data:m}=await p.json(),f=(0,s.B8)(m.annotations?.filter((e=>!!e.content)).map((e=>F.A.fromJSON(e.id,e.content)))||[]);if(f.size){const e=this._state.instantSettings;!this.isUsingInstantProvider()||e&&e.listenToServerChangesEnabled||await this.syncChanges()}return f}exportPDF(){let{flatten:e=!1,includeComments:t=!0,excludeAnnotations:r=!1,outputFormat:n=!1,optimize:s=!1,flattenElectronicSignatures:o=e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e&&!1===o)throw new i.uE("Cannot set `flattenElectronicSignatures` to `false` when `flatten` is set to `true`.");const a=`${this._state.documentURL}/pdf?token=${this._state.token}${e?`&flatten=${String(e)}`:""}&comments=${String(t)}&render_ap_streams=${String(!e)}&remove_annotations=${String(r)}${e?"":`&keep_signatures=${!o}`}`;if(s){const e={documentFormat:"pdf",grayscaleText:!1,grayscaleGraphics:!1,grayscaleFormFields:!1,grayscaleAnnotations:!1,grayscaleImages:!1,disableImages:!1,mrcCompression:!1,imageOptimizationQuality:2,linearize:!1};let t;if("boolean"!=typeof s){t={...e,...s}}else t=e;const{documentFormat:r,grayscaleText:n,grayscaleGraphics:i,grayscaleFormFields:o,grayscaleAnnotations:l,grayscaleImages:c,disableImages:h,mrcCompression:u,imageOptimizationQuality:d,linearize:p}=t;return fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({parts:[{document:{id:"#self"}}],output:{type:r,grayscaleText:n,grayscaleGraphics:i,grayscaleFormFields:o,grayscaleAnnotations:l,grayscaleImages:c,disableImages:h,mrcCompression:u,imageOptimizationQuality:d,linearize:p}}),credentials:"include"}).then((e=>e.arrayBuffer()))}if(n){const e={conformance:G.o.PDFA_2B,vectorization:!0,rasterization:!0};let t;if("boolean"!=typeof n){t={...e,...n}}else t=e;const{conformance:r,vectorization:s,rasterization:i}=t;return fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({parts:[{document:{id:"#self"}}],output:{type:"pdfa",conformance:r,vectorization:s,rasterization:i}}),credentials:"include"}).then((e=>e.arrayBuffer()))}return fetch(a,{method:"GET",credentials:"include"}).then((e=>e.arrayBuffer()))}exportOffice(e){let{format:t}=e;const r=`${this._state.documentURL}/build`;return this._fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({parts:[{document:{id:"#self"}}],output:{type:t.toLowerCase()}}),credentials:"include"}).then((e=>e.arrayBuffer())).catch((e=>{throw new i.uE(`Exporting to Office failed: ${e.message}`)}))}exportXFDF(){return this._fetch(`${this._state.documentURL}/document.xfdf`).then((e=>e.text()))}exportInstantJSON(e){return this._fetch(`${this._state.documentURL}/instant.json${"number"==typeof e?`?version=${e}`:""}`).then((e=>e.json()))}getPDFURL(){let{includeComments:e=!0,excludeAnnotations:t=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{promise:Promise.resolve(`${this._state.documentURL}/pdf?token=${this._state.token}&flatten=true&comments=${String(e)}&remove_annotations=${String(t)}`),revoke:()=>{}}}generatePDFObjectURL(){let e,{includeComments:t=!0,excludeAnnotations:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=!1;return{promise:new Promise((s=>{this.exportPDF({flatten:!0,includeComments:t,excludeAnnotations:r}).then((t=>{if(n)return;const r=new Blob([t],{type:"application/pdf"});e=window.URL.createObjectURL(r),s(e)}))})),revoke:()=>{e&&window.URL.revokeObjectURL(e),n=!0}}}async getDocumentOutline(){let e;try{e=(await this._fetch(`${this._state.documentURL}/outline.json`).then((e=>e.json()))).data}catch(t){e={}}const t=Array.isArray(e.outline)?e.outline:[];return(0,s.B8)(t.map(L.r))}async setDocumentOutline(){throw new i.uE("Not implemented in Server backend.")}async getPageGlyphs(){throw new i.uE("Not implemented in Server backend.")}onKeystrokeEvent(){throw new Error("not implemented")}async getMeasurementScales(){let e;try{return e=(await this._fetch(`${this._state.documentURL}/measurement_content_formats`).then((e=>e.json()))).data,e}catch(e){throw new i.uE(`Fetching measurement scales failed: ${e}`)}}async getSecondaryMeasurementUnit(){let e;try{return e=(await this._fetch(`${this._state.documentURL}/secondary_measurement_unit`).then((e=>e.json()))).data,e}catch(e){throw new i.uE(`Fetching secondary measurement unit failed: ${e}`)}}async setSecondaryMeasurementUnit(e){try{const t=JSON.stringify({unitTo:e?e.unitTo:null,precision:e?e.precision:null});await this._fetch(`${this._state.documentURL}/secondary_measurement_unit`,{method:"post",body:t,credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Setting secondary measurement unit failed: ${e}`)}}async addMeasurementScale(e){try{const t=JSON.stringify((0,h.U)(e));await this._fetch(`${this._state.documentURL}/measurement_content_formats`,{method:"post",body:t,credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Adding a new measurement scale failed: ${e}`)}}async removeMeasurementScale(e){try{const t=JSON.stringify((0,h.U)(e));await this._fetch(`${this._state.documentURL}/measurement_content_formats/delete`,{method:"post",body:t,credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Removing scale failed: ${e}`)}}async applyOperationsAndReload(e){try{const t=await z(e);this._destroyProvider(),await this._fetch(`${this._state.documentURL}/apply-operations`,{method:"post",body:t,credentials:"include"})}catch(e){throw new i.uE(`Applying operations failed: ${e}`)}return this.reloadDocument()}async applyRedactionsAndReload(){try{return this._destroyProvider(),await this._fetch(`${this._state.documentURL}/redact`,{method:"post",credentials:"include"}),this.reloadDocument()}catch(e){throw this.provider.load(),new i.uE(`Applying redactions failed: ${e}`)}}async reloadDocument(){this.clearRenderedAnnotationsCache();try{return await this.load({password:this._password})}catch(e){throw new i.uE(`Reloading the document failed: ${e}`)}}async exportPDFWithOperations(e){try{const t=await z(e);return this._fetch(`${this._state.documentURL}/pdf-with-operations`,{method:"post",body:t,credentials:"include"}).then((e=>e.arrayBuffer()))}catch(e){throw new i.uE(`Exporting PDF with operations failed: ${e}`)}}async setSignaturesLTV(e){throw new Error("not implemented")}async getSignaturesInfo(){return this._refreshSignaturesInfoPromise&&await this._refreshSignaturesInfoPromise,this._state.digitalSignatures}refreshSignaturesInfo(){return this._refreshSignaturesInfoPromise||(this._refreshSignaturesInfoPromise=new Promise(((e,t)=>{this._fetch(`${this._state.documentURL}/signatures`,{method:"get",credentials:"include"}).then((e=>e.json())).then((t=>{let{data:r}=t;this._state=this._state.set("digitalSignatures",(0,p.N5)(r)),this._refreshSignaturesInfoPromise=null,e()})).catch((e=>{this._state=this._state.set("digitalSignatures",null),this._refreshSignaturesInfoPromise=null,t(e)}))}))),this._refreshSignaturesInfoPromise}async signDocumentAndReload(e,t){(0,i.V1)(void 0===t||"object"==typeof t,"Signing service data must be an object if specified.");try{if(void 0!==t&&"object"!=typeof t)throw new i.uE("Signing service data must be an object if specified.");(0,V.r6)(e);const r=e?{..."placeholderSize"in e?{estimatedSize:e.placeholderSize}:null,..."flatten"in e?{flatten:e.flatten}:null,...e?.signatureMetadata?{signatureMetadata:(0,p.sZ)(e.signatureMetadata)}:null,..."position"in e?{position:(0,p.qN)(e.position)}:null,..."appearance"in e?{appearance:await(0,p.z8)(e.appearance)}:null,..."formFieldName"in e?{formFieldName:e.formFieldName}:null,...void 0!==e?.signingData&&"signatureContainer"in e.signingData?{signatureContainer:e.signingData.signatureContainer}:null,...void 0!==e?.signingData&&"signatureType"in e.signingData?{signatureType:p.xz[e.signingData.signatureType]}:null}:null,n=e?.appearance?.watermarkImage||null,s=e?.appearance?.graphicImage||null,o={...t?{signingToken:t.signingToken}:null,...r},a=new FormData;if(a.append("data",JSON.stringify(o)),n&&a.append("image",n),s&&a.append("graphicImage",s),this._destroyProvider(),await this._fetch(`${this._state.documentURL}/sign`,{method:"post",body:a,credentials:"include"}),await this.reloadDocument(),e?.formFieldName)return e.formFieldName}catch(e){throw this.provider.load(),new i.uE(`Adding digital signature failed: ${e.message||e}`)}}async setFontSubstitutions(e){try{const t=`${this._state.documentURL}/font-substitutions`;await this._fetch(t,{method:"put",body:JSON.stringify({fontSubstitutions:e}),credentials:"include",headers:{"Content-Type":"application/json"}})}catch(e){throw new i.uE(`Error setting font substitution: ${e.message}`)}}getDocumentHandle(){return this._state.documentHandle}async getEmbeddedFiles(){const e=await this._fetch(`${this._state.documentURL}/embedded-files`,{method:"get",credentials:"include"}),{data:t}=await e.json();return(0,s.B8)(t?.embeddedFiles?.map((e=>{let{id:t,content:r}=e;return(0,x.r)(t,r)}))||[])}cancelRequests(){this._requestQueue.cancelAll()}_destroyProvider(){this.provider&&(this.provider._clients&&this.provider._clients.disconnect(),this.provider.destroy())}async _fetch(e,t){const r=await fetch(e,{...t,headers:{...t?.headers,"X-PSPDFKit-Token":this._state.token,"PSPDFKit-Platform":"web","PSPDFKit-Version":(0,R._q)()}});if(!r.ok){let e=await function(e){return e.clone().json().catch((()=>e.text()))}(r);e="object"==typeof e?e.reason:e;const t=e||`${r.status} ${r.statusText}`;throw new i.uE(t)}return r}syncChanges(){return this.provider.syncChanges()}async clearAPStreamCache(){}async runPDFFormattingScripts(){return[]}async runPDFFormattingScriptsFromWidgets(){return this.runPDFFormattingScripts()}async lazyLoadPages(){}async contentEditorReload(){return this._destroyProvider(),this.reloadDocument()}getOCGs(){throw new Error("not implemented")}getOCGVisibilityState(){throw new Error("not implemented")}setOCGVisibilityState(){throw new Error("not implemented")}async timestampData(e,t){throw new Error("not implemented")}async waitUntilFullyLoaded(e){throw new Error("not implemented")}}async function z(e){const t={},r=new WeakMap,n=await Promise.all(e.map((async(e,n)=>{if("importDocument"===e.type){const s=e.document;return(0,i.V1)(s instanceof File||s instanceof Blob,"Wrong `importDocument` operation `document` value: it must be a File or a Blob"),(0,T.g)(r,t,s,e,n,"document")}if("applyInstantJson"===e.type){const s=e.instantJson;(0,i.V1)("object"==typeof s&&null!==s,"Wrong `applyInstantJson` operation `instantJson` value: it must be an object");const o=JSON.stringify(s),a=new Blob([o],{type:"application/json"});return(0,T.g)(r,t,a,e,n,"dataFilePath")}if("applyXfdf"===e.type){const s=e.xfdf;(0,i.V1)("string"==typeof s,"Wrong `applyXfdf` operation `xfdf` value: it must be a string");const o=new Blob([s],{type:"application/vnd.adobe.xfdf"});return(0,T.g)(r,t,o,e,n,"dataFilePath")}return e}))),s=new FormData;s.append("operations",JSON.stringify({operations:n}));for(const e in t)s.append(e,t[e]);return s}},4824:(e,t,r)=>{"use strict";r.d(t,{S:()=>n,z:()=>s});const n={clientsPresenceEnabled:!0,listenToServerChangesEnabled:!0},s={clientsPresenceEnabled:!1,listenToServerChangesEnabled:!1}},93904:(e,t,r)=>{const n=Symbol("SemVer ANY");class s{static get ANY(){return n}constructor(e,t){if(t=i(t),e instanceof s){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),c("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,c("comp",this)}parse(e){const t=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],r=e.match(t);if(!r)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new h(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(c("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new h(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new u(e.value,t).test(this.value):""===e.operator?""===e.value||new u(this.value,t).test(e.semver):(!(t=i(t)).includePrerelease||"<0.0.0-0"!==this.value&&"<0.0.0-0"!==e.value)&&(!(!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&(!(!this.operator.startsWith(">")||!e.operator.startsWith(">"))||(!(!this.operator.startsWith("<")||!e.operator.startsWith("<"))||(!(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))||(!!(l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))||!!(l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))))))}}e.exports=s;const i=r(98587),{safeRe:o,t:a}=r(99718),l=r(72111),c=r(57272),h=r(53908),u=r(78311)},78311:(e,t,r)=>{const n=/\s+/g;class s{constructor(e,t){if(t=o(t),e instanceof s)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new s(e.raw,t);if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(n," "),this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length)),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter((e=>!g(e[0]))),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&w(e[0])){this.set=[e];break}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");const t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&f)|(this.options.loose&&E))+":"+e,r=i.get(t);if(r)return r;const n=this.options.loose,s=n?h[u.HYPHENRANGELOOSE]:h[u.HYPHENRANGE];e=e.replace(s,N(this.options.includePrerelease)),l("hyphen replace",e),e=e.replace(h[u.COMPARATORTRIM],d),l("comparator trim",e),e=e.replace(h[u.TILDETRIM],p),l("tilde trim",e),e=e.replace(h[u.CARETTRIM],m),l("caret trim",e);let o=e.split(" ").map((e=>$(e,this.options))).join(" ").split(/\s+/).map((e=>b(e,this.options)));n&&(o=o.filter((e=>(l("loose invalid filter",e,this.options),!!e.match(h[u.COMPARATORLOOSE]))))),l("range list",o);const c=new Map,w=o.map((e=>new a(e,this.options)));for(const e of w){if(g(e))return[e];c.set(e.value,e)}c.size>1&&c.has("")&&c.delete("");const y=[...c.values()];return i.set(t,y),y}intersects(e,t){if(!(e instanceof s))throw new TypeError("a Range is required");return this.set.some((r=>y(r,t)&&e.set.some((e=>y(e,t)&&r.every((r=>e.every((e=>r.intersects(e,t)))))))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(T(this.set[t],e,this.options))return!0;return!1}}e.exports=s;const i=new(r(68794)),o=r(98587),a=r(93904),l=r(57272),c=r(53908),{safeRe:h,t:u,comparatorTrimReplace:d,tildeTrimReplace:p,caretTrimReplace:m}=r(99718),{FLAG_INCLUDE_PRERELEASE:f,FLAG_LOOSE:E}=r(16874),g=e=>"<0.0.0-0"===e.value,w=e=>""===e.value,y=(e,t)=>{let r=!0;const n=e.slice();let s=n.pop();for(;r&&n.length;)r=n.every((e=>s.intersects(e,t))),s=n.pop();return r},$=(e,t)=>(l("comp",e,t),e=I(e,t),l("caret",e),e=v(e,t),l("tildes",e),e=A(e,t),l("xrange",e),e=_(e,t),l("stars",e),e),R=e=>!e||"x"===e.toLowerCase()||"*"===e,v=(e,t)=>e.trim().split(/\s+/).map((e=>S(e,t))).join(" "),S=(e,t)=>{const r=t.loose?h[u.TILDELOOSE]:h[u.TILDE];return e.replace(r,((t,r,n,s,i)=>{let o;return l("tilde",e,t,r,n,s,i),R(r)?o="":R(n)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:R(s)?o=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:i?(l("replaceTilde pr",i),o=`>=${r}.${n}.${s}-${i} <${r}.${+n+1}.0-0`):o=`>=${r}.${n}.${s} <${r}.${+n+1}.0-0`,l("tilde return",o),o}))},I=(e,t)=>e.trim().split(/\s+/).map((e=>P(e,t))).join(" "),P=(e,t)=>{l("caret",e,t);const r=t.loose?h[u.CARETLOOSE]:h[u.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,((t,r,s,i,o)=>{let a;return l("caret",e,t,r,s,i,o),R(r)?a="":R(s)?a=`>=${r}.0.0${n} <${+r+1}.0.0-0`:R(i)?a="0"===r?`>=${r}.${s}.0${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${n} <${+r+1}.0.0-0`:o?(l("replaceCaret pr",o),a="0"===r?"0"===s?`>=${r}.${s}.${i}-${o} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}-${o} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i}-${o} <${+r+1}.0.0-0`):(l("no pr"),a="0"===r?"0"===s?`>=${r}.${s}.${i}${n} <${r}.${s}.${+i+1}-0`:`>=${r}.${s}.${i}${n} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${i} <${+r+1}.0.0-0`),l("caret return",a),a}))},A=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map((e=>L(e,t))).join(" ")),L=(e,t)=>{e=e.trim();const r=t.loose?h[u.XRANGELOOSE]:h[u.XRANGE];return e.replace(r,((r,n,s,i,o,a)=>{l("xRange",e,r,n,s,i,o,a);const c=R(s),h=c||R(i),u=h||R(o),d=u;return"="===n&&d&&(n=""),a=t.includePrerelease?"-0":"",c?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&d?(h&&(i=0),o=0,">"===n?(n=">=",h?(s=+s+1,i=0,o=0):(i=+i+1,o=0)):"<="===n&&(n="<",h?s=+s+1:i=+i+1),"<"===n&&(a="-0"),r=`${n+s}.${i}.${o}${a}`):h?r=`>=${s}.0.0${a} <${+s+1}.0.0-0`:u&&(r=`>=${s}.${i}.0${a} <${s}.${+i+1}.0-0`),l("xRange return",r),r}))},_=(e,t)=>(l("replaceStars",e,t),e.trim().replace(h[u.STAR],"")),b=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(h[t.includePrerelease?u.GTE0PRE:u.GTE0],"")),N=e=>(t,r,n,s,i,o,a,l,c,h,u,d)=>`${r=R(n)?"":R(s)?`>=${n}.0.0${e?"-0":""}`:R(i)?`>=${n}.${s}.0${e?"-0":""}`:o?`>=${r}`:`>=${r}${e?"-0":""}`} ${l=R(c)?"":R(h)?`<${+c+1}.0.0-0`:R(u)?`<${c}.${+h+1}.0-0`:d?`<=${c}.${h}.${u}-${d}`:e?`<${c}.${h}.${+u+1}-0`:`<=${l}`}`.trim(),T=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==a.ANY&&e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},53908:(e,t,r)=>{const n=r(57272),{MAX_LENGTH:s,MAX_SAFE_INTEGER:i}=r(16874),{safeRe:o,safeSrc:a,t:l}=r(99718),c=r(98587),{compareIdentifiers:h}=r(61123);class u{constructor(e,t){if(t=c(t),e instanceof u){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw new TypeError(`version is longer than ${s} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?o[l.LOOSE]:o[l.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>i||this.major<0)throw new TypeError("Invalid major version");if(this.minor>i||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>i||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<i)return t}return e})):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof u)){if("string"==typeof e&&e===this.version)return 0;e=new u(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof u||(e=new u(e,this.options)),h(this.major,e.major)||h(this.minor,e.minor)||h(this.patch,e.patch)}comparePre(e){if(e instanceof u||(e=new u(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],s=e.prerelease[t];if(n("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return h(r,s)}while(++t)}compareBuild(e){e instanceof u||(e=new u(e,this.options));let t=0;do{const r=this.build[t],s=e.build[t];if(n("build compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return-1;if(r!==s)return h(r,s)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw new Error("invalid increment argument: identifier is empty");if(t){const e=new RegExp(`^${this.options.loose?a[l.PRERELEASELOOSE]:a[l.PRERELEASE]}$`),r=`-${t}`.match(e);if(!r||r[1]!==t)throw new Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(r)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===h(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=u},57414:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},72111:(e,t,r)=>{const n=r(94641),s=r(13999),i=r(35580),o=r(54089),a=r(7059),l=r(25200);e.exports=(e,t,r,c)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,c);case"!=":return s(e,r,c);case">":return i(e,r,c);case">=":return o(e,r,c);case"<":return a(e,r,c);case"<=":return l(e,r,c);default:throw new TypeError(`Invalid operator: ${t}`)}}},46170:(e,t,r)=>{const n=r(53908),s=r(30144),{safeRe:i,t:o}=r(99718);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){const n=t.includePrerelease?i[o.COERCERTLFULL]:i[o.COERCERTL];let s;for(;(s=n.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&s.index+s[0].length===r.index+r[0].length||(r=s),n.lastIndex=s.index+s[1].length+s[2].length;n.lastIndex=-1}else r=e.match(t.includePrerelease?i[o.COERCEFULL]:i[o.COERCE]);if(null===r)return null;const a=r[2],l=r[3]||"0",c=r[4]||"0",h=t.includePrerelease&&r[5]?`-${r[5]}`:"",u=t.includePrerelease&&r[6]?`+${r[6]}`:"";return s(`${a}.${l}.${c}${h}${u}`,t)}},40909:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r)=>{const s=new n(e,r),i=new n(t,r);return s.compare(i)||s.compareBuild(i)}},11763:(e,t,r)=>{const n=r(50560);e.exports=(e,t)=>n(e,t,!0)},50560:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},51832:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e,null,!0),s=n(t,null,!0),i=r.compare(s);if(0===i)return null;const o=i>0,a=o?r:s,l=o?s:r,c=!!a.prerelease.length;if(!!l.prerelease.length&&!c){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(a))return l.minor&&!l.patch?"minor":"patch"}const h=c?"pre":"";return r.major!==s.major?h+"major":r.minor!==s.minor?h+"minor":r.patch!==s.patch?h+"patch":"prerelease"}},94641:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>0===n(e,t,r)},35580:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)>0},54089:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)>=0},93007:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r,s,i)=>{"string"==typeof r&&(i=s,s=r,r=void 0);try{return new n(e instanceof n?e.version:e,r).inc(t,s,i).version}catch(e){return null}}},7059:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)<0},25200:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(e,t,r)<=0},32938:(e,t,r)=>{const n=r(53908);e.exports=(e,t)=>new n(e,t).major},46254:(e,t,r)=>{const n=r(53908);e.exports=(e,t)=>new n(e,t).minor},13999:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>0!==n(e,t,r)},30144:(e,t,r)=>{const n=r(53908);e.exports=(e,t,r=!1)=>{if(e instanceof n)return e;try{return new n(e,t)}catch(e){if(!r)return null;throw e}}},24493:(e,t,r)=>{const n=r(53908);e.exports=(e,t)=>new n(e,t).patch},31729:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},9970:(e,t,r)=>{const n=r(50560);e.exports=(e,t,r)=>n(t,e,r)},74277:(e,t,r)=>{const n=r(40909);e.exports=(e,t)=>e.sort(((e,r)=>n(r,e,t)))},97638:(e,t,r)=>{const n=r(78311);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},43927:(e,t,r)=>{const n=r(40909);e.exports=(e,t)=>e.sort(((e,r)=>n(e,r,t)))},56953:(e,t,r)=>{const n=r(30144);e.exports=(e,t)=>{const r=n(e,t);return r?r.version:null}},99589:(e,t,r)=>{const n=r(99718),s=r(16874),i=r(53908),o=r(61123),a=r(30144),l=r(56953),c=r(57414),h=r(93007),u=r(51832),d=r(32938),p=r(46254),m=r(24493),f=r(31729),E=r(50560),g=r(9970),w=r(11763),y=r(40909),$=r(43927),R=r(74277),v=r(35580),S=r(7059),I=r(94641),P=r(13999),A=r(54089),L=r(25200),_=r(72111),b=r(46170),N=r(93904),T=r(78311),O=r(97638),F=r(77631),C=r(19628),D=r(270),x=r(41261),U=r(13874),j=r(97075),k=r(75571),M=r(5342),G=r(76780),V=r(72525),B=r(75032);e.exports={parse:a,valid:l,clean:c,inc:h,diff:u,major:d,minor:p,patch:m,prerelease:f,compare:E,rcompare:g,compareLoose:w,compareBuild:y,sort:$,rsort:R,gt:v,lt:S,eq:I,neq:P,gte:A,lte:L,cmp:_,coerce:b,Comparator:N,Range:T,satisfies:O,toComparators:F,maxSatisfying:C,minSatisfying:D,minVersion:x,validRange:U,outside:j,gtr:k,ltr:M,intersects:G,simplifyRange:V,subset:B,SemVer:i,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},16874:e=>{const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},57272:e=>{const t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},61123:e=>{const t=/^[0-9]+$/,r=(e,r)=>{const n=t.test(e),s=t.test(r);return n&&s&&(e=+e,r=+r),e===r?0:n&&!s?-1:s&&!n?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},68794:e=>{e.exports=class{constructor(){this.max=1e3,this.map=new Map}get(e){const t=this.map.get(e);return void 0===t?void 0:(this.map.delete(e),this.map.set(e,t),t)}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){const e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}},98587:e=>{const t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},99718:(e,t,r)=>{const{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:i}=r(16874),o=r(57272),a=(t=e.exports={}).re=[],l=t.safeRe=[],c=t.src=[],h=t.safeSrc=[],u=t.t={};let d=0;const p="[a-zA-Z0-9-]",m=[["\\s",1],["\\d",i],[p,s]],f=(e,t,r)=>{const n=(e=>{for(const[t,r]of m)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e})(t),s=d++;o(e,s,t),u[e]=s,c[s]=t,h[s]=n,a[s]=new RegExp(t,r?"g":void 0),l[s]=new RegExp(n,r?"g":void 0)};f("NUMERICIDENTIFIER","0|[1-9]\\d*"),f("NUMERICIDENTIFIERLOOSE","\\d+"),f("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${p}*`),f("MAINVERSION",`(${c[u.NUMERICIDENTIFIER]})\\.(${c[u.NUMERICIDENTIFIER]})\\.(${c[u.NUMERICIDENTIFIER]})`),f("MAINVERSIONLOOSE",`(${c[u.NUMERICIDENTIFIERLOOSE]})\\.(${c[u.NUMERICIDENTIFIERLOOSE]})\\.(${c[u.NUMERICIDENTIFIERLOOSE]})`),f("PRERELEASEIDENTIFIER",`(?:${c[u.NUMERICIDENTIFIER]}|${c[u.NONNUMERICIDENTIFIER]})`),f("PRERELEASEIDENTIFIERLOOSE",`(?:${c[u.NUMERICIDENTIFIERLOOSE]}|${c[u.NONNUMERICIDENTIFIER]})`),f("PRERELEASE",`(?:-(${c[u.PRERELEASEIDENTIFIER]}(?:\\.${c[u.PRERELEASEIDENTIFIER]})*))`),f("PRERELEASELOOSE",`(?:-?(${c[u.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[u.PRERELEASEIDENTIFIERLOOSE]})*))`),f("BUILDIDENTIFIER",`${p}+`),f("BUILD",`(?:\\+(${c[u.BUILDIDENTIFIER]}(?:\\.${c[u.BUILDIDENTIFIER]})*))`),f("FULLPLAIN",`v?${c[u.MAINVERSION]}${c[u.PRERELEASE]}?${c[u.BUILD]}?`),f("FULL",`^${c[u.FULLPLAIN]}$`),f("LOOSEPLAIN",`[v=\\s]*${c[u.MAINVERSIONLOOSE]}${c[u.PRERELEASELOOSE]}?${c[u.BUILD]}?`),f("LOOSE",`^${c[u.LOOSEPLAIN]}$`),f("GTLT","((?:<|>)?=?)"),f("XRANGEIDENTIFIERLOOSE",`${c[u.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),f("XRANGEIDENTIFIER",`${c[u.NUMERICIDENTIFIER]}|x|X|\\*`),f("XRANGEPLAIN",`[v=\\s]*(${c[u.XRANGEIDENTIFIER]})(?:\\.(${c[u.XRANGEIDENTIFIER]})(?:\\.(${c[u.XRANGEIDENTIFIER]})(?:${c[u.PRERELEASE]})?${c[u.BUILD]}?)?)?`),f("XRANGEPLAINLOOSE",`[v=\\s]*(${c[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[u.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[u.XRANGEIDENTIFIERLOOSE]})(?:${c[u.PRERELEASELOOSE]})?${c[u.BUILD]}?)?)?`),f("XRANGE",`^${c[u.GTLT]}\\s*${c[u.XRANGEPLAIN]}$`),f("XRANGELOOSE",`^${c[u.GTLT]}\\s*${c[u.XRANGEPLAINLOOSE]}$`),f("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),f("COERCE",`${c[u.COERCEPLAIN]}(?:$|[^\\d])`),f("COERCEFULL",c[u.COERCEPLAIN]+`(?:${c[u.PRERELEASE]})?`+`(?:${c[u.BUILD]})?(?:$|[^\\d])`),f("COERCERTL",c[u.COERCE],!0),f("COERCERTLFULL",c[u.COERCEFULL],!0),f("LONETILDE","(?:~>?)"),f("TILDETRIM",`(\\s*)${c[u.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",f("TILDE",`^${c[u.LONETILDE]}${c[u.XRANGEPLAIN]}$`),f("TILDELOOSE",`^${c[u.LONETILDE]}${c[u.XRANGEPLAINLOOSE]}$`),f("LONECARET","(?:\\^)"),f("CARETTRIM",`(\\s*)${c[u.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",f("CARET",`^${c[u.LONECARET]}${c[u.XRANGEPLAIN]}$`),f("CARETLOOSE",`^${c[u.LONECARET]}${c[u.XRANGEPLAINLOOSE]}$`),f("COMPARATORLOOSE",`^${c[u.GTLT]}\\s*(${c[u.LOOSEPLAIN]})$|^$`),f("COMPARATOR",`^${c[u.GTLT]}\\s*(${c[u.FULLPLAIN]})$|^$`),f("COMPARATORTRIM",`(\\s*)${c[u.GTLT]}\\s*(${c[u.LOOSEPLAIN]}|${c[u.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",f("HYPHENRANGE",`^\\s*(${c[u.XRANGEPLAIN]})\\s+-\\s+(${c[u.XRANGEPLAIN]})\\s*$`),f("HYPHENRANGELOOSE",`^\\s*(${c[u.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[u.XRANGEPLAINLOOSE]})\\s*$`),f("STAR","(<|>)?=?\\s*\\*"),f("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),f("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},75571:(e,t,r)=>{const n=r(97075);e.exports=(e,t,r)=>n(e,t,">",r)},76780:(e,t,r)=>{const n=r(78311);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t,r))},5342:(e,t,r)=>{const n=r(97075);e.exports=(e,t,r)=>n(e,t,"<",r)},19628:(e,t,r)=>{const n=r(53908),s=r(78311);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach((e=>{a.test(e)&&(i&&-1!==o.compare(e)||(i=e,o=new n(i,r)))})),i}},270:(e,t,r)=>{const n=r(53908),s=r(78311);e.exports=(e,t,r)=>{let i=null,o=null,a=null;try{a=new s(t,r)}catch(e){return null}return e.forEach((e=>{a.test(e)&&(i&&1!==o.compare(e)||(i=e,o=new n(i,r)))})),i}},41261:(e,t,r)=>{const n=r(53908),s=r(78311),i=r(35580);e.exports=(e,t)=>{e=new s(e,t);let r=new n("0.0.0");if(e.test(r))return r;if(r=new n("0.0.0-0"),e.test(r))return r;r=null;for(let t=0;t<e.set.length;++t){const s=e.set[t];let o=null;s.forEach((e=>{const t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":o&&!i(t,o)||(o=t);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}})),!o||r&&!i(r,o)||(r=o)}return r&&e.test(r)?r:null}},97075:(e,t,r)=>{const n=r(53908),s=r(93904),{ANY:i}=s,o=r(78311),a=r(97638),l=r(35580),c=r(7059),h=r(25200),u=r(54089);e.exports=(e,t,r,d)=>{let p,m,f,E,g;switch(e=new n(e,d),t=new o(t,d),r){case">":p=l,m=h,f=c,E=">",g=">=";break;case"<":p=c,m=u,f=l,E="<",g="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,d))return!1;for(let r=0;r<t.set.length;++r){const n=t.set[r];let o=null,a=null;if(n.forEach((e=>{e.semver===i&&(e=new s(">=0.0.0")),o=o||e,a=a||e,p(e.semver,o.semver,d)?o=e:f(e.semver,a.semver,d)&&(a=e)})),o.operator===E||o.operator===g)return!1;if((!a.operator||a.operator===E)&&m(e,a.semver))return!1;if(a.operator===g&&f(e,a.semver))return!1}return!0}},72525:(e,t,r)=>{const n=r(97638),s=r(50560);e.exports=(e,t,r)=>{const i=[];let o=null,a=null;const l=e.sort(((e,t)=>s(e,t,r)));for(const e of l){n(e,t,r)?(a=e,o||(o=e)):(a&&i.push([o,a]),a=null,o=null)}o&&i.push([o,null]);const c=[];for(const[e,t]of i)e===t?c.push(e):t||e!==l[0]?t?e===l[0]?c.push(`<=${t}`):c.push(`${e} - ${t}`):c.push(`>=${e}`):c.push("*");const h=c.join(" || "),u="string"==typeof t.raw?t.raw:String(t);return h.length<u.length?h:t}},75032:(e,t,r)=>{const n=r(78311),s=r(93904),{ANY:i}=s,o=r(97638),a=r(50560),l=[new s(">=0.0.0-0")],c=[new s(">=0.0.0")],h=(e,t,r)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===i){if(1===t.length&&t[0].semver===i)return!0;e=r.includePrerelease?l:c}if(1===t.length&&t[0].semver===i){if(r.includePrerelease)return!0;t=c}const n=new Set;let s,h,p,m,f,E,g;for(const t of e)">"===t.operator||">="===t.operator?s=u(s,t,r):"<"===t.operator||"<="===t.operator?h=d(h,t,r):n.add(t.semver);if(n.size>1)return null;if(s&&h){if(p=a(s.semver,h.semver,r),p>0)return null;if(0===p&&(">="!==s.operator||"<="!==h.operator))return null}for(const e of n){if(s&&!o(e,String(s),r))return null;if(h&&!o(e,String(h),r))return null;for(const n of t)if(!o(e,String(n),r))return!1;return!0}let w=!(!h||r.includePrerelease||!h.semver.prerelease.length)&&h.semver,y=!(!s||r.includePrerelease||!s.semver.prerelease.length)&&s.semver;w&&1===w.prerelease.length&&"<"===h.operator&&0===w.prerelease[0]&&(w=!1);for(const e of t){if(g=g||">"===e.operator||">="===e.operator,E=E||"<"===e.operator||"<="===e.operator,s)if(y&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===y.major&&e.semver.minor===y.minor&&e.semver.patch===y.patch&&(y=!1),">"===e.operator||">="===e.operator){if(m=u(s,e,r),m===e&&m!==s)return!1}else if(">="===s.operator&&!o(s.semver,String(e),r))return!1;if(h)if(w&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===w.major&&e.semver.minor===w.minor&&e.semver.patch===w.patch&&(w=!1),"<"===e.operator||"<="===e.operator){if(f=d(h,e,r),f===e&&f!==h)return!1}else if("<="===h.operator&&!o(h.semver,String(e),r))return!1;if(!e.operator&&(h||s)&&0!==p)return!1}return!(s&&E&&!h&&0!==p)&&(!(h&&g&&!s&&0!==p)&&(!y&&!w))},u=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n>0?e:n<0||">"===t.operator&&">="===e.operator?t:e},d=(e,t,r)=>{if(!e)return t;const n=a(e.semver,t.semver,r);return n<0?e:n>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let s=!1;e:for(const n of e.set){for(const e of t.set){const t=h(n,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},77631:(e,t,r)=>{const n=r(78311);e.exports=(e,t)=>new n(e,t).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")))},13874:(e,t,r)=>{const n=r(78311);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}}}]);