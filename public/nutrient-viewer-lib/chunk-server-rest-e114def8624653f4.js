/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
"use strict";(globalThis.webpackChunkNutrientViewer=globalThis.webpackChunkNutrientViewer||[]).push([[269],{33393:(t,a,e)=>{e.d(a,{RESTProvider:()=>m});var s=e(67136),o=e(49568),i=e(85409),n=e(97881),r=e(82987);class d extends(o.mS({alreadyLoadedPages:(0,o.T5)(),serverURL:null,authPayload:null,isLoaded:!1,isFormsEnabled:!0,loadBookmarksPromise:null,ignoredFormFieldNames:null})){}var l=e(60156),c=e(78236),h=e(85410);class m{constructor(t,a,e){let{isFormsEnabled:o}=e;(0,s.A)(this,"canCreateBackendOrphanWidgets",!0),(0,s.A)(this,"setDocumentHandleConflictCallback",(()=>{})),this.state=new d({serverURL:t,authPayload:a,isFormsEnabled:o}),this._setReadStateCallbacksPromise=new Promise((t=>{this._setReadStateCallbacksPromiseResolve=t}))}async load(){return this.state=this.state.set("isLoaded",!0),this.state.isFormsEnabled&&await this._initializeFormFieldValues(),this}destroy(){}setReadStateCallbacks(t){this._readStateCallbacks=t,this._setReadStateCallbacksPromiseResolve?.()}setAnnotationCallbacks(t){this.annotationCallbacks=t}setBookmarkCallbacks(t){this.bookmarkCallbacks=t}setFormFieldValueCallbacks(t){this.formFieldValueCallbacks=t}async createAnnotation(t,a){this._verifyLoaded();const{id:e,...s}=(0,n.eq)(t),o={id:e,content:s},r=await this._request("/annotations","POST",o);if(200!==r.status)throw new i.uE("Nutrient Document Engine returned an error, when saving an annotation.");if("attachment_missing"===(await r.json()).error){const t=function(t,a){const e=new FormData;return e.append("annotation",JSON.stringify(t)),a.forEach(((t,a)=>{a&&t.data&&e.append(a,t.data)})),e}(o,a);if(200!==(await this._request("/annotations","POST",t)).status)throw new i.uE("Nutrient Document Engine returned an error, when saving an annotation attachment.")}}async updateAnnotation(t){this._verifyLoaded();const{id:a,...e}=(0,n.eq)(t);await this._request(`/annotations/${a}`,"PUT",{id:a,content:e})}async deleteAnnotation(t){this._verifyLoaded(),await this._request(`/annotations/${t.id}`,"DELETE")}async createBookmark(t){this._verifyLoaded(),await this.loadBookmarks();const{id:a,...e}=(0,r.U)(t);if(200!==(await this._request("/bookmarks","POST",{id:a,content:e})).status)throw new i.uE("Nutrient Document Engine returned an error, when saving an bookmark.")}async updateBookmark(t){this._verifyLoaded(),await this.loadBookmarks();const{id:a,...e}=(0,r.U)(t);await this._request(`/bookmarks/${a}`,"PUT",{id:a,content:e})}async deleteBookmark(t){this._verifyLoaded(),await this.loadBookmarks(),await this._request(`/bookmarks/${t}`,"DELETE")}async setFormFieldValue(t){this._verifyLoaded();const a={id:(0,l.B)(t),content:(0,n.cA)(t)};await this._request("/form-field-values","POST",{formFieldValues:[a]})}async createFormFieldValue(){}async deleteFormFieldValue(){}async loadAnnotationsForPageIndex(t){if(this._verifyLoaded(),this.state.alreadyLoadedPages.has(t))await this.state.alreadyLoadedPages.get(t);else try{const a=this._request(`/page-${t}-annotations`,"GET").then((t=>t.json())).catch((t=>{throw t}));this.state=this.state.setIn(["alreadyLoadedPages",t],a);const e=await a;this.state=this.state.setIn(["alreadyLoadedPages",t],Promise.resolve());const s=(0,o.B8)().withMutations((t=>{e.annotations.forEach((a=>{try{t.push((0,n.h8)(a.id,a.content))}catch(t){(0,i.pq)(`Skipped creating annotation #${a.id} from payload because an error occurred while deserializing.`,String(a.content)),(0,i.pq)(t)}}))}));s.size>0&&((0,i.V1)(this.annotationCallbacks),this.annotationCallbacks.createAnnotations(s,(0,o.T5)(),c.n))}catch(t){this._handleError(t,"annotations")}}async loadBookmarks(){if(this._verifyLoaded(),this.state.loadBookmarksPromise)await this.state.loadBookmarksPromise;else try{const t=this._request("/bookmarks","GET").then((t=>t.json())).then((t=>t.data)).catch((t=>{throw t}));this.state=this.state.set("loadBookmarksPromise",t);const a=await t;this.state=this.state.set("loadBookmarksPromise",Promise.resolve()),(0,i.V1)(Array.isArray(a.bookmarks),"Unexpected reply from bookmarks endpoint.");const e=(0,o.B8)().withMutations((t=>{a.bookmarks.forEach((a=>{try{t.push((0,r.r)(a.id,a.content))}catch(t){(0,i.pq)(`Skipped creating bookmark #${a.id} from payload because an error occurred while deserializing.`,a),(0,i.pq)(t)}}))}));e.size>0&&((0,i.V1)(this.bookmarkCallbacks),this.bookmarkCallbacks.createBookmarks(e,c.n))}catch(t){this._handleError(t,"bookmarks")}}async syncChanges(){}async _initializeFormFieldValues(){const t=await this._request("/form-field-values","GET"),a=await t.json();(0,i.V1)(Array.isArray(a.formFieldValues),"Unexpected reply from form-values endpoint.");const e=(0,o.B8)(a.formFieldValues.map((t=>{let{content:a}=t;try{return(0,n.R5)(a)}catch(t){return(0,i.pq)(`Skipped form field value ${a.name} from payload because an error occurred while deserializing.`,String(a)),(0,i.pq)(t),null}})).filter(Boolean));(0,i.V1)(this.formFieldValueCallbacks),this.state.ignoredFormFieldNames&&this.state.ignoredFormFieldNames.size?this.formFieldValueCallbacks.setFormFieldValues(e.filter((t=>!this.state.ignoredFormFieldNames?.includes(t.name)))):this.formFieldValueCallbacks.setFormFieldValues(e)}_handleError(t,a){(0,i.z3)(`Loading or updating ${a} failed:\n\n${t.message}`)}_request(t,a,e){(0,i.V1)(null!=this.state.authPayload,"Cannot call request without authPayload");const s=e instanceof FormData||"object"!=typeof e?null:{"Content-Type":"application/json"},o={"X-PSPDFKit-Token":this.state.authPayload.token,"PSPDFKit-Platform":"web","PSPDFKit-Version":(0,h._q)(),...s};return fetch(`${this.state.serverURL}${t}`,{method:a,headers:o,body:e instanceof FormData?e:"object"==typeof e?JSON.stringify(e):void 0,credentials:"include"})}_verifyLoaded(){if(!this.state.isLoaded)throw new Error("not loaded")}setIgnoredFormFieldNames(t){this.state=this.state.set("ignoredFormFieldNames",t)}async updateTabOrder(){throw new i.uE("Tab order is not supported on this backend.")}async setTabOrder(){throw new i.uE("setTabOrder is not supported on this backend.")}}}}]);