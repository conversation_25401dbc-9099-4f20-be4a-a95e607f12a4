/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
"use strict";(globalThis.webpackChunkNutrientViewer=globalThis.webpackChunkNutrientViewer||[]).push([[362],{63993:(t,e,n)=>{n.r(e),n.d(e,{corePool:()=>l.mG,customFontsPromiseRef:()=>_,default:()=>b,loadModule:()=>D,normalizeCoreOptions:()=>l.DO,validateStandaloneConfiguration:()=>l.mn});var a=n(49568),s=n(85409),i=n(55994),o=n(41204),r=n(85410),l=n(20873),u=n(37506),c=n(85553),d=n(89055),m=n(68322),h=n(24983),f=n(84318),p=n(33075);async function g(t,e){const n=await fetch(t,{credentials:"same-origin"}).finally((()=>{e?.()})),a=await n.arrayBuffer();return{isFullyAvailable:!0,fullDocument:a,contentType:n.headers?.get("Content-Type")||(0,p.oj)(a)}}async function y(t,e,n,a){if(t instanceof ArrayBuffer)return{isFullyAvailable:!0,fullDocument:t,contentType:(0,p.oj)(t)};(0,s.V1)("string"==typeof t,"Document is not a string");let o=!1;const{linearizedDocument:r,serverSupportsRangeRequests:l}=await(async()=>{if(n&&function(t){if(t.startsWith("/"))return!0;try{const e=new URL(t);return"http:"===e.protocol||"https:"===e.protocol}catch(t){return!1}}(t)){const n=await e.openLinearizedDocument(i.EA);return(0,s.V1)(n,"Linearized document is not available"),{linearizedDocument:n,serverSupportsRangeRequests:await n.initialize(t)}}return{linearizedDocument:null,serverSupportsRangeRequests:!1}})();try{if(!l)return o=!0,g(t,a);(0,s.V1)(r,"Linearized document is not available"),r.startDownload();if(!await r.waitUntilIsLinearizedIsKnown())return r.destroy(),o=!0,g(t,a);a?.();const e=await r.waitUntilDocumentInfoAvailable();return e?(a?.(),{isFullyAvailable:!1,documentResponse:e,contentType:"application/pdf"}):(r.destroy(),o=!0,g(t,a))}catch(e){if(r?.destroy(),o)throw e;return g(t,a)}}let w;class b extends l.Ay{constructor(t){const e=t.baseUrl||(0,r.$_)(window.document),n=t.baseCoreUrl||e,a=t.baseProcessorEngineUrl||e,i={...t,baseUrl:e,baseCoreUrl:n,baseProcessorEngineUrl:a};if("string"!=typeof i.baseUrl)throw new s.uE("`baseUrl` is mandatory and must be a valid URL, e.g. `https://example.com/`");if("string"!=typeof i.document&&!(i.document instanceof ArrayBuffer))throw new s.uE("document must be either an URL to a supported document type (PDF and images), e.g. `https://example.com/document.pdf`, or an `ArrayBuffer`");if(w&&w!==i.licenseKey)throw new s.uE("Trying to re-use instance with a different licenseKey.\nUnfortunately we only allow one licenseKey per instance.\nPlease contact support for further assistance.");if("string"==typeof i.licenseKey&&i.licenseKey.startsWith("TRIAL-"))throw new s.uE("You're using the npm key instead of the license key. This key is used to download the PSPDFKit for Web package via the node package manager.\n\nLeave out the license key to activate as a trial.");super(i),this.destroyed=!1,this.activeConfiguration=t}async load(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=.2;t.progressCallback&&t.progressCallback("loading",e),this._isPDFJavaScriptEnabled=t.isPDFJavaScriptEnabled,"string"==typeof this._state.document&&(0,p.XC)(this._state.baseUrl,this._state.document,this._state.productId);const n=await D(this.client,this._state).finally((()=>{e+=.3,t.progressCallback&&t.progressCallback("loading",e)})),i=y(this._state.document,this.corePDFDocument,this._state.allowLinearizedLoading,(()=>{e+=.3,t.progressCallback&&t.progressCallback("loading",e)}));(0,s.V1)(n);const{features:r,signatureFeatureAvailability:l,capabilities:u}=n;if(this._state.productId===m.v.SharePoint&&"string"==typeof this._state.document&&Array.isArray(n.afu)){const t=new URL(this._state.document,this._state.baseUrl);if(!n.afu.some((e=>t.hostname.match(e))))throw new s.uE(`The document origin ${t.hostname} is not authorized.`)}const f=l===c.g.ELECTRONIC_SIGNATURES&&(0,d.UX)(r)&&this._state.forceLegacySignaturesFeature?c.g.LEGACY_SIGNATURES:l;this._state=this._state.set("features",(0,a.B8)(r)).set("signatureFeatureAvailability",f),w=this._state.licenseKey;const g=await i;let b=null;if(g.isFullyAvailable){let e=g.fullDocument.slice(0),n=null;if((0,p.C5)(g.contentType))try{this.destroyed?(e=null,b=await new Promise((()=>{}))):(b=await this.client.openDocument(g.fullDocument,{password:t.password,initialPageIndex:"number"==typeof t.initialPageIndex?t.initialPageIndex:0,formsConfiguration:this._formsConfiguration}),e=null)}catch(t){"INVALID_PASSWORD"===t.message&&this._state.document instanceof ArrayBuffer&&(this._state=this._state.set("document",t.callArgs[0])),"IMAGE_DOCUMENTS_NOT_LICENSED"===t.message&&(t.message="The image documents feature is not enabled for your license key. Please contact support or sales to purchase the UI module for your product."),n=t}else u.includes("GdPictureWASM")||(n=new s.uE("File not in PDF format or corrupted."));if(u.includes("GdPictureWASM")&&(n instanceof s.uE&&n.message.includes("File not in PDF format or corrupted.")||!(0,p.C5)(g.contentType))){(0,s.V1)(e);let a,i=(0,h.jU)();try{i||(i=(0,h.NY)({baseUrl:this._state.baseProcessorEngineUrl,mainThreadOrigin:this._state.appName||(0,o.D5)()||window.location.origin,licenseKey:this._state.licenseKey||void 0,customFonts:this._state.customFonts||void 0,dynamicFonts:this._state.dynamicFonts||void 0,fontSubstitutions:this._state.fontSubstitutions,processorEngine:this._state.processorEngine}),(0,h.Pm)(i)),a=await i,(0,s.V1)(a);const n=await a.toPdf(e,null,t.officeConversionSettings);b=await this.client.openDocument(n,{password:t.password,initialPageIndex:"number"==typeof t.initialPageIndex?t.initialPageIndex:0,formsConfiguration:this._formsConfiguration})}catch(t){throw"INVALID_PASSWORD"===t.message&&this._state.document instanceof ArrayBuffer&&(this._state=this._state.set("document",n.callArgs[0])),"IMAGE_DOCUMENTS_NOT_LICENSED"===t.message&&(t.message="The image documents feature is not enabled for your license key. Please contact support or sales to purchase the UI module for your product."),t}finally{e=null,a?.destroy(),(0,h.Pm)(null)}}else if(n)throw n;(0,s.V1)(b),await this.afterDocumentLoaded(b)}else b=g.documentResponse;return this._state=this._state.set("documentResponse",b),{features:this._state.features,signatureFeatureAvailability:this._state.signatureFeatureAvailability,hasPassword:!!t.password,password:t.password,allowedTileScales:"all",evaluation:n.evaluation,linearizedLoading:!g.isFullyAvailable,documentResponse:b}}destroy(){this.destroyed=!0,super.destroy()}getCustomFontsPromise(){return _}async afterDocumentLoaded(t){if(this._isPDFJavaScriptEnabled&&(this._initialChanges=await this.client.enablePDFJavaScriptSupport()),this._XFDF&&await this.client.importXFDF(this._XFDF.source,this._XFDF.keepCurrentAnnotations,this._XFDF.ignorePageRotation),this._instantJSON&&this._instantJSON.pdfId&&t.ID.permanent){const e=this._instantJSON.pdfId,n=t.ID;if(e.permanent!==n.permanent)throw new s.uE("Could not instantiate from Instant JSON: Permanent PDF ID mismatch.\nPlease use the same PDF document that was used to create this Instant JSON.\nFor more information, please visit: https://www.nutrient.io/guides/web/importing-exporting/instant-json/");if(e.changing!==n.changing)throw new s.uE("Could not instantiate from Instant JSON: Changing PDF ID mismatch.\nPlease use the same revision of this PDF document that was used to create this Instant JSON.\nFor more information, please visit: https://www.nutrient.io/guides/web/importing-exporting/instant-json/")}if(this._trustedCAsCallback)try{const t=await this._trustedCAsCallback();if(!Array.isArray(t))throw new s.uE("Certificates response must be an array");if(t.some((t=>!(t instanceof ArrayBuffer)&&"string"!=typeof t)))throw new s.uE("All certificates must be passed as ArrayBuffer (DER) or string (PEM)");await this.client.loadCertificates(t.map(u.PI))}catch(t){throw new s.uE(`Could not retrieve certificates for digital signatures validation: ${t.message}.`)}}async waitUntilFullyLoaded(t){return this.corePDFDocument.linearizedDocument.waitUntilDownloaded().then((async()=>{await this.corePDFDocument.normallyOpenLinearizedDocument(this,{password:t,initialPageIndex:0,formsConfiguration:this._formsConfiguration}),await this.afterDocumentLoaded(this._state.documentResponse)}))}async getDocumentBytes(){if(this._state.document instanceof ArrayBuffer)return this._state.document;if("string"==typeof this._state.document){const t=await fetch(this._state.document);if(!t.ok)throw new s.uE(`Failed to fetch document: ${t.statusText}`);return t.arrayBuffer()}throw new s.uE("Document bytes are not available")}}const _={current:void 0};async function D(t,e){_.current=_.current||(e.customFonts?(0,f.eY)(e.customFonts):void 0);const n=(0,r.f)(e.appName);return t.loadNativeModule(e.baseCoreUrl,{mainThreadOrigin:n,disableWebAssemblyStreaming:e.disableWebAssemblyStreaming,enableAutomaticLinkExtraction:e.enableAutomaticLinkExtraction,overrideMemoryLimit:e.overrideMemoryLimit,workerSpawnerFn:()=>(0,i.pj)(e.inlineWorkers)}).then((async()=>t.load(e.baseCoreUrl,e.licenseKey,{mainThreadOrigin:n,..._.current?{customFonts:await _.current}:null,dynamicFonts:e.dynamicFonts,productId:e.productId,featureFlags:e.enableCoreUnifiedComments?["UnifiedComments"]:void 0})))}}}]);