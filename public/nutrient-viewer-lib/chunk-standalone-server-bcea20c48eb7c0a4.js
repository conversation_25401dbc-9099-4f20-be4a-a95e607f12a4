/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 *
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 *
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USA<PERSON> IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 *
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
"use strict";(globalThis.webpackChunkNutrientViewer=globalThis.webpackChunkNutrientViewer||[]).push([[42],{96419:(e,t,n)=>{n.d(t,{N:()=>y});var a=n(7891),i=n(59501),s=n(85409),o=n(49568),r=n(89574),c=n(32784),l=n(9939),d=n(97881);function u(e,t,n){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}function m(e,t){return e.get(p(e,t))}function h(e,t,n){return e.set(p(e,t),n),n}function p(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}var f=new WeakMap,g=new WeakMap;class y{constructor(e,t){u(this,f,void 0),u(this,g,null),this.pdfLibrary=new a.T7.Qz(e),h(f,this,this.pdfLibrary.openDocument(t))}get rawDocument(){return m(f,this)}get linearizedDocument(){return(0,s.V1)(m(g,this),"Document is not linearized."),m(g,this)}async openLinearizedDocument(e){return h(g,this,await this.pdfLibrary.openLinarizedDocument(e)),h(f,this,m(g,this).document),m(g,this)}async normallyOpenLinearizedDocument(e,t){(0,s.V1)(m(g,this),"Document is not linearized."),m(g,this).destroy(),h(g,this,null),await e.client.openCreateFilePathDocument(t)}destroy(){(0,i.nl)(this.rawDocument.ctx)}renderTile(e,t,n,i,s,o){let c;o&&(c={annotations:o.annotations.filter(r.mH).map(d.eq).toJS().map((e=>({content:e}))),formFieldValues:o.formFieldValues.map(d.cA).toJS(),formFields:o.formFields.map(d.T7).toJS(),signatures:o.signatures||[],attachments:o.attachments});const u=this.rawDocument.renderer.renderTile(e,t,n,{renderForPrinting:i,renderText:s,priority:t.width===n.width&&t.height===n.height?a.MX.bx.High:a.MX.bx.Normal},c).then((e=>(0,l.vB)({buffer:e.buffer.buffer,width:n.width,height:n.height,format:e.format}).then((e=>{if(null===e)throw new Error("Image handle is null");return e}))));return{promise:u.promise,cancel:u.cancel}}getTextLines(e){return this.rawDocument.text.getTextLines(e).then((t=>(0,c.K0)({textLines:t},e)))}getContentTree(e){return this.rawDocument.text.getContentTree(e).then((t=>{let n=[],a=0,i=0;return n=t.reduce(((t,n)=>{let{nodes:s}=n;const o=(0,c.l1)(s,e,t.length,a,i);return a+=o.reduce(((e,t)=>{let{content:n}=t;return e+(0,c.Y_)(n).size}),0),i+=o.reduce(((e,t)=>{let{content:n}=t;return e+(0,c.M3)(n).size}),0),t.concat(o)}),[]),(0,o.B8)(n)}))}compareText(e){const t={data:e.originalDocument.arrayBuffer,password:e.originalDocument.password,pageIndexes:e.originalDocument.pageIndexes},n={data:e.changedDocument.arrayBuffer,password:e.changedDocument.password,pageIndexes:e.changedDocument.pageIndexes};return a.MX.fu.compareText(this.rawDocument.ctx,t,n,{numberOfContextWords:e.comparisonOperation.options?.numberOfContextWords}).then((e=>e))}}},72262:(e,t,n)=>{n.d(t,{K:()=>d});var a=n(67136),i=n(49568),s=n(85409),o=n(76117),r=n(89574),c=n(28650),l=n(69371);class d{constructor(){(0,a.A)(this,"attachmentsCache",(0,i.T5)()),(0,a.A)(this,"cachedAPStreams",(0,i.T5)()),(0,a.A)(this,"pageAPStreamsPromises",(0,i.T5)()),(0,a.A)(this,"annotationAPStreamPromises",(0,i.T5)()),(0,a.A)(this,"_cachedRenderedAnnotations",(0,i.uY)()),(0,a.A)(this,"_objectURLs",{}),(0,a.A)(this,"_makeEnqueuedRelease",((e,t)=>{if("IMG"===e?.element?.tagName){const n=e.element.src;if(!n)return;"number"==typeof this._objectURLs[n]?this._objectURLs[n]++:this._objectURLs[n]=1;const a=e.release;e.release=()=>{this._objectURLs[n]--,this._objectURLs[n]<=0&&(a?.(),this._cachedRenderedAnnotations.has(t)&&(this._cachedRenderedAnnotations=this._cachedRenderedAnnotations.delete(t)),delete this._objectURLs[n])}}})),(0,a.A)(this,"_cacheableRenderedAnnotationSetExpire",(e=>{const t=this._cachedRenderedAnnotations.get(e);(0,s.V1)(t),t.timeout&&clearTimeout(t.timeout);const n=setTimeout((()=>{this._cachedRenderedAnnotations.has(e)&&(this._cachedRenderedAnnotations=this._cachedRenderedAnnotations.delete(e))}),3e4);this._cachedRenderedAnnotations=this._cachedRenderedAnnotations.update(e,(e=>({...e,timeout:n})))})),(0,a.A)(this,"_addCachedRenderedAnnotation",((e,t)=>{let{width:n,height:a,noZoom:i,APStreamPromise:s}=t;if(this._cachedRenderedAnnotations.size>1e3){const e=this._cachedRenderedAnnotations.keySeq().first();e&&(this._cachedRenderedAnnotations=this._cachedRenderedAnnotations.delete(e))}this._cachedRenderedAnnotations=this._cachedRenderedAnnotations.set(e,{width:n,height:a,noZoom:i,APStreamPromise:s}),this._cacheableRenderedAnnotationSetExpire(e)}))}getAnnotationFormFieldAndValue(e){const t=this.provider;(0,s.V1)(t instanceof t.constructor,"Backend can only use backend annotation provider");const n=e instanceof o.sb?t._readStateCallbacks?.getFormFieldByName(e.formFieldName):null;return{formField:n,formFieldValue:!n||n instanceof o.Vw?null:new o.E2({name:n.name,value:void 0!==n.formattedValue?n.formattedValue:"string"==typeof n.value?n.value:n.values})}}getAnnotationAvailableVariants(e){const t=this.provider;(0,s.V1)(t instanceof t.constructor,"Backend can only use backend annotation provider");return[...t._readStateCallbacks?.getAvailableVariants(e)||[],"normal"]}cachedRenderAnnotation(e,t,n,a,i,o){const c=this.provider;(0,s.V1)(c instanceof c.constructor,"Backend can only use backend annotation provider");const{formField:l,formFieldValue:d}=this.getAnnotationFormFieldAndValue(e);if(!(0,r.lG)(e,l))return this.renderAnnotation(e,d,t,n,a,i,o);const u=this.getAnnotationAvailableVariants(e);let m=!1,h=()=>{m=!0};return{promise:new Promise((async(s,r)=>{const c=t=>{const n=this.annotationAPStreamPromises.get(e.id);n&&(this.annotationAPStreamPromises=this.annotationAPStreamPromises.delete(e.id),n(t))},l=this.annotationAPStreamPromises.get(e.id);this.annotationAPStreamPromises=this.annotationAPStreamPromises.set(e.id,s),l&&l(null);try{const r=this.pageAPStreamsPromises.get(e.pageIndex);if(!r){const t=new Promise((t=>{this.annotationAPStreamPromises=this.annotationAPStreamPromises.set(e.id,t)}));return void s(await t)}await r;const l=this.cachedAPStreams.get(e.pageIndex);if(l){const t=l?l.get(e.id):null;if(t)return void c(this.getAPStream(t,i))}const{promise:p,cancel:f}=this.renderAPStream(e,d,t,n,a,u,i,o);if(m)c(null);else if(h=f,u.length>1){const t=await Promise.all(p.map((e=>e.promise)));c(t[u.indexOf(i||"normal")]),t.some(Boolean)&&this.cacheAPStream(u.reduce(((e,n,a)=>t[a]?{...e,[n]:t[a]}:e),{}),e)}else{const t=await p,n=t?this.getAPStream(t,i):null;c(n),n&&this.cacheAPStream(t,e)}}catch(e){r(e)}})),cancel:h}}cacheAPStream(e,t){let n=this.cachedAPStreams.get(t.pageIndex);n||(this.cachedAPStreams=this.cachedAPStreams.set(t.pageIndex,(0,i.T5)()),n=this.cachedAPStreams.get(t.pageIndex)),this.cachedAPStreams=this.cachedAPStreams.setIn([t.pageIndex,t.id],e)}clearAllPageAPStreams(e){const t=this.cachedAPStreams.get(e);t&&(t.forEach((e=>{this.releaseAPStream(e)})),this.cachedAPStreams=this.cachedAPStreams.delete(e)),this.pageAPStreamsPromises=this.pageAPStreamsPromises.delete(e)}clearPageAPStreams(e,t){const n=this.cachedAPStreams.get(e);n&&(n.filter(((e,n)=>t.has(n))).forEach((e=>{this.releaseAPStream(e)})),this.cachedAPStreams=this.cachedAPStreams.updateIn([e],(0,l.C$)((e=>e.filter(((e,n)=>!t.has(n)))))))}getAPStream(e,t){return e instanceof c.A?e:e?.[t||"normal"]||null}renderAPStream(e,t,n,a,i,s,o,r){if(s.length>1){const o=s.map((s=>this.renderAnnotation(e,t,n,a,i,"normal"!==s?s:void 0,r)));return{promise:o,cancel:()=>{o.forEach((e=>{e.cancel()}))}}}return this.renderAnnotation(e,t,n,a,i,o,r)}releaseAPStream(e){e instanceof c.A?e.release():Object.values(e).forEach((e=>{e.release()}))}isVisuallyIdenticalAnnotationCached(e){let{annotation:t,providedVisualAnnotationIndex:n,width:a,height:i}=e;if(!(0,r.cu)(t))return!1;const o=n||(0,r.sS)(t);if(!this._cachedRenderedAnnotations.has(o))return!1;const c=this._cachedRenderedAnnotations.get(o);return(0,s.V1)(c),(0,r.Ab)({noZoom:c.noZoom&&t.noZoom,cachedRenderedAnnotationWidth:c.width,cachedRenderedAnnotationHeight:c.height,testAnnotationWidth:a,testAnnotationHeight:i})}cachedRenderedAnnotationDiscardablePromise(e,t){let{promise:n,resolve:a,reject:i}=t;const o=this._cachedRenderedAnnotations.get(e);return(0,s.V1)(o),o.APStreamPromise.then((t=>{let n;return t&&(n=new c.A(t.element.cloneNode(!0),t.release),this._makeEnqueuedRelease(n,e),this._cacheableRenderedAnnotationSetExpire(e)),a(n),n})).catch(i),{promise:n,cancel:()=>{}}}clearRenderedAnnotationsCache(){this._cachedRenderedAnnotations.forEach((e=>{let{APStreamPromise:t,timeout:n}=e;t.then((e=>{e?.release()})),n&&clearTimeout(n)})),this._cachedRenderedAnnotations=(0,i.T5)(),this._objectURLs={}}}},20873:(e,t,n)=>{n.d(t,{mG:()=>oe,Ay:()=>re,DO:()=>ce,mn:()=>le});var a=n(67136),i=n(55994),s=n(45646),o=n(85409),r=n(49568),c=n(80018),l=n(63097),d=n(37361),u=n(64337),m=n(20546),h=n(87374),p=n(68322),f=n(37094),g=n(95248),y=n(40927),b=n(76117),A=n(83145),S=n(83647),w=n(40527),P=n(70631),_=n(50960),F=n(44439),v=n(89574),k=n(25888),C=n(9939),D=n(15168),I=n(83720),R=n(14511),T=n(41204),x=n(77270),V=n(47159),O=n(24983),L=n(96419);class B{constructor(e,t){this.identifier=e,this.callback=t}request(){return this.callback()}}var E=n(41477),U=n(5293),j=n(97881),N=n(72262),z=n(7737),M=n(82987),q=n(78625),K=n(74111),W=n(78236);class $ extends((0,r.mS)({alreadyLoadedPages:(0,r.T5)(),isLoaded:!1,isDestroyed:!1})){}const J={skippedPdfObjectIds:[],skippedPdfBookmarkIds:[],annotations:[],bookmarks:[],formFieldValues:[],formFields:[],attachments:{}};class G{constructor(e,t){(0,a.A)(this,"_state",new $),(0,a.A)(this,"_formFieldsLoadedPromise",null),(0,a.A)(this,"_objectCreationPromises",(0,r.T5)()),(0,a.A)(this,"_loadBookmarksPromise",null),(0,a.A)(this,"_commentsLoadedPromise",null),(0,a.A)(this,"canCreateBackendOrphanWidgets",!1),(0,a.A)(this,"updateTabOrderTimeout",null),(0,a.A)(this,"pagesTabOrderToUpdate",(0,r.NZ)()),this._core=e,this._json=t?(0,o.$y)(t):null,this._setReadStateCallbacksPromise=new Promise((e=>{this._setReadStateCallbacksPromiseResolve=e}))}async load(){if(this._state=this._state.set("isLoaded",!0),!this._formFieldCallbacks&&await this._loadFormFieldValues(),!this._json)return this;await this._core.importInstantJSON({...J,...this._json}),(0,o.V1)(this._json);const{annotations:e,attachments:t}=this._json;if(this._isDestroyed()||!t||0===Object.entries(t).length)return this;if(e)for(let n=0;n<e.length;n++){let a=null;const i=e[n];if("imageAttachmentId"in i&&i.imageAttachmentId){const e=t?t[i.imageAttachmentId]:null;if(e)try{a=(0,q.lj)(atob(e.binary),e.contentType),(0,o.V1)(this._annotationCallbacks),this._annotationCallbacks.createAttachment(i.imageAttachmentId,a)}catch(e){(0,o.pq)(`Skipped attachment with id ${i.imageAttachmentId} from payload because an error occurred while converting the binary image to blob.`),(0,o.pq)(e)}}}return this}destroy(){this._state=this._state.set("isDestroyed",!0),this._annotationCallbacks=null,this._readStateCallbacks=null,this._bookmarkCallbacks=null,this._formFieldCallbacks=null,this._formFieldValueCallbacks=null,this._commentCallbacks=null}setReadStateCallbacks(e){this._readStateCallbacks=e,this._setReadStateCallbacksPromiseResolve?.()}setAnnotationCallbacks(e){this._annotationCallbacks=e}setBookmarkCallbacks(e){this._bookmarkCallbacks=e}setFormFieldCallbacks(e){this._formFieldCallbacks=e}setFormFieldValueCallbacks(e){this._formFieldValueCallbacks=e}setCommentCallbacks(e){this._commentCallbacks=e}createComment(e,t,n){return this._core.applyComments(t.map((e=>X(e,n))).toArray())}updateComment(e,t,n){return this._core.applyComments(t.map((e=>X(e,n))).toArray())}deleteComment(e,t,n){return this._core.applyComments(t.map((e=>X(e,n))).toArray())}async loadComments(){return this._commentsLoadedPromise||(this._commentsLoadedPromise=this._loadComments()),this._commentsLoadedPromise}async _loadComments(){this._verifyLoaded();const e=await this._core.getComments()??[],t=(0,r.B8)(e.map((e=>{let t;return t=e.pdfObjectId?e.id||e.pdfObjectId?.toString():(0,v.K1)(),(0,j.XL)(t,e)})));await Promise.all(t.map((e=>"number"==typeof e.pageIndex&&this.loadAnnotationsForPageIndex(e.pageIndex))).filter(Boolean).toArray()),this._commentCallbacks?.createComments(t,W.n),this._commentsLoadedPromise=Promise.resolve()}async updateTabOrder(e){this.pagesTabOrderToUpdate=this.pagesTabOrderToUpdate.add(e),this.updateTabOrderTimeout&&clearTimeout(this.updateTabOrderTimeout),this.updateTabOrderTimeout=setTimeout((async()=>{const e=this.pagesTabOrderToUpdate.toArray();if(this.pagesTabOrderToUpdate=(0,r.NZ)(),this._isDestroyed())return;const t=await Promise.all(e.map((e=>this._core.getTabOrder(e))));this._isDestroyed()||((0,o.V1)(this._annotationCallbacks),(0,z.vA)((()=>{e.forEach(((e,n)=>{this._annotationCallbacks?.setPageTabOrder(e,t[n])}))})))}),1e3)}async setTabOrder(e,t){return this._core.setTabOrder(e,t)}async createAnnotation(e,t){this._verifyLoaded();const n=t.find(((t,n)=>((0,o.V1)("imageAttachmentId"in e,"Annotation must have imageAttachmentId."),n===e.imageAttachmentId))),a=await this._core.createAnnotation((0,j.eq)(e),n?n.data:null);"number"!=typeof a||"number"!=typeof e.pdfObjectId||e.pdfObjectId===a||this._isDestroyed()||((0,o.V1)(this._annotationCallbacks),this._annotationCallbacks.updateAnnotations((0,r.B8)([e.set("pdfObjectId",a)]))),await this.updateTabOrder(e.pageIndex)}async updateAnnotation(e){this._verifyLoaded(),await this._core.updateAnnotation((0,j.eq)(e)),await this.updateTabOrder(e.pageIndex)}deleteAnnotation(e){return this._verifyLoaded(),this._core.deleteAnnotation((0,j.eq)(e))}createBookmark(e){return this._verifyLoaded(),this._core.createBookmark((0,M.U)(e))}updateBookmark(e){return this._verifyLoaded(),this._core.updateBookmark((0,M.U)(e))}deleteBookmark(e){return this._verifyLoaded(),this._core.deleteBookmark(e)}createFormField(e){this._verifyLoaded(),(0,o.V1)(this._readStateCallbacks);const t=this._readStateCallbacks.getFormFieldWidgets(e);return this._core.createFormField((0,j.T7)(e),t.map((e=>(0,j.eq)(e))).toArray()).then((async e=>{(0,z.vA)((()=>{e.forEach(((e,n)=>{const a=t.get(n);(0,o.V1)(a),(0,o.V1)(this._annotationCallbacks),"number"!=typeof e||"number"!=typeof a.pdfObjectId||a.pdfObjectId===e||this._isDestroyed()||this._annotationCallbacks.updateAnnotations((0,r.B8)([a.set("pdfObjectId",e)]))}))}))}))}updateFormField(e){this._verifyLoaded(),(0,o.V1)(this._readStateCallbacks);const t=this._readStateCallbacks.getFormFieldWidgets(e);return this._core.updateFormField((0,j.T7)(e),t.map((e=>(0,j.eq)(e))).toArray())}deleteFormField(e){return this._verifyLoaded(),this._core.deleteFormField((0,j.T7)(e))}loadFormFields(){return this._formFieldsLoadedPromise||(this._formFieldsLoadedPromise=this._loadFormFields()),this._formFieldsLoadedPromise}async _loadFormFields(){this._verifyLoaded();const e=await this._core.readFormJSONObjects();if(this._isDestroyed())return;let t=(0,r.B8)(),n=(0,r.B8)().withMutations((n=>{e.forEach((e=>{const{formField:a,widgets:i,value:s}=e;try{let e;e=a.pdfObjectId?a.pdfObjectId.toString():(0,v.K1)();const r=(0,j.mh)(e,a);(0,o.V1)(this._readStateCallbacks),this._readStateCallbacks.isFormFieldInState(r.name)||n.push(r.set("value",s)),i.forEach((e=>{let n;n=e.pdfObjectId?e.id||e.pdfObjectId.toString():(0,v.K1)(),(0,o.V1)(this._readStateCallbacks),t=t.push((0,j.h8)(n,e))}))}catch(e){(0,o.pq)(`Skipped creating form field #${a.pdfObjectId} from payload because an error occurred while deserializing.`),(0,o.pq)(e)}}))}));const a={},i={};t.forEach((e=>{i[e.id]||(i[e.id]=[]),i[e.id].push(e.pdfObjectId)})),t=t.map((e=>{if(i[e.id].filter((t=>t!==e.pdfObjectId)).length>0&&e.pdfObjectId?.toString()!==e.id||this._readStateCallbacks?.isAnnotationInState(e.id)){const t=(0,v.K1)();return a[e.formFieldName]?a[e.formFieldName].push({[e.id]:t}):a[e.formFieldName]=[{[e.id]:t}],n=n.map((n=>n.name===e.formFieldName?n.update("annotationIds",(n=>n?.map((n=>n===e.id?t:n)))):n)),e.set("id",t)}return e})),Object.keys(a).forEach((e=>{const a=n.find((t=>t.name===e));(0,o.V1)(a);const i=t.filter((t=>t.formFieldName===e)).toArray().map((e=>(0,j.eq)(e)));this._core.updateFormField((0,j.T7)(a),i)})),n.size>0&&!this._isDestroyed()&&((0,o.V1)(this._formFieldCallbacks),this._formFieldCallbacks.createFormFields(n,W.n)),await this._loadFormFieldValues(),t.size>0&&!this._isDestroyed()&&((0,o.V1)(this._annotationCallbacks),this._annotationCallbacks.createAnnotations(t,(0,r.T5)(),W.n)),this._formFieldsLoadedPromise=Promise.resolve()}createFormFieldValue(e){return this._verifyLoaded(),this.setFormFieldValue(e)}setFormFieldValue(e){return this._verifyLoaded(),this._core.setFormFieldValue((0,j.cA)(e))}deleteFormFieldValue(e){return this._verifyLoaded(),this._core.deleteFormFieldValue(e.replace("form-field-value/",""))}loadAnnotationsForPageIndex(e){const t=this._state.alreadyLoadedPages.get(e);if(t)return t;const n=this._loadAnnotationsForPageIndex(e);return this._state=this._state.setIn(["alreadyLoadedPages",e],n),n}async _loadAnnotationsForPageIndex(e){this._verifyLoaded();const[t,n]=await Promise.all([this._core.annotationsForPageIndex(e),this._core.getTabOrder(e)]);if(this._isDestroyed())return;const a=[],i=[],s=t.map((e=>{let{rollover:t,down:n,...s}=e;return t&&"number"==typeof s.pdfObjectId&&a.push(s.pdfObjectId),n&&"number"==typeof s.pdfObjectId&&i.push(s.pdfObjectId),s})).filter((e=>"number"==typeof e.pageIndex));this._formFieldCallbacks&&await this.loadFormFields();const c=(0,r.B8)().withMutations((e=>{s.filter((e=>!e.id||this._readStateCallbacks&&!this._readStateCallbacks.isAnnotationInState(e.id))).forEach((t=>{t.pdfObjectId;try{let n;n=function(e){return"pspdfkit/link"===e.type&&0===e.pdfObjectId}(t)?t.id||(0,v.K1)():t.id||t.pdfObjectId.toString(),(e.some((e=>e.id===n))||this._readStateCallbacks?.isAnnotationInState(n))&&(n=(0,v.K1)(),t.id=n,this._core.updateAnnotation(t));const a=(0,j.h8)(n,t);e.push(a)}catch(e){(0,o.pq)(`Skipped creating annotation #${t.pdfObjectId} from payload because an error occurred while deserializing.`),(0,o.pq)(e)}}))}));(0,z.vA)((()=>{(0,o.V1)(this._annotationCallbacks),c.size>0&&this._annotationCallbacks.createAnnotations(c,(0,r.T5)(),W.n),this._annotationCallbacks.setPageTabOrder(e,n),a.length>0&&this._annotationCallbacks.addAnnotationVariants("rollover",a),i.length>0&&this._annotationCallbacks.addAnnotationVariants("down",i)})),this._state=this._state.setIn(["alreadyLoadedPages",e],Promise.resolve())}async _loadFormFieldValues(){this._verifyLoaded();const e=await this._core.getFormValues();if(this._isDestroyed())return;const t=(0,r.B8)().withMutations((t=>{e.forEach((e=>{try{t.push((0,j.R5)(e))}catch(t){(0,o.pq)(`Skipped creating form field value #${e.pdfObjectId} from payload because an error occurred while deserializing.`),(0,o.pq)(t)}}))}));t.size>0&&!this._isDestroyed()&&((0,o.V1)(this._formFieldValueCallbacks),this._formFieldValueCallbacks.setFormFieldValues(t))}async loadBookmarks(){this._verifyLoaded();const e=await this._core.getBookmarks();if(this._isDestroyed())return;const t=(0,r.B8)().withMutations((t=>{e.forEach((e=>{let n;n=e.id?e.id:e.pdfBookmarkId?e.pdfBookmarkId:(0,K.z)();try{t.push((0,M.r)(n,e))}catch(e){(0,o.pq)(`Skipped creating bookmark #${n} from payload because an error occurred while deserializing.`),(0,o.pq)(e)}}))}));t.size>0&&!this._isDestroyed()&&((0,o.V1)(this._bookmarkCallbacks),this._bookmarkCallbacks.createBookmarks(t,W.n))}_verifyLoaded(){(0,o.V1)(this._state.isLoaded,"StandaloneProvider not properly initialized.")}_isDestroyed(){return this._state.isDestroyed}async syncChanges(){}}function X(e,t){(0,o.V1)(e.rootId,"A new comment must have `rootId` present");const n=t.get(e.rootId);return(0,o.V1)(n,"An annotation must be present linked to the comment to create"),(0,j.wG)(e,n.pdfObjectId?.toString()===n.id?parseInt(e.rootId):e.rootId)}function H(e){return"string"==typeof e?.serialNumber&&e.body instanceof ArrayBuffer}function Y(e){return e?.pkcs7 instanceof ArrayBuffer&&(!("ocspResponses"in e)||Array.isArray(e.ocspResponses)&&e.ocspResponses.every((e=>H(e))))}function Z(e){if(!e)return!1;const{signedData:t,timestampResponse:n,ocspResponses:a}=e;if(!(t instanceof ArrayBuffer))return!1;if(n&&!(n instanceof ArrayBuffer))return!1;if(a){if(!Array.isArray(a))return!1;if(!a.every((e=>H(e))))return!1}return!0}class Q{constructor(e){this.backend=e}async signDocumentAndReload(e,t){const n=e?.signingData,a=n?.certificates;(0,o.V1)(void 0===t||"function"==typeof t,"On a Standalone deployment, when `signaturePreparationData.signingData.privateKey` is not provided, `twoStepSignatureCallbackOrSigningServiceData` must be a function or `PSPDFKit.StandaloneSigningServiceData`."),(0,o.V1)(!n?.timestamp||"string"==typeof n?.timestamp?.url,"The `url` property of `signingData.timestamp` must be a string."),(0,o.V1)(!n?.timestamp||!n?.timestamp?.password||"string"==typeof n?.timestamp?.password,"The `password` property of `signingData.timestamp` must be a string."),(0,o.V1)(!n?.timestamp||!n?.timestamp?.username||"string"==typeof n?.timestamp?.username,"The `username` property of `signingData.timestamp` must be a string."),(0,o.V1)(void 0===n?.ltv||"boolean"==typeof n?.ltv,"The `ltv` property of `signingData` must be a boolean if set."),(0,o.V1)(void 0===n?.ltv||"boolean"==typeof n?.ltv,"The `ltv` property of `signingData` must be a boolean if set."),(0,o.V1)(!n||!n.signatureType||n.signatureType===S.D7.CMS||Array.isArray(a)&&a.length>0&&a.every((e=>Boolean(e instanceof ArrayBuffer&&e.byteLength>0||"string"==typeof e&&e.length>0))),"For signatures of type `PSPDFKit.SignatureType.CAdES` an `Array` of certificates must be provided in `signaturePreparationData.signingData.certificates`.");const i={signatureType:n?.signatureType||(Array.isArray(a)&&a.length>0?S.D7.CAdES:S.D7.CMS),...a&&{certificates:a.map((e=>e instanceof ArrayBuffer?c.o4.fromUint8Array(new Uint8Array(e)):c.o4.encode(e)))},...e?.placeholderSize?{estimatedSize:e.placeholderSize}:null};try{const{hash:a,signatureFormFieldName:s,file:r,fileContents:l,dataToBeSigned:d}=await this.backend.client.prepareSign((0,j.Y6)(i),e?.signatureMetadata?(0,j.sZ)(e.signatureMetadata):null,Boolean(e?.flatten),e?.formFieldName,(0,j.qN)(e?.position),await(0,j.z8)(e?.appearance)),u=function(e){const t=e.trim(),n=t.length/2,a=new Uint8Array(n);for(let e=0;e<n;e++)a[e]=parseInt(t.substr(2*e,2),16);return a}(d);let m,h,p;if(t){try{m=await t({hash:a,fileContents:l,dataToBeSigned:u})}catch(e){throw new o.uE(`\`twoStepSignatureCallback\` threw an error: ${e}`)}if(!(m instanceof ArrayBuffer||Y(m)||Z(m)))throw new o.uE(`The resolved value from \`twoStepSignatureCallback\` should be a an \`ArrayBuffer\`, \`SignatureCallbackResponsePkcs7\`, or \`SignatureCallbackResponseRaw\`, but is of type \`${typeof m}\` instead.`);p=m}else{if(!n?.privateKey)throw new o.uE("No `twoStepSignatureCallback` or `signingData.privateKey` was provided.");{const e={name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"},modulusLength:2048,extractable:!1,publicExponent:new Uint8Array([1,0,1])},t=await globalThis.crypto.subtle.importKey("pkcs8",function(e){const t=e.split("\n");let n="";for(let e=0;e<t.length;e++){t[e].trim().length>0&&t[e].indexOf("-BEGIN RSA PRIVATE KEY-")<0&&t[e].indexOf("-BEGIN PRIVATE KEY-")<0&&t[e].indexOf("-BEGIN RSA PUBLIC KEY-")<0&&t[e].indexOf("-BEGIN CERTIFICATE-")<0&&t[e].indexOf("-END RSA PRIVATE KEY-")<0&&t[e].indexOf("-END PRIVATE KEY-")<0&&t[e].indexOf("-END RSA PUBLIC KEY-")<0&&t[e].indexOf("-END CERTIFICATE-")<0&&(n+=t[e].trim())}return c.o4.toUint8Array(n).buffer}(n.privateKey),e,!0,["sign"]);h=await globalThis.crypto.subtle.sign(e,t,u),p=h}}let f="",g=i.certificates||[];if(p instanceof ArrayBuffer)f=c.o4.fromUint8Array(new Uint8Array(p))||"";else if(Y(p)){const e=p;f=c.o4.fromUint8Array(new Uint8Array(e.pkcs7))||""}else if(Z(p)){const e=p;f=c.o4.fromUint8Array(new Uint8Array(e.signedData))||"",g=e.certificates.map((e=>e instanceof ArrayBuffer?c.o4.fromUint8Array(new Uint8Array(e)):c.o4.encode(e)))}let y=null,b=[];if(n?.ltv&&(p instanceof ArrayBuffer?b=await this.backend.getRevocationResponses(g):(Y(p)||Z(p))&&((0,o.V1)(null==p.ocspResponses||Array.isArray(p.ocspResponses)&&p.ocspResponses.every((e=>H(e))),"The `ocspResponses` property of `signatureData` must be a array of `OCSPResponses`."),b=p.ocspResponses?.map((e=>({response_code:200,body:c.o4.fromUint8Array(new Uint8Array(e.body)),token:e.serialNumber})))??[],b.length||((0,o.R8)("The `TwoStepSignatureCallback` didn't return certificate revocation responses; trying to fetch them online."),b=await this.backend.getRevocationResponses(g)))),Z(p)){const e=p;(0,o.V1)(null==e.timestampResponse||e.timestampResponse instanceof ArrayBuffer,"The `timestampResponse` property of `signatureData` must be an `ArrayBuffer`."),e.timestampResponse&&(y={response_code:200,body:c.o4.fromUint8Array(new Uint8Array(e.timestampResponse)),token:"1234"})}if(!y&&n?.timestamp&&p instanceof ArrayBuffer){const e=c.o4.toUint8Array(f).buffer;y=await this.backend.timestampData(e,n.timestamp)}const A=await this.backend.client.sign(r,s,a,j.xz[i.signatureType],f,g,Y(p)?"pkcs7":Z(p)?"raw":n?.signatureContainer?n.signatureContainer:null,y,b);return n?.ltv&&A.signature.certificateChainValidationStatus===S.k5.ok_but_could_not_check_revocation&&(0,o.R8)("Document signed, but couldn't add certificate revocation information so the signature may not be LTV-enabled."),await this.backend.reloadDocument(),s}catch(e){throw await this.backend.client.restoreToOriginalState(),e}}}var ee=n(64966);class te extends Q{constructor(e){super(e)}async signDocumentAndReload(e,t){t=(0,w.kJ)(t),(0,o.V1)(t,"`twoStepSignatureCallbackOrStandaloneSigningServiceData` must be a `PSPDFKit.StandaloneSigningServiceData` object when performing signing via a backend service.");const{jwt:n,signingToken:a}=t,i=this.resolveApiBasePath(t),s=this.resolveServerUrl(t),r=await this.getCertificates(s,n,`${i}get_certificates`,a),c=this.signatureCallbackFactory(s,n,r,e,t,`${i}sign_hash`);return await super.signDocumentAndReload({...e,signingData:{...e?.signingData,signatureType:e?.signingData?.signatureType||S.D7.CAdES,certificates:r}},c)}async getCertificates(e,t,n,a){let i;const s=`${e}${n}`;try{i=await fetch(s,{method:"POST",headers:{Authorization:`Bearer ${t}`,Accept:"application/json","Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({signingToken:a})})}catch(e){throw new o.uE(`The signing service failed to retrieve certificates: Failed to fetch from ${s}`)}if(!i.ok)throw new o.uE(`The signing service failed to retrieve certificates: ${await i.json()}`);const r=(await i.json())?.data||{};return r.certificates.concat(r.ca_certificates).map((e=>c.o4.fromBase64(e)))}resolveApiBasePath(e){return(0,ee.i)(e.jwt).api_base_path||"/api/"}resolveServerUrl(e){const t=e.serverUrl||(0,ee.i)(e.jwt).server_url;return(0,o.V1)(t,"`twoStepSignatureCallbackOrStandaloneSigningServiceData.jwt` must contain claim `server_url` or the `twoStepSignatureCallbackOrStandaloneSigningServiceData.serverUrl` should be set"),t}signatureCallbackFactory(e,t,n,a,i,s){return async r=>{let l,{hash:d,dataToBeSigned:u}=r;try{l=await fetch(`${e}${s}`,{method:"POST",headers:{Authorization:`Bearer ${t}`,Accept:"application/json","Content-Type":"application/json"},credentials:"same-origin",body:JSON.stringify({dataToBeSigned:c.o4.fromUint8Array(u),hash:d,signatureType:a?.signingData?.signatureType||S.D7.CAdES,signingToken:i.signingToken,cadesLevel:a?.signingData?.padesLevel||S.sX.b_lt})})}catch(t){throw new o.uE(`The signing service failed to retrieve certificates: Failed to fetch from ${e}${s}`)}if(!l.ok)throw new o.uE(`The signing service failed to sign: ${await l.text()}`);const m=(await l.json())?.data||{};if(a?.signingData?.signatureContainer===S.Yj.pkcs7){const e={pkcs7:c.o4.toUint8Array(m.pkcs7).buffer};return m.ocspResponses&&(e.ocspResponses=m.ocspResponses.map((e=>({serialNumber:e.serialNumber,body:c.o4.toUint8Array(e.body).buffer})))),e}{const e={certificates:n,signedData:c.o4.toUint8Array(m.signedData).buffer,timestampResponse:c.o4.toUint8Array(m.timestampResponse).buffer};return m.ocspResponses&&(e.ocspResponses=m.ocspResponses.map((e=>({serialNumber:e.serialNumber,body:c.o4.toUint8Array(e.body).buffer})))),e}}}}var ne=n(30026),ae=n(85553);class ie extends(r.mS({baseUrl:null,baseCoreUrl:null,baseProcessorEngineUrl:null,licenseKey:null,document:null,backendPermissions:new ne.A,documentResponse:null,disableWebAssemblyStreaming:!1,enableAutomaticLinkExtraction:!1,overrideMemoryLimit:null,features:(0,r.B8)(),signatureFeatureAvailability:ae.g.NONE,documentHandle:null,trustedCAsCallback:null,signaturesInfoPromise:null,customFonts:null,fontSubstitutions:null,forceLegacySignaturesFeature:!1,forceAnnotationsRender:!1,appName:null,lazyLoadedPages:null,productId:null,processorEngine:null,dynamicFonts:null,inlineWorkers:!0,allowLinearizedLoading:!1,enableCoreUnifiedComments:!1})){}let se;se=n(55994).Fe;const oe=new i.iy(se);class re extends N.K{constructor(e){super(),(0,a.A)(this,"type","STANDALONE"),(0,a.A)(this,"standaloneDocumentSigner",new Q(this)),(0,a.A)(this,"signingServiceDocumentSigner",new te(this)),(0,a.A)(this,"_XFDF",null),le(e);const{baseUrl:t,baseCoreUrl:n,baseProcessorEngineUrl:i,instantJSON:o,XFDF:r,enableAutomaticLinkExtraction:c,overrideMemoryLimit:l,trustedCAsCallback:u,electronAppName:m,appName:f,isSharePoint:g,isSalesforce:y,productId:b,processorEngine:A,dynamicFonts:S,inlineWorkers:w,formsConfiguration:P,allowLinearizedLoading:_,unifiedCommentsSupport:F}=e;"string"==typeof r&&(this._XFDF={source:r,keepCurrentAnnotations:!0===e.XFDFKeepCurrentAnnotations,ignorePageRotation:!0===e.XFDFIgnorePageRotation}),o&&o.annotations&&(o.annotations=o.annotations.map((e=>(e.id=e.id?.toString(),e)))),this._instantJSON=o,this._formsConfiguration=P,"function"==typeof u&&(this._trustedCAsCallback=u);const{disableWebAssemblyStreaming:v,customFonts:k,fontSubstitutions:C}=e,{standaloneInstancesPoolSize:D}=e;void 0!==D&&(oe.size=D);const I=!!e.electronicSignatures&&Boolean(e.electronicSignatures.forceLegacySignaturesFeature);let R=b||null;!g&&!y||R||(R=g?p.v.SharePoint:p.v.Salesforce),this._state=new ie(ce({baseUrl:t,baseCoreUrl:n,baseProcessorEngineUrl:i,licenseKey:e.licenseKey,document:e.document,disableWebAssemblyStreaming:v,enableAutomaticLinkExtraction:c,overrideMemoryLimit:l,documentHandle:"0",customFonts:k,fontSubstitutions:C,forceLegacySignaturesFeature:I,appName:f||m,productId:R,processorEngine:A||h.j.fasterProcessing,dynamicFonts:S,inlineWorkers:w,allowLinearizedLoading:_,enableCoreUnifiedComments:F})),this._requestQueue=new s.L(d.z3);const{object:T,checkIn:x}=oe.checkOut();this.client=T,this.checkIn=x,this.corePDFDocument=new L.N({wasmClient:this.client},{type:"alreadyLoadedDocument"});const V=o?{annotations:o.annotations||[],formFields:o.formFields||[],formFieldValues:o.formFieldValues||[],skippedPdfObjectIds:o.skippedPdfObjectIds||[],skippedPdfFormFieldIds:o.skippedPdfFormFieldIds||[],attachments:o.attachments||{},bookmarks:o.bookmarks||[],skippedPdfBookmarkIds:o.skippedPdfBookmarkIds||[],comments:o.comments||void 0,skippedComments:o.skippedComments||void 0,format:o.format,...o.pdfId?{pdfId:o.pdfId}:null}:null;this.provider=new G(this.client,V)}isUsingInstantProvider(){return!1}hasClientsPresence(){return!1}async load(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._isPDFJavaScriptEnabled=e.isPDFJavaScriptEnabled,{features:this._state.features,signatureFeatureAvailability:this._state.signatureFeatureAvailability,hasPassword:!!e.password,password:e.password,allowedTileScales:"all"}}async afterDocumentLoaded(e){}destroy(){this.corePDFDocument.destroy(),this.provider&&this.provider.destroy(),this._requestQueue&&this._requestQueue.destroy(),this.checkIn()}async documentInfo(){return this._state.documentResponse}async lazyLoadPages(){if(!this._state.lazyLoadedPages){const e=await this.client.getAllPageInfos(this._state.documentResponse.pageCount);this._state=this._state.set("lazyLoadedPages",e)}return this._state.lazyLoadedPages}getDocumentHandle(){return this._state.documentHandle}getFormJSON(){return this.client.getFormJSON()}permissions(){return Promise.resolve(this._state.backendPermissions)}textForPageIndex(e){return this.corePDFDocument.getTextLines(e)}getContentTreeForPageIndex(e){return this.corePDFDocument.getContentTree(e)}getTextFromRects(e,t){return this.client.getTextFromRects(e,t.toJS())}getAvailableFontFaces(e){return this.client.getAvailableFontFaces(e)}async getSuggestedLineHeightFactor(e){return"number"!=typeof e.pdfObjectId?1:"number"!=typeof e.pageIndex?((0,o.R8)("Annotation must have a pageIndex."),1):(e.lineHeightFactor&&(0,o.R8)(`Annotation ${e.id} already has a line height factor.`),this.client.getSuggestedLineHeightFactor(e.pdfObjectId,e.pageIndex))}async getClosestSnapPoint(e){const t=await this.client.getClosestSnapPoint(e.x,e.y);return t&&"number"==typeof t[0]&&"number"==typeof t[1]?new _.bR({x:t[0],y:t[1]}):e}configureSnapper(e){return this.client.configureSnapper(e)}renderAnnotation(e,t,n,a,i,s,o){if(0===Math.floor(a)||0===Math.floor(i))return{promise:Promise.resolve(void 0),cancel:()=>{}};const r=(0,x.yl)();let c;if((0,v.cu)(e)){if(c=(0,v.sS)(e),this.isVisuallyIdenticalAnnotationCached({annotation:e,providedVisualAnnotationIndex:c,width:a,height:i}))return this.cachedRenderedAnnotationDiscardablePromise(c,r);this._addCachedRenderedAnnotation(c,{width:a,height:i,noZoom:e.noZoom,APStreamPromise:r.promise})}const l=e.id,d=new B(l,(()=>this.client.renderAnnotation((0,j.eq)(e),n,a,i,(0,D.LG)(),s,o).then((e=>e?"string"==typeof e?(0,C.$3)(e):(0,C.vB)({buffer:e,width:a,height:i}):Promise.resolve(null))).then((t=>((0,v.cu)(e)&&this._makeEnqueuedRelease(t,c),r.resolve(t),t)))));return this._requestQueue.enqueue(d,!1)}async getMeasurementSnappingPoints(e){return this.client.getMeasurementSnappingPoints(e)}async getSecondaryMeasurementUnit(){return await this.client.getSecondaryMeasurementUnit()}async setSecondaryMeasurementUnit(e){return await this.client.setSecondaryMeasurementUnit(e)}async compareDocuments(e,t){const n={originalDocument:e.originalDocument,changedDocument:e.changedDocument,comparisonOperation:(0,g.c)(t)};return this.corePDFDocument.compareText(n).promise}async renderPageAnnotations(e,t,n){const a=this.provider,i=[],s=[],r=t.some((e=>e instanceof b.sb));r&&await a._setReadStateCallbacksPromise;const c=t.filter((e=>{const t=r?a._readStateCallbacks.getAnnotationWithFormField(e.id):null,n=t?.formField,o=(0,v.lG)(e,n);if(o&&n){i.find((e=>e.name===n.name))||(i.push((0,j.cA)((0,I.Af)(n))),s.push(n))}return o}));function l(e,t){if((0,v.uM)(e)){const n=s.find((t=>t.name===e.formFieldName)),a=t.find((t=>t.name===e.formFieldName));if(!(0,I.Ny)(n,a))return!1}return!0}const d=new Promise(((t,i)=>{const r=c.filter((e=>0!==Math.floor(e.boundingBox.width*n)&&0!==Math.floor(e.boundingBox.height*n)));this.client.renderPageAnnotations(e,r.map((e=>e.pdfObjectId)).toArray(),r.map((e=>e.boundingBox.width*n)).toArray(),r.map((e=>e.boundingBox.height*n)).toArray(),(0,D.LG)()).then((e=>{const i=s.map((e=>a._readStateCallbacks?.getFormFieldByName(e.name))).filter(Boolean),c=e.map(((e,t)=>{const a=r.get(t);return(0,o.V1)(a),l(a,i)&&a&&e?"string"==typeof e?(0,C.$3)(e):(0,C.vB)({buffer:e,width:a.boundingBox.width*n,height:a.boundingBox.height*n}):Promise.resolve(null)}));Promise.all(c).then((e=>{const i=s.map((e=>a._readStateCallbacks?.getFormFieldByName(e.name))).filter(Boolean);e.forEach(((e,t)=>{const a=r.get(t);if(a){const{formFieldValue:t}=this.getAnnotationFormFieldAndValue(a),s=this.getAnnotationAvailableVariants(a),o=this.annotationAPStreamPromises.get(a.id),r=l(a,i);if(o&&(this.annotationAPStreamPromises=this.annotationAPStreamPromises.delete(a.id),o(r?e:null)),s.length>1){const i={normal:e};e&&r&&this.cacheAPStream(i,a);const{promise:o}=this.renderAPStream(a,t,null,a.boundingBox.width*n,a.boundingBox.height*n,s);Promise.all(o.map((e=>e.promise))).then((e=>{e.some(Boolean)&&s.forEach(((t,n)=>{"normal"!==t&&e[n]&&(i[t]=e[n])}))}))}else e&&r&&this.cacheAPStream(e,a)}})),t()}))})).catch(i)}));return this.pageAPStreamsPromises=this.pageAPStreamsPromises.set(e,d),d}renderDetachedAnnotation(e,t,n,a){if(e.id)throw new o.uE(`Detached annotations should not have an \`id\`: ${e.id}`);const i=(0,x.yl)();let s;if((0,v.cu)(e)){if(s=(0,v.sS)(e),this.isVisuallyIdenticalAnnotationCached({annotation:e,providedVisualAnnotationIndex:s,width:n,height:a}))return this.cachedRenderedAnnotationDiscardablePromise(s,i);this._addCachedRenderedAnnotation(s,{width:n,height:a,noZoom:e.noZoom,APStreamPromise:i.promise})}const r=(0,l.Z0)(),c=new B(r,(()=>this.client.renderDetachedAnnotation((0,j.eq)(e),t,n,a,(0,D.LG)()).then((e=>e?(0,C.vB)({buffer:e,width:n,height:a}):Promise.resolve(null))).then((t=>((0,v.cu)(e)&&this._makeEnqueuedRelease(t,s),i.resolve(t),t))).catch((()=>{const e=this._cachedRenderedAnnotations.get(s);e&&e.timeout&&clearTimeout(e.timeout),this._cachedRenderedAnnotations.delete(s)})))),{promise:d,cancel:u}=this._requestQueue.enqueue(c,!1);return{promise:d,cancel:u}}async getAttachment(e){const[t,n]=await this.client.getAttachment(e);return new Blob([t],{type:n})}async parseXFDF(e,t){const{errors:n,formFieldValues:a,annotations:i}=await this.client.parseXFDF(e,t);return{errors:n?.map((e=>({errorMessage:e.error_message,type:e.type}))),formFieldValues:a?.reduce(((e,t)=>(e[t.fqdn]=t.values,e)),{}),annotations:(0,r.B8)(i?.map((e=>(0,j.h8)((0,v.K1)(),e)))||[])}}async search(e,t,n,a){let i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:f.n.TEXT;const o=await this.client.search(e,t,n,a,s);return(0,V.g)(o.filter((e=>i||!e.isAnnotation)))}async searchAndRedact(e,t,n){const{totalPages:a}=n,i=await this.client.search(e,t.startPageIndex??0,t.pageRange??a,t.caseSensitive,t.searchType);return(0,r.B8)(i.filter((e=>t.searchInAnnotations||!e.isAnnotation)).map((e=>{const a=e.isAnnotation?[e.annotationRect]:e.rectsOnPage,i=(0,r.B8)(a).map((e=>((0,o.V1)(e),(0,U.a)(e))));return new A.A({...(0,v.mN)(n),...t.annotationPreset,pageIndex:e.pageIndex,rects:i,boundingBox:F.A.union(i)})})))}async exportPDF(){let{flatten:e=!1,incremental:t,saveForPrinting:n=!1,format:a="pdf",excludeAnnotations:i=!1,preserveInstantJSONChanges:s=!0,permissions:r,outputFormat:c=!1,flattenElectronicSignatures:l=e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},d=arguments.length>1?arguments[1]:void 0;if(e&&!l)throw new o.uE("Cannot set `flattenElectronicSignatures` to `false` when `flatten` is set to `true`.");if(c&&"boolean"!=typeof c&&c.conformance&&(0,o.V1)(c.conformance&&Object.values(m.o).includes(c.conformance),"The supplied PDF/A Conformance type is not valid. Valid Conformance should be one of the following options PSPDFKit.Conformance."+Object.keys(m.o).join(", PSPDFKit.Conformance.")),void 0===t)if(this._state.features.includes(u.Y.DIGITAL_SIGNATURES)){const e=await this.getSignaturesInfo();t=!n&&Boolean("not_signed"!==e.status)}else t=!1;return this.client.exportFile(e,t,n,a,i,s,r,d).then((async e=>{let[t,n]=e;if(t.mimeType=n.mimeType,t.extension=n.extension,c){const e="boolean"!=typeof c&&c.conformance?c.conformance:m.o.PDFA_2B;let n;try{return n=await this._setupGdPictureClient(),await n.toPdf(t,e)}finally{n?.destroy(),(0,O.Pm)(null)}}return t}))}async exportOffice(e){let t,{format:n}=e;try{const[e]=await this.client.exportFile(!1,!1,!1,"pdf",!1,!0);return t=await this._setupGdPictureClient(),await t.toOffice(e,n)}catch(e){throw new o.uE(`Exporting to ${n} failed: ${e.message}.`)}finally{t?.destroy(),(0,O.Pm)(null)}}async _setupGdPictureClient(){let e=(0,O.jU)();return e||(e=(0,O.NY)({baseUrl:this._state.baseProcessorEngineUrl,mainThreadOrigin:this._state.appName||(0,T.D5)()||window.location.origin,licenseKey:this._state.licenseKey,processorEngine:this._state.processorEngine,customFonts:this._state.customFonts,dynamicFonts:this._state.dynamicFonts,fontSubstitutions:this._state.fontSubstitutions}),(0,O.Pm)(e)),e}exportXFDF(e){return this.client.exportXFDF(e)}exportInstantJSON(e){return this.client.exportInstantJSON(e)}getPDFURL(){let{includeComments:e=!0,saveForPrinting:t,excludeAnnotations:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.generatePDFObjectURL({includeComments:e,saveForPrinting:t,excludeAnnotations:n})}generatePDFObjectURL(){let e,{includeComments:t=!0,saveForPrinting:n,excludeAnnotations:a=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=!1;return{promise:new Promise((s=>{this.exportPDF({flatten:!0,includeComments:t,saveForPrinting:n,excludeAnnotations:a}).then((t=>{if(i)return;const n=new Blob([t],{type:t.mimeType});e=window.URL.createObjectURL(n),s(e)}))})),revoke:()=>{e&&window.URL.revokeObjectURL(e),i=!0}}}async getDocumentOutline(){const e=await this.client.getDocumentOutline();return(0,r.B8)(e.map(E.r))}async setDocumentOutline(e){return this.client.setDocumentOutline(e.map(E.U).toArray())}async getPageGlyphs(e){const t=await this.client.getPageGlyphs(e);return(0,j.Fj)(t)}async onKeystrokeEvent(e){return await this.client.onKeystrokeEvent(e)}async evalFormValuesActions(e){return this.client.evalFormValuesActions(e.map(j.cA).toJS())}async evalScript(e,t,n){return this.client.evalScript(e,t,n)}async setFormJSONUpdateBatchMode(e){return this.client.setFormJSONUpdateBatchMode(e)}async getMeasurementScales(){const e=await this.client.getMeasurementScales();return e?.measurementContentFormats}async addMeasurementScale(e){return await this.client.addMeasurementScale(e)}async removeMeasurementScale(e){return await this.client.removeMeasurementScale(e)}async getAnnotationsByScale(e){return await this.client.getAnnotationsByScale(e)}async applyOperationsAndReload(e){try{const{processedOperations:t,operationsDocuments:n}=await(0,k.t)(e);await this.client.applyOperations(t,n)}catch(e){throw new o.uE(`Applying operations failed: ${e}`)}return this.provider._state=this.provider._state.set("alreadyLoadedPages",(0,r.T5)()),this.reloadDocument()}async applyRedactionsAndReload(){try{await this.client.applyRedactions()}catch(e){throw new o.uE(`Applying redactions failed: ${e}`)}return this.reloadDocument()}async reloadDocument(){try{this.provider?.destroy(),this.provider=new G(this.client,null),this._state=this._state.set("lazyLoadedPages",null);const e=await this.client.reloadDocument();return this._state=this._state.set("documentResponse",e).set("documentHandle",(parseInt(this._state.documentHandle)+1).toString()).set("signaturesInfoPromise",null),this.clearRenderedAnnotationsCache(),{features:this._state.features,signatureFeatureAvailability:this._state.signatureFeatureAvailability,hasPassword:!1,password:void 0,allowedTileScales:"all"}}catch(e){throw new o.uE(`Reloading failed: ${e}`)}}async getEmbeddedFiles(){const e=await this.client.getEmbeddedFilesList();return(0,r.B8)(e.map((e=>{let{id:t,...n}=e;return(0,y.r)(t,n,!0)})))}async exportPDFWithOperations(e){try{const{processedOperations:t,operationsDocuments:n}=await(0,k.t)(e);return this.client.exportPDFWithOperations(t,n)}catch(e){throw new o.uE(`Exporting PDF with operations failed: ${e}`)}}async setSignaturesLTV(e){try{const t=(e??[]).map((e=>e instanceof ArrayBuffer?c.o4.fromUint8Array(new Uint8Array(e)):c.o4.encode(e))),n=await this.getRevocationResponses(t),a=await this.client.setSignaturesLTV(n).then((e=>(0,j.N5)(e)));if(a.signatures?.find((e=>e.certificateChainValidationStatus===S.k5.ok_but_could_not_check_revocation)))throw"Could not set LTV for all signatures. Check that the OCSP response is valid.";return this._state=this._state.set("signaturesInfoPromise",null),this.getSignaturesInfo()}catch(e){throw new o.uE(`Setting signatures LTV failed: ${e}`)}}getSignaturesInfo(){try{if(this._state.signaturesInfoPromise)return this._state.signaturesInfoPromise;const e=this.client.getSignaturesInfo().then((e=>(0,j.N5)(e)));return this._state=this._state.set("signaturesInfoPromise",e),e}catch(e){throw new o.uE(`Getting document signatures info: ${e}`)}}async refreshSignaturesInfo(){this._state=this._state.set("signaturesInfoPromise",null)}async loadCertificates(e){return this.client.loadCertificates(e)}async getRevocationResponses(e){const t=await this.client.getRevocationRequests(e);return await Promise.all(t.map((async e=>{let{method:t,url:n,content_type:a,request_data:i,token:s}=e;try{const e=await fetch(n,{method:t,headers:{"Content-Type":a??"application/ocsp-request"},body:c.o4.toUint8Array(i).buffer}),o=await e.arrayBuffer();return{response_code:e.status,body:c.o4.fromUint8Array(new Uint8Array(o)),token:s}}catch(e){return{response_code:0,body:"",token:s,error_message:e.message}}})))}async signDocumentAndReload(e,t){(0,o.V1)(void 0===t||"function"==typeof t||(0,w.kJ)(t),"On a Standalone deployment, `twoStepSignatureCallbackOrSigningServiceData` must be a function or `PSPDFKit.StandaloneSigningServiceData` if provided.");const n=(0,w.kJ)(t);return n?this.signingServiceDocumentSigner.signDocumentAndReload(e,n):this.standaloneDocumentSigner.signDocumentAndReload(e,t)}cancelRequests(){this._requestQueue.cancelAll()}async syncChanges(){}getDefaultGroup(){}isCollaborationPermissionsEnabled(){return!1}async clearAPStreamCache(){return this.client.clearAPStreamCache()}async setComparisonDocument(e,t){return this.client.setComparisonDocument(e,t)}async openComparisonDocument(e){return this._state=this._state.set("forceAnnotationsRender",!1),await this.client.closeDocument(),this._state=this._state.set("forceAnnotationsRender",!0),await this.client.openComparisonDocument(e)||this._state.documentResponse}async documentCompareAndOpen(e){return this.client.documentCompareAndOpen(e)}async persistOpenDocument(e){return this.client.persistOpenDocument(e)}async cleanupDocumentComparison(){return this.client.cleanupDocumentComparison()}async runPDFFormattingScripts(e,t){return this.client.runPDFFormattingScripts(e,t)}async runPDFFormattingScriptsFromWidgets(e,t,n){let a=[];if(this._isPDFJavaScriptEnabled){const{withAPStream:i,withoutAPStream:s}=e.reduce(((e,a)=>{if(a instanceof b.sb){const i=t?.get(a.formFieldName);if(i instanceof b.Vw)return e;n?.(a)?e.withAPStream.push(a.formFieldName):e.withoutAPStream.push(a.formFieldName)}return e}),{withAPStream:[],withoutAPStream:[]});let o=[];if(i.length&&!s.length)o=await this.runPDFFormattingScripts(i,!0);else if(!i.length&&s.length)o=await this.runPDFFormattingScripts(s,!1);else if(i.length&&s.length){const[e,t]=await Promise.all([this.runPDFFormattingScripts(i,!0),this.runPDFFormattingScripts(s,!1)]);o=e.concat(t)}a=(0,I.A5)(this._initialChanges,o)}return a}setFontSubstitutions(e){return this.client.setFontSubstitutions(e)}async contentEditorReload(){return(0,o.V1)(this.provider instanceof G,"Standalone can only use standalone annotation provider"),this.provider._state=this.provider._state.set("alreadyLoadedPages",(0,r.T5)()),this.reloadDocument()}getOCGs(){return this.client.getOCGs()}getOCGVisibilityState(){return this.client.getOCGVisibilityState()}setOCGVisibilityState(e){return this.client.setOCGVisibilityState(e)}updateButtonIcon(e,t,n){return this.client.updateButtonIcon((0,j.eq)(e),t,n)}async timestampData(e,t){const{url:n,username:a="",password:i=""}=t,s=c.o4.fromUint8Array(new Uint8Array(e));try{const e=await this.client.getTimestampRequest(s,{url:n,username:a,password:i}),t={method:e.method,headers:{"Content-Type":e.contentType||"application/timestamp-query"},body:c.o4.toUint8Array(e.requestData).buffer};(e.username||e.password)&&(t.headers.Authorization=`Basic ${btoa(`${e.username}:${e.password}`)}`);try{const n=await fetch(e.url,t),a=await n.arrayBuffer();return{response_code:n.status,body:c.o4.fromUint8Array(new Uint8Array(a)),token:e.token}}catch(t){return{response_code:400,body:"",token:e.token,error_message:t.message}}}catch(e){return{response_code:500,body:"",token:"",error_message:e.message}}}async waitUntilFullyLoaded(e){}}function ce(e){return{baseUrl:e.baseUrl,baseCoreUrl:e.baseCoreUrl,baseProcessorEngineUrl:e.baseProcessorEngineUrl,licenseKey:e.licenseKey,document:e.document,disableWebAssemblyStreaming:!!e.disableWebAssemblyStreaming,enableAutomaticLinkExtraction:!!e.enableAutomaticLinkExtraction,overrideMemoryLimit:"number"==typeof e.overrideMemoryLimit?e.overrideMemoryLimit:null,documentHandle:"number"==typeof e.documentHandle?e.documentHandle:"0",trustedCAsCallback:"function"==typeof e.trustedCAsCallback?e.trustedCAsCallback:null,customFonts:Array.isArray(e.customFonts)?e.customFonts.filter((e=>e instanceof P.A)):null,forceLegacySignaturesFeature:Boolean(e.forceLegacySignaturesFeature),appName:"string"==typeof e.appName?e.appName:null,productId:e.productId,processorEngine:e.processorEngine,dynamicFonts:e.dynamicFonts,fontSubstitutions:e.fontSubstitutions,inlineWorkers:e.inlineWorkers,allowLinearizedLoading:e.allowLinearizedLoading,enableCoreUnifiedComments:e.enableCoreUnifiedComments}}function le(e){const{licenseKey:t,instantJSON:n,XFDF:a,disableWebAssemblyStreaming:i,disableIndexedDBCaching:s,enableAutomaticLinkExtraction:r,overrideMemoryLimit:c,standaloneInstancesPoolSize:l,trustedCAsCallback:d,baseUrl:u,baseCoreUrl:m,baseProcessorEngineUrl:h,customFonts:p,isSharePoint:f,isSalesforce:g,dynamicFonts:y,inlineWorkers:b,formsConfiguration:A}=e;if((0,o.V1)("string"==typeof u,"`baseUrl` is mandatory and must be a valid URL, e.g. `https://example.com/"),(0,R.f4)(u),(0,o.V1)(!m||"string"==typeof m,"`baseCoreUrl` must be a valid URL if set, e.g. `https://example.com/"),m&&(0,R.Qo)(m),(0,o.V1)(!h||"string"==typeof h,"`baseProcessorEngineUrl` must be a valid URL if set, e.g. `https://example.com/"),h&&(0,R.hc)(h),(0,o.V1)(null==t||"string"==typeof t,"licenseKey must be a string value if provided. Please obtain yours from https://customers.pspdfkit.com."),"string"==typeof t&&(0,o.V1)(!t.startsWith("TRIAL-"),"You're using the npm key instead of the license key. This key is used to download the PSPDFKit for Web package via the node package manager.\n\nLeave out the license key to activate as a trial."),(0,o.V1)(void 0===a||"string"==typeof a,"XFDF must be a string"),n&&((0,o.yj)(n),(0,o.V1)(void 0===a,"Cannot import from both instantJSON and XFDF")),(0,o.V1)(void 0===i||"boolean"==typeof i,"disableWebAssemblyStreaming must be a boolean"),(0,o.V1)(void 0===r||"boolean"==typeof r,"enableAutomaticLinkExtraction must be a boolean"),(0,o.V1)(void 0===c||"number"==typeof c,"overrideMemoryLimit must be a number"),(0,o.V1)(void 0===l||"number"==typeof l&&l>=0,"standaloneInstancesPoolSize must be a non-negative number"),(0,o.V1)(void 0===d||"function"==typeof d,"trustedCAsCallback must be a function"),(0,o.V1)(void 0===p||Array.isArray(p)&&p.every((e=>e instanceof P.A)),"customFonts must be an array of PSPDFKit.Font instances"),(0,o.V1)(void 0===p||p.every((e=>e.callback)),"All PSPDFKit.Font instances specified on customFonts must have its callback property defined"),void 0!==s&&(0,o.t6)("disableIndexedDbCaching has been deprecated and it no longer has effect. It will be removed in a later version.\nBrowsers dropped IndexedDB serialization of Wasm modules in favor of regular HTTP caching."),(f||g)&&(0,o.t6)("isSharePoint and isSalesforce configuration properties are deprecated and will be removed in the next major release. Please use the new Configuration#productId property instead. For more information, please check the migration guide."),(0,o.V1)(!(f&&g),"You cannot enable both SharePoint and Salesforce integrations at the same time. Please set either isSharePoint or isSalesforce to true, but not both."),"string"==typeof y)try{new URL(y)}catch(e){throw new o.uE("dynamicFonts must be a valid URL to a JSON file containing the data for fonts to be dynamically loaded.")}(0,o.V1)(void 0===b||"boolean"==typeof b,"inlineWorkers must be a boolean"),A&&(0,o.V1)(void 0===A.export?.disableComboBoxArrow||"boolean"==typeof A.export?.disableComboBoxArrow,"formsConfiguration.export.disableComboBoxArrow must be a boolean")}},40927:(e,t,n)=>{n.d(t,{r:()=>o});var a=n(49568);class i extends(a.mS({id:"",attachmentId:"",description:null,fileName:null,fileSize:null,updatedAt:null})){}var s=n(46952);function o(e,t){return function(e,t){return t}(0,arguments.length>2&&void 0!==arguments[2]&&arguments[2])?new i({id:(0,s.A)(),description:t.fileDescription,attachmentId:e,fileName:t.fileName||null,fileSize:t.fileSize||null,updatedAt:t.modificationDate?new Date(t.modificationDate):null}):new i({id:e,description:t.description,attachmentId:t.fileAttachmentId,fileName:t.fileName||null,fileSize:t.fileSize||null,updatedAt:new Date(t.updatedAt)||null})}},41477:(e,t,n)=>{n.d(t,{U:()=>c,r:()=>l});var a=n(85409),i=n(49568),s=n(6085),o=n(16792),r=n(76117);function c(e){return{type:"pspdfkit/outline-element",children:e.children&&e.children.map((e=>c(e))).toJS(),title:e.title,color:e.color&&(0,o.J)(e.color),isBold:e.isBold,isItalic:e.isItalic,isExpanded:e.isExpanded,action:e.action&&(0,s.Ix)(e.action)}}function l(e){let t,n;(0,a.V1)("pspdfkit/outline-element"===e.type,"invalid outline element type."),(0,a.V1)(null==e.children||Array.isArray(e.children),"children must be an Array<OutlineElement>."),(0,a.V1)("string"==typeof e.title,"title must be a string."),(0,a.V1)(null==e.isBold||"boolean"==typeof e.isBold,"isBold must be a boolean."),(0,a.V1)(null==e.isItalic||"boolean"==typeof e.isItalic,"isItalic must be a boolean."),(0,a.V1)(null==e.isExpanded||"boolean"==typeof e.isExpanded,"isExpanded must be a boolean.");try{t=e.action&&(0,s._8)(e.action)}catch(t){(0,a.R8)(`PDF Action not supported ${JSON.stringify(e.action)})`)}try{n=e.color&&(0,o.p)(e.color)}catch(t){(0,a.R8)(`Invalid color:\n\n${e.color}`)}const c={title:e.title,color:n,isBold:!0===e.isBold,isItalic:!0===e.isItalic,isExpanded:!0===e.isExpanded,action:t,children:e.children&&e.children.length>0?(0,i.B8)(e.children.map(l)):(0,i.B8)()};return new r.LC(c)}},25888:(e,t,n)=>{n.d(t,{g:()=>i,t:()=>s});var a=n(85409);function i(e,t,n,a,i,s){let o;return e.has(n)?o=e.get(n):(o=i.toString(),t[o]=n,e.set(n,o)),{...a,[s]:o}}async function s(e){const t=new WeakMap,n={};return{processedOperations:await Promise.all(e.map((async(e,s)=>{if("importDocument"===e.type){const{document:o}=e;return(0,a.V1)(o instanceof File||o instanceof Blob,"Wrong `importDocument` operation `document` value: it must be a File or a Blob"),i(t,n,o,e,s,"document")}if("applyInstantJson"===e.type){const o=e.instantJson;(0,a.V1)("object"==typeof o&&null!==o,"Wrong `applyInstantJson` operation `instantJson` value: it must be an object");const r=JSON.stringify(o),c=new Blob([r],{type:"application/json"});return i(t,n,c,e,s,"dataFilePath")}if("applyXfdf"===e.type){const o=e.xfdf;(0,a.V1)("string"==typeof o,"Wrong `applyXfdf` operation `xfdf` value: it must be a string");const r=new Blob([o],{type:"application/vnd.adobe.xfdf"});return i(t,n,r,e,s,"dataFilePath")}return e}))),operationsDocuments:n}}},47159:(e,t,n)=>{n.d(t,{g:()=>r});var a=n(49568),i=n(85409),s=n(5293),o=n(3220);function r(e){return(0,i.V1)(Array.isArray(e),"Wrong `json` field"),(0,a.B8)(e.map((e=>((0,i.V1)("number"==typeof e.pageIndex,"Wrong `pageIndex` field"),(0,i.V1)("string"==typeof e.previewText,"Wrong `previewText` field"),(0,i.V1)(Array.isArray(e.rangeInPreview),"Wrong `rangeInPreview` field"),(0,i.V1)(Array.isArray(e.rectsOnPage),"Wrong `rectsOnPage` field"),new o.A({pageIndex:e.pageIndex,previewText:e.previewText,locationInPreview:e.rangeInPreview[0],lengthInPreview:e.rangeInPreview[1],rectsOnPage:(0,a.B8)(e.rectsOnPage).map((e=>(0,s.a)(e))),isAnnotation:!!e.isAnnotation,annotationRect:e.annotationRect?(0,s.a)(e.annotationRect):null})))).filter(Boolean))}}}]);