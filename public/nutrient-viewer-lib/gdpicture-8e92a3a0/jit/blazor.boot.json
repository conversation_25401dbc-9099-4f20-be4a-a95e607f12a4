{"mainAssemblyName": "GdPicture.NET.PSPDFKit.Wasm.dll", "resources": {"hash": "sha256-+WLlKIw+ltsFwwdgLb2morhNAQhMzC75o45X1UU+ZVs=", "jsModuleNative": {"dotnet.native.js": "sha256-P++zq7obvn3R6fkSuOuS0yEe5o6lgymGM7BE4A9mggE="}, "jsModuleRuntime": {"dotnet.runtime.js": "sha256-t76BRXwt7ie+t1popBz8/uUmIQCWeGU9XALjF0psLNw="}, "wasmNative": {"dotnet.native.wasm": "sha256-u8xSe5H7Mxh5teCk7A7lnxZh4PnxrycpMq2uYT6ZBSs="}, "icu": {"icudt_CJK.dat": "sha256-SZLtQnRc0JkwqHab0VUVP7T3uBPSeYzxzDnpxPpUnHk=", "icudt_EFIGS.dat": "sha256-8fItetYY8kQ0ww6oxwTLiT3oXlBwHKumbeP2pRF4yTc=", "icudt_no_CJK.dat": "sha256-L7sV7NEYP37/Qr2FPCePo5cJqRgTXRwGHuwF5Q+0Nfs="}, "assembly": {"BouncyCastle.Cryptography.wasm": "sha256-y1d6wFhgAFYamc1avFTyzlKdvfrlD/UR//mXn7zbhB8=", "DocumentFormat.OpenXml.wasm": "sha256-4/VGsFQfMlsoH9d49E2HzOeyFWfu1auDOlDhZpulhOE=", "DocumentFormat.OpenXml.Framework.wasm": "sha256-U6H0i85ISDAFwCASjEs+I27HteLJmzOkH8U9d11V4wA=", "GdPicture.NET.14.API.wasm": "sha256-n0WGhSxl+k19rz4vQJMiPZe/t7L878l/qaUhKlI2bHk=", "GdPicture.NET.14.barcode.1d.writer.wasm": "sha256-NzB55R55ETSzWBe8Ex9s7hqFuW6CE9lxgH9lh/UhPQ4=", "GdPicture.NET.14.barcode.2d.writer.wasm": "sha256-6M2EabXCNWKLUVAc6gFml5uvdjQVqSesJy2tXTUevDc=", "GdPicture.NET.14.CAD.wasm": "sha256-m+Ak3JkqHG8hCR9yiifbkYXQAsb1+w4kjII8CaQGcDE=", "GdPicture.NET.14.CAD.DWG.wasm": "sha256-+CVMl/9SvqbKo04LIv4Ajn4cp0oj5chbYaxB/txbIBw=", "GdPicture.NET.14.Common.wasm": "sha256-w61R0BQdURIKeJAGl79R4u2heumbZ/84oMcF7gBi3Po=", "GdPicture.NET.14.Document.wasm": "sha256-7xC4mIThDWoYJi8zM5Pu7tkELB5FIG16waPuiWXEiZE=", "GdPicture.NET.14.Email.wasm": "sha256-2UqttuTarqKhBSNf/fV4PcJwn41xb9jIdJoi+tb3RaQ=", "GdPicture.NET.14.HTML.wasm": "sha256-n8z7RIhLVi6Kon+U+KXV1NLlBXE20sF/zEsjEYjA1wY=", "GdPicture.NET.14.Imaging.wasm": "sha256-J4vk4OyfomEGB94FM8V3X6zV6oF2SE2hDugI/e+pSOU=", "GdPicture.NET.14.Imaging.Formats.Conversion.wasm": "sha256-++lO0WQNuOfBXr8n+ABKOj+kLeIMVIQ2WmSUuhVJksk=", "GdPicture.NET.14.Imaging.Formats.wasm": "sha256-+hEoTCe4L0LNs/AeLoyMbHrh8qSw2BIgtJCJuP2WD/w=", "GdPicture.NET.14.Imaging.Rendering.wasm": "sha256-ekpb9VQQS623ZpO6Ys6laeDZWy/fIpAMUAX5xYEwaco=", "GdPicture.NET.14.MSOfficeBinary.wasm": "sha256-Jl3vbA32E5Cr4WcJqbtjAn0VYfHCm88c9E7oHerTncQ=", "GdPicture.NET.14.OpenDocument.wasm": "sha256-uJKRuZSLzxyROsbjcPhBaxQpPJv4w5snulUM37TIPGc=", "GdPicture.NET.14.OpenXML.wasm": "sha256-QTmThrmZhx+PLII/Lvs9EVynfh8xA9a3ByjR5CcMKCs=", "GdPicture.NET.14.OpenXML.Templating.wasm": "sha256-FRkWBQHFY2PsJuAGCPVNNTwGQD0VeMAJO9eWpX14m0Q=", "GdPicture.NET.14.PDF.wasm": "sha256-pnaKb09q4pym7pkzZYkc5ZN/3EJNVA9cEMtbpP3nAHo=", "GdPicture.NET.14.RTF.wasm": "sha256-q23JUeWHiS1xzNAuZpT7plTDIwMJHgxgWhEKdfRRr+Y=", "GdPicture.NET.14.SVG.wasm": "sha256-v2oJg5A9A8CT34v73x8KFl6ETy4Hsoqw+pktMBORkdI=", "GdPicture.NET.JsonApi.wasm": "sha256-KibqkyUUCpZIvNHwcbQQCFB75lvypBt0KjhPx+HGtys=", "GdPicture.NET.PSPDFKit.Wasm.wasm": "sha256-UCHsuLVsdWFXnCZLfix8yml/YQce97JGsyX94YMG/Mo=", "GdPicture.NET.Wasm.wasm": "sha256-mPTBM3xTF6Ql1u0BT7lF66FLEDIPXQJW/A+H4Q1/evg=", "Microsoft.CSharp.wasm": "sha256-cUpv5FP/TtTdPuIN5wA+R3Nvul+yFQUp5mk/j0dToRw=", "Newtonsoft.Json.wasm": "sha256-m9cv732Rrm46z5J/pvXgBXpfgs5S70rAWxmPGziO4Mg=", "OpenMcdf.wasm": "sha256-wUgdnsriy9cZvlPsvhCN4leasgWvYMhm5qGHiP5/brc=", "protobuf-net.Core.wasm": "sha256-ySD8yV1RHvQLYveCQy169RQH5Obccnl5wQg7Uc4xL84=", "protobuf-net.wasm": "sha256-zBDF+jKwz+vR8HekYvmgCuOuULgQ0Bsceax49lXdyfI=", "RtfPipe.wasm": "sha256-3FHRVYJ5KNZ7uWogZCE45lZ//jfH5IULrRdfVg4GRLQ=", "System.Collections.Concurrent.wasm": "sha256-veBqWdTuCzeqoZyHGIsCZCwJC4yyVYF8LxkD6Gq5U7o=", "System.Collections.wasm": "sha256-6KNXCYWvtk9MkneeOxnkCpFqFJaBYvrKPCza6Ow3Bh0=", "System.Collections.Immutable.wasm": "sha256-ABUb1py3lskD1wPj8b8M3Ah33CvqqVCptH/bjgYIuVo=", "System.Collections.NonGeneric.wasm": "sha256-a4CsCwp35oxWEdtJI05jzRSPoUFJlj8xVFzfrFEpUfM=", "System.Collections.Specialized.wasm": "sha256-1xInD1QXEsVe3z9B7MvyuhhvVAjX13+gLaM/4kTAuko=", "System.ComponentModel.wasm": "sha256-RvnLrIfJqRZZwcLJQFXF0ttjWRXPz0rczthkg6tCPlQ=", "System.ComponentModel.EventBasedAsync.wasm": "sha256-Qg2SrrYgWhAXiRkv+AW0O93UUZoQKe7YSOFB5zoEDbI=", "System.ComponentModel.Primitives.wasm": "sha256-98x3tESesrwsHrzyqPW0NQdF3CboOPb3zUJuhD5XQZQ=", "System.ComponentModel.TypeConverter.wasm": "sha256-IHF+UWOIi1ULnmWAMIsZ1t92hluhUmvnSVG+I5gtXAM=", "System.Console.wasm": "sha256-GLXj1w1eqv7czVXpkYPH1EegmMiJMuUz2CRmXnmHLk8=", "System.Data.Common.wasm": "sha256-9MtPzJg6M2FX+4TcfIRm8lGi9tSvOSpYL7sqlSmfDgM=", "System.Diagnostics.DiagnosticSource.wasm": "sha256-xwkM6k7wgUyLtENoszTIuMxbDb/od0LeBMqud6P8+n8=", "System.Diagnostics.Process.wasm": "sha256-ZfD6dEyrZWsBFzPecrmtg41B0WoY2dNoItCkFHSUkl4=", "System.Diagnostics.TraceSource.wasm": "sha256-30gn/M6sS07l/ovohH2OkgXutnsT0r6U8SHfLFu2Km4=", "System.wasm": "sha256-CxmrkIvEXnmahYbn2po13+vl7kqlVdf4Xo1maCNJOzE=", "System.Drawing.wasm": "sha256-vaOzvdvNxiyNMoOBqKTppRtjmCX5DrNuxLshDWzZPxo=", "System.Drawing.Primitives.wasm": "sha256-8U+IZlcN/qD74sXxEL2fZZr9/UEN/0BUbqLXLBFaqVw=", "System.Formats.Asn1.wasm": "sha256-gCeI9wxJTlUQq8TbUXcvUPznyciAx+dV9SYY/It45Mg=", "System.IO.Compression.wasm": "sha256-kf4Po52IFmETV3bKi0FgNbsDacSL6D+LZTjA8PoQBD8=", "System.IO.Compression.ZipFile.wasm": "sha256-9v8yzOkDU2KfUbsQHp3b+pfng8ghd9krK2uXcsjpv1A=", "System.IO.Packaging.wasm": "sha256-4I7Fje7VKP53kPyKO66fMI2+pKNKGqcBtHJ4M56w9zI=", "System.Linq.wasm": "sha256-FkTmZbPl2KMXeEVu3xp6R3i6eH60OKJFmUfEDZyLoNk=", "System.Linq.Expressions.wasm": "sha256-WcVVOaYgdT8NDftBWUYF6N5uKu70lBzP3Sv/7R0wJK4=", "System.Memory.wasm": "sha256-pXwksNTLS30v3mw7SUldcCbR26nlXB+Afsc1BJksvUo=", "System.Net.Http.wasm": "sha256-9JLSliZJITUW/eX41i+6FgKe7Etc3nYyM6tJRk62VuM=", "System.Net.Http.Formatting.wasm": "sha256-DD62WmwC0Z26x0/puGyvB/gHL8wehL0jg4jAgs+zECs=", "System.Net.Mail.wasm": "sha256-8Uw4Uz7vY7jlFL4PkdoHAlC7J2pTyNLHWx2URbusgCQ=", "System.Net.Primitives.wasm": "sha256-pp41Z9VMs8WUslOgcGq3IByBFpoyfr5hzy2r5M+4q4E=", "System.Net.Requests.wasm": "sha256-VMq3ICg+PdqrVM4Ll8mlPtjnz+vZ4lzVzZJbNvc8jH4=", "System.Net.Security.wasm": "sha256-G5MTUJoo9Oz4MRrLt7RvPAboLAk4ejUdOjsypz0GxMU=", "System.Net.ServicePoint.wasm": "sha256-tqEnzSy8Iec5IbyY/XvGEbPw1OrEjEhxcNtvktEWTUk=", "System.Net.Sockets.wasm": "sha256-byXUbulPoeV41NGUkL+ZUYqaHBXqQt6uSIC/nEFohhw=", "System.Net.WebHeaderCollection.wasm": "sha256-s9msLGbZgS/ORXwvwsl2/O0l5rrYC5LQAzQA7SxGfJ8=", "System.Net.WebSockets.Client.wasm": "sha256-miRHgCiluYasxT5/hCRCZsqYS+Dn3mYTH9KeElAXxCg=", "System.Net.WebSockets.wasm": "sha256-bLuYGv00tp998jAjugfBAshiDnEXNdvCSJW9zjM5Nl8=", "System.ObjectModel.wasm": "sha256-RePGfaqOJ9SsxTL7iEHP3ijrEsGyLA1yMmwebdYbN5E=", "System.Private.CoreLib.wasm": "sha256-LrPeZ+K6UPGgCvXrp/y1McpEgngQlQdEpLahbNhc/bM=", "System.Private.Uri.wasm": "sha256-oi5aBF8RjM0McwbPGqcUA5W9zXsehC++N1br54kcwHQ=", "System.Private.Xml.wasm": "sha256-ZnqFgxQd6Kj6asO9kbPByukikB29OvFsHN1picpNglI=", "System.Private.Xml.Linq.wasm": "sha256-inDEUW5AbxXJmjonXJH4Z6uk3KAxtO0DrQDYVZ9jHhg=", "System.Runtime.InteropServices.JavaScript.wasm": "sha256-9jXqd/LwCdngqp+uGINU42RnoXBx2jUW3ae+WwUKNH4=", "System.Runtime.Numerics.wasm": "sha256-G2kbe/nYnr60aKaAe8tWbwKoxOkhvP06E+tUEPTRi0U=", "System.Runtime.Serialization.Formatters.wasm": "sha256-JQqvvM2rNcI+QSvfkgspBI/pj06CqGUCBDI7t6a5G9s=", "System.Runtime.Serialization.Primitives.wasm": "sha256-9YqA+jZhCzyL/ycwBxgnfit0OZIWSK7KgKO9nKXXelo=", "System.Security.Cryptography.wasm": "sha256-SIXsRYcvMdjx5aNLgrJxZdyCI1KXFdAxp5gYqplkR/k=", "System.Security.Cryptography.Pkcs.wasm": "sha256-KfNZrpvTzTgqpue4krG8O/Wd9gw30Bh7SQ0wGsIyaks=", "System.Text.Encoding.CodePages.wasm": "sha256-hhyGFgQggqJNKkht6lkYKWWFLbu1D2gMWqG9Igka634=", "System.Text.Encodings.Web.wasm": "sha256-j/1CYb1fNIeEPhB5v5cXY6fZCZPcFE2H0CVr3X5PBVw=", "System.Text.Json.wasm": "sha256-u0LkQymCemld50QI9Sk0Ojzd9JjnMFnAa9Yp6Rj6vLg=", "System.Text.RegularExpressions.wasm": "sha256-xAGbvGlN1E4LznZ+DzjuWm+OpYzlGlZaXla3/xHJNi8=", "System.Threading.Tasks.Parallel.wasm": "sha256-3lhyeOhbHzrsdisvKoEv5kCPkXSR96Z3N6FlyyxJXBE=", "System.Windows.Extensions.wasm": "sha256-XGQl4AUYuHCXVYK5DAd57je7y5lUDlwQKhZ0yv4hd8A=", "System.Xml.Linq.wasm": "sha256-jEsvRrM84rKTsLCIlTDxIQCsmGQgtJePedp2XnoG8yw="}, "vfs": {"runtimeconfig.bin": {"supportFiles/0_runtimeconfig.bin": "sha256-bZVADVE9/vuk4VK9UcMd3AREONFSoWULYacsy5DHL3I="}}}, "debugLevel": 0, "globalizationMode": "sharded"}