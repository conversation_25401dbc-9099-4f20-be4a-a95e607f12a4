import{dotnet}from"./dotnet.js";export async function initDotnet(t,e=void 0){if(null===t||"string"!=typeof t||0===t.trim().length)throw Error("`baseUrl` must be a string passed to `initDotnet` and be non-empty.");const{getAssemblyExports:i,getConfig:o,Module:n}=await dotnet.withConfig({locateFile:e=>`${t}/${e}`}).withResourceLoader(e).create();globalThis.gdPicture={module:n,baseUrl:t};const s=await i(o().mainAssemblyName);return await s.GdPictureWasm.API.Initialize(),{Assemblies:s,Module:n,BaseUrl:t}}