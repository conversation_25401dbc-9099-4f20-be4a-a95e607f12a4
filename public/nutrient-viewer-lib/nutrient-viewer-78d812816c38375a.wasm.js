/*!
 * Nutrient Web SDK 1.4.1 (https://www.nutrient.io/sdk/web)
 * 
 * Copyright (c) 2016-2025 PSPDFKit GmbH. All rights reserved.
 * 
 * THIS SOURCE CODE AND ANY ACCOMPANYING DOCUMENTATION ARE PROTECTED BY INTERNATIONAL COPYRIGHT LAW
 * AND MAY NOT BE RESOLD OR REDISTRIBUTED. USAGE IS BOUND TO THE PSPDFKIT LICENSE AGREEMENT.
 * UNAUTHORIZED REPRODUCTION OR DISTRIBUTION IS SUBJECT TO CIVIL AND CRIMINAL PENALTIES.
 * This notice may not be removed from this file.
 * 
 * PSPDFKit uses several open source third-party components: https://www.nutrient.io/legal/acknowledgements/web-acknowledgements/
 */
var PSPDFModuleInit=(()=>{var e="undefined"!=typeof document?document.currentScript?.src:void 0;return"undefined"!=typeof __filename&&(e=e||__filename),function(r={}){var t,n,o,a,i=r,s=new Promise(((e,r)=>{t=e,n=r})),l="object"==typeof window,u="undefined"!=typeof WorkerGlobalScope,d="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node&&"renderer"!=process.type,c=Object.assign({},i),f="./this.program",h=(e,r)=>{throw r},p="";if(d){var m=require("fs");require("path");p=__dirname+"/",a=e=>(e=V(e)?new URL(e):e,m.readFileSync(e)),o=async(e,r=!0)=>(e=V(e)?new URL(e):e,m.readFileSync(e,r?void 0:"utf8")),!i.thisProgram&&process.argv.length>1&&(f=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),h=(e,r)=>{throw process.exitCode=e,r}}else(l||u)&&(u?p=self.location.href:"undefined"!=typeof document&&document.currentScript&&(p=document.currentScript.src),e&&(p=e),p=p.startsWith("blob:")?"":p.substr(0,p.replace(/[?#].*/,"").lastIndexOf("/")+1),u&&(a=e=>{var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),o=async e=>{if(V(e))return new Promise(((r,t)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?r(n.response):t(n.status)},n.onerror=t,n.send(null)}));var r=await fetch(e,{credentials:"same-origin"});if(r.ok)return r.arrayBuffer();throw new Error(r.status+" : "+r.url)});var v=i.print||console.log.bind(console),y=i.printErr||console.error.bind(console);Object.assign(i,c),c=null,i.arguments&&i.arguments,i.thisProgram&&(f=i.thisProgram);var g,w,E,b,_,k,$,C,P,T,D=i.wasmBinary,F=!1;function S(e,r){e||W(r)}function M(){var e=g.buffer;i.HEAP8=E=new Int8Array(e),i.HEAP16=_=new Int16Array(e),i.HEAPU8=b=new Uint8Array(e),i.HEAPU16=k=new Uint16Array(e),i.HEAP32=$=new Int32Array(e),i.HEAPU32=C=new Uint32Array(e),i.HEAPF32=P=new Float32Array(e),i.HEAPF64=T=new Float64Array(e)}var A=[],j=[],O=[],R=!1;var N=0,x=null;function z(e){N++,i.monitorRunDependencies?.(N)}function U(e){if(N--,i.monitorRunDependencies?.(N),0==N&&x){var r=x;x=null,r()}}function W(e){i.onAbort?.(e),y(e="Aborted("+e+")"),F=!0,e+=". Build with -sASSERTIONS for more info.",R&&At();var r=new WebAssembly.RuntimeError(e);throw n(r),r}var L,I,B,H=e=>e.startsWith("data:application/octet-stream;base64,"),V=e=>e.startsWith("file://");function Y(){var e,r="nutrient-viewer.wasm.wasm";return H(r)?r:(e=r,i.locateFile?i.locateFile(e,p):p+e)}async function q(e){if(!D)try{var r=await o(e);return new Uint8Array(r)}catch{}return function(e){if(e==L&&D)return new Uint8Array(D);if(a)return a(e);throw"both async and sync fetching of the wasm failed"}(e)}async function G(e,r,t){if(!(e||"function"!=typeof WebAssembly.instantiateStreaming||H(r)||V(r)||d||"function"!=typeof fetch))try{var n=fetch(r,{credentials:"same-origin"});return await WebAssembly.instantiateStreaming(n,t)}catch(e){y(`wasm streaming compile failed: ${e}`),y("falling back to ArrayBuffer instantiation")}return async function(e,r){try{var t=await q(e);return await WebAssembly.instantiate(t,r)}catch(e){y(`failed to asynchronously prepare wasm: ${e}`),W(e)}}(r,t)}var X={3158212:()=>"undefined"!=typeof wasmOffsetConverter};class J{name="ExitStatus";constructor(e){this.message=`Program terminated with exit(${e})`,this.status=e}}var K=e=>{for(;e.length>0;)e.shift()(i)},Z=i.noExitRuntime||!0,Q={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,r)=>{for(var t=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),t++):t&&(e.splice(n,1),t--)}if(r)for(;t;t--)e.unshift("..");return e},normalize:e=>{var r=Q.isAbs(e),t="/"===e.substr(-1);return(e=Q.normalizeArray(e.split("/").filter((e=>!!e)),!r).join("/"))||r||(e="."),e&&t&&(e+="/"),(r?"/":"")+e},dirname:e=>{var r=Q.splitPath(e),t=r[0],n=r[1];return t||n?(n&&(n=n.substr(0,n.length-1)),t+n):"."},basename:e=>{if("/"===e)return"/";var r=(e=(e=Q.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===r?e:e.substr(r+1)},join:(...e)=>Q.normalize(e.join("/")),join2:(e,r)=>Q.normalize(e+"/"+r)},ee=e=>(ee=(()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return e=>crypto.getRandomValues(e);if(d)try{var e=require("crypto");if(e.randomFillSync)return r=>e.randomFillSync(r);var r=e.randomBytes;return e=>(e.set(r(e.byteLength)),e)}catch(e){}W("initRandomDevice")})())(e),re={resolve:(...e)=>{for(var r="",t=!1,n=e.length-1;n>=-1&&!t;n--){var o=n>=0?e[n]:ye.cwd();if("string"!=typeof o)throw new TypeError("Arguments to path.resolve must be strings");if(!o)return"";r=o+"/"+r,t=Q.isAbs(o)}return(t?"/":"")+(r=Q.normalizeArray(r.split("/").filter((e=>!!e)),!t).join("/"))||"."},relative:(e,r)=>{function t(e){for(var r=0;r<e.length&&""===e[r];r++);for(var t=e.length-1;t>=0&&""===e[t];t--);return r>t?[]:e.slice(r,t-r+1)}e=re.resolve(e).substr(1),r=re.resolve(r).substr(1);for(var n=t(e.split("/")),o=t(r.split("/")),a=Math.min(n.length,o.length),i=a,s=0;s<a;s++)if(n[s]!==o[s]){i=s;break}var l=[];for(s=i;s<n.length;s++)l.push("..");return(l=l.concat(o.slice(i))).join("/")}},te="undefined"!=typeof TextDecoder?new TextDecoder:void 0,ne=(e,r=0,t=NaN)=>{for(var n=r+t,o=r;e[o]&&!(o>=n);)++o;if(o-r>16&&e.buffer&&te)return te.decode(e.subarray(r,o));for(var a="";r<o;){var i=e[r++];if(128&i){var s=63&e[r++];if(192!=(224&i)){var l=63&e[r++];if((i=224==(240&i)?(15&i)<<12|s<<6|l:(7&i)<<18|s<<12|l<<6|63&e[r++])<65536)a+=String.fromCharCode(i);else{var u=i-65536;a+=String.fromCharCode(55296|u>>10,56320|1023&u)}}else a+=String.fromCharCode((31&i)<<6|s)}else a+=String.fromCharCode(i)}return a},oe=[],ae=e=>{for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n<=127?r++:n<=2047?r+=2:n>=55296&&n<=57343?(r+=4,++t):r+=3}return r},ie=(e,r,t,n)=>{if(!(n>0))return 0;for(var o=t,a=t+n-1,i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++i);if(s<=127){if(t>=a)break;r[t++]=s}else if(s<=2047){if(t+1>=a)break;r[t++]=192|s>>6,r[t++]=128|63&s}else if(s<=65535){if(t+2>=a)break;r[t++]=224|s>>12,r[t++]=128|s>>6&63,r[t++]=128|63&s}else{if(t+3>=a)break;r[t++]=240|s>>18,r[t++]=128|s>>12&63,r[t++]=128|s>>6&63,r[t++]=128|63&s}}return r[t]=0,t-o};function se(e,r,t){var n=t>0?t:ae(e)+1,o=new Array(n),a=ie(e,o,0,o.length);return r&&(o.length=a),o}var le={ttys:[],init(){},shutdown(){},register(e,r){le.ttys[e]={input:[],output:[],ops:r},ye.registerDevice(e,le.stream_ops)},stream_ops:{open(e){var r=le.ttys[e.node.rdev];if(!r)throw new ye.ErrnoError(43);e.tty=r,e.seekable=!1},close(e){e.tty.ops.fsync(e.tty)},fsync(e){e.tty.ops.fsync(e.tty)},read(e,r,t,n,o){if(!e.tty||!e.tty.ops.get_char)throw new ye.ErrnoError(60);for(var a=0,i=0;i<n;i++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new ye.ErrnoError(29)}if(void 0===s&&0===a)throw new ye.ErrnoError(6);if(null==s)break;a++,r[t+i]=s}return a&&(e.node.atime=Date.now()),a},write(e,r,t,n,o){if(!e.tty||!e.tty.ops.put_char)throw new ye.ErrnoError(60);try{for(var a=0;a<n;a++)e.tty.ops.put_char(e.tty,r[t+a])}catch(e){throw new ye.ErrnoError(29)}return n&&(e.node.mtime=e.node.ctime=Date.now()),a}},default_tty_ops:{get_char:e=>(()=>{if(!oe.length){var e=null;if(d){var r=Buffer.alloc(256),t=0,n=process.stdin.fd;try{t=m.readSync(n,r,0,256)}catch(e){if(!e.toString().includes("EOF"))throw e;t=0}t>0&&(e=r.slice(0,t).toString("utf-8"))}else"undefined"!=typeof window&&"function"==typeof window.prompt&&null!==(e=window.prompt("Input: "))&&(e+="\n");if(!e)return null;oe=se(e,!0)}return oe.shift()})(),put_char(e,r){null===r||10===r?(v(ne(e.output)),e.output=[]):0!=r&&e.output.push(r)},fsync(e){e.output&&e.output.length>0&&(v(ne(e.output)),e.output=[])},ioctl_tcgets:e=>({c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}),ioctl_tcsets:(e,r,t)=>0,ioctl_tiocgwinsz:e=>[24,80]},default_tty1_ops:{put_char(e,r){null===r||10===r?(y(ne(e.output)),e.output=[]):0!=r&&e.output.push(r)},fsync(e){e.output&&e.output.length>0&&(y(ne(e.output)),e.output=[])}}},ue=(e,r)=>Math.ceil(e/r)*r,de=e=>{e=ue(e,65536);var r=St(65536,e);return r&&((e,r)=>{b.fill(0,e,e+r)})(r,e),r},ce={ops_table:null,mount:e=>ce.createNode(null,"/",16895,0),createNode(e,r,t,n){if(ye.isBlkdev(t)||ye.isFIFO(t))throw new ye.ErrnoError(63);ce.ops_table||={dir:{node:{getattr:ce.node_ops.getattr,setattr:ce.node_ops.setattr,lookup:ce.node_ops.lookup,mknod:ce.node_ops.mknod,rename:ce.node_ops.rename,unlink:ce.node_ops.unlink,rmdir:ce.node_ops.rmdir,readdir:ce.node_ops.readdir,symlink:ce.node_ops.symlink},stream:{llseek:ce.stream_ops.llseek}},file:{node:{getattr:ce.node_ops.getattr,setattr:ce.node_ops.setattr},stream:{llseek:ce.stream_ops.llseek,read:ce.stream_ops.read,write:ce.stream_ops.write,allocate:ce.stream_ops.allocate,mmap:ce.stream_ops.mmap,msync:ce.stream_ops.msync}},link:{node:{getattr:ce.node_ops.getattr,setattr:ce.node_ops.setattr,readlink:ce.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ce.node_ops.getattr,setattr:ce.node_ops.setattr},stream:ye.chrdev_stream_ops}};var o=ye.createNode(e,r,t,n);return ye.isDir(o.mode)?(o.node_ops=ce.ops_table.dir.node,o.stream_ops=ce.ops_table.dir.stream,o.contents={}):ye.isFile(o.mode)?(o.node_ops=ce.ops_table.file.node,o.stream_ops=ce.ops_table.file.stream,o.usedBytes=0,o.contents=null):ye.isLink(o.mode)?(o.node_ops=ce.ops_table.link.node,o.stream_ops=ce.ops_table.link.stream):ye.isChrdev(o.mode)&&(o.node_ops=ce.ops_table.chrdev.node,o.stream_ops=ce.ops_table.chrdev.stream),o.atime=o.mtime=o.ctime=Date.now(),e&&(e.contents[r]=o,e.atime=e.mtime=e.ctime=o.atime),o},getFileDataAsTypedArray:e=>e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0),expandFileStorage(e,r){var t=e.contents?e.contents.length:0;if(!(t>=r)){r=Math.max(r,t*(t<1048576?2:1.125)>>>0),0!=t&&(r=Math.max(r,256));var n=e.contents;e.contents=new Uint8Array(r),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage(e,r){if(e.usedBytes!=r)if(0==r)e.contents=null,e.usedBytes=0;else{var t=e.contents;e.contents=new Uint8Array(r),t&&e.contents.set(t.subarray(0,Math.min(r,e.usedBytes))),e.usedBytes=r}},node_ops:{getattr(e){var r={};return r.dev=ye.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,ye.isDir(e.mode)?r.size=4096:ye.isFile(e.mode)?r.size=e.usedBytes:ye.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.atime),r.mtime=new Date(e.mtime),r.ctime=new Date(e.ctime),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr(e,r){for(const t of["mode","atime","mtime","ctime"])r[t]&&(e[t]=r[t]);void 0!==r.size&&ce.resizeFileStorage(e,r.size)},lookup(e,r){throw ce.doesNotExistError},mknod:(e,r,t,n)=>ce.createNode(e,r,t,n),rename(e,r,t){var n;try{n=ye.lookupNode(r,t)}catch(e){}if(n){if(ye.isDir(e.mode))for(var o in n.contents)throw new ye.ErrnoError(55);ye.hashRemoveNode(n)}delete e.parent.contents[e.name],r.contents[t]=e,e.name=t,r.ctime=r.mtime=e.parent.ctime=e.parent.mtime=Date.now()},unlink(e,r){delete e.contents[r],e.ctime=e.mtime=Date.now()},rmdir(e,r){var t=ye.lookupNode(e,r);for(var n in t.contents)throw new ye.ErrnoError(55);delete e.contents[r],e.ctime=e.mtime=Date.now()},readdir:e=>[".","..",...Object.keys(e.contents)],symlink(e,r,t){var n=ce.createNode(e,r,41471,0);return n.link=t,n},readlink(e){if(!ye.isLink(e.mode))throw new ye.ErrnoError(28);return e.link}},stream_ops:{read(e,r,t,n,o){var a=e.node.contents;if(o>=e.node.usedBytes)return 0;var i=Math.min(e.node.usedBytes-o,n);if(i>8&&a.subarray)r.set(a.subarray(o,o+i),t);else for(var s=0;s<i;s++)r[t+s]=a[o+s];return i},write(e,r,t,n,o,a){if(r.buffer===E.buffer&&(a=!1),!n)return 0;var i=e.node;if(i.mtime=i.ctime=Date.now(),r.subarray&&(!i.contents||i.contents.subarray)){if(a)return i.contents=r.subarray(t,t+n),i.usedBytes=n,n;if(0===i.usedBytes&&0===o)return i.contents=r.slice(t,t+n),i.usedBytes=n,n;if(o+n<=i.usedBytes)return i.contents.set(r.subarray(t,t+n),o),n}if(ce.expandFileStorage(i,o+n),i.contents.subarray&&r.subarray)i.contents.set(r.subarray(t,t+n),o);else for(var s=0;s<n;s++)i.contents[o+s]=r[t+s];return i.usedBytes=Math.max(i.usedBytes,o+n),n},llseek(e,r,t){var n=r;if(1===t?n+=e.position:2===t&&ye.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new ye.ErrnoError(28);return n},allocate(e,r,t){ce.expandFileStorage(e.node,r+t),e.node.usedBytes=Math.max(e.node.usedBytes,r+t)},mmap(e,r,t,n,o){if(!ye.isFile(e.node.mode))throw new ye.ErrnoError(43);var a,i,s=e.node.contents;if(2&o||!s||s.buffer!==E.buffer){if(i=!0,!(a=de(r)))throw new ye.ErrnoError(48);s&&((t>0||t+r<s.length)&&(s=s.subarray?s.subarray(t,t+r):Array.prototype.slice.call(s,t,t+r)),E.set(s,a))}else i=!1,a=s.byteOffset;return{ptr:a,allocated:i}},msync:(e,r,t,n,o)=>(ce.stream_ops.write(e,r,0,n,t,!1),0)}},fe=(e,r,t,n,o,a)=>{ye.createDataFile(e,r,t,n,o,a)},he=i.preloadPlugins||[],pe=(e,r,t,n,a,i,s,l,u,d)=>{var c=r?re.resolve(Q.join2(e,r)):e;function f(t){function o(t){d?.(),l||fe(e,r,t,n,a,u),i?.(),U()}((e,r,t,n)=>{"undefined"!=typeof Browser&&Browser.init();var o=!1;return he.forEach((a=>{o||a.canHandle(r)&&(a.handle(e,r,t,n),o=!0)})),o})(t,c,o,(()=>{s?.(),U()}))||o(t)}z(),"string"==typeof t?(async e=>{var r=await o(e);return new Uint8Array(r)})(t).then(f,s):f(t)},me=(e,r)=>{var t=0;return e&&(t|=365),r&&(t|=146),t},ve={DIR_MODE:16895,FILE_MODE:33279,reader:null,mount(e){S(u),ve.reader??=new FileReaderSync;var r=ve.createNode(null,"/",ve.DIR_MODE,0),t={};function n(e){for(var n=e.split("/"),o=r,a=0;a<n.length-1;a++){var i=n.slice(0,a+1).join("/");t[i]||=ve.createNode(o,n[a],ve.DIR_MODE,0),o=t[i]}return o}function o(e){var r=e.split("/");return r[r.length-1]}return Array.prototype.forEach.call(e.opts.files||[],(function(e){ve.createNode(n(e.name),o(e.name),ve.FILE_MODE,0,e,e.lastModifiedDate)})),(e.opts.blobs||[]).forEach((e=>{ve.createNode(n(e.name),o(e.name),ve.FILE_MODE,0,e.data)})),(e.opts.packages||[]).forEach((e=>{e.metadata.files.forEach((r=>{var t=r.filename.substr(1);ve.createNode(n(t),o(t),ve.FILE_MODE,0,e.blob.slice(r.start,r.end))}))})),r},createNode(e,r,t,n,o,a){var i=ye.createNode(e,r,t);return i.mode=t,i.node_ops=ve.node_ops,i.stream_ops=ve.stream_ops,i.atime=i.mtime=i.ctime=(a||new Date).getTime(),S(ve.FILE_MODE!==ve.DIR_MODE),t===ve.FILE_MODE?(i.size=o.size,i.contents=o):(i.size=4096,i.contents={}),e&&(e.contents[r]=i),i},node_ops:{getattr:e=>({dev:1,ino:e.id,mode:e.mode,nlink:1,uid:0,gid:0,rdev:0,size:e.size,atime:new Date(e.atime),mtime:new Date(e.mtime),ctime:new Date(e.ctime),blksize:4096,blocks:Math.ceil(e.size/4096)}),setattr(e,r){for(const t of["mode","atime","mtime","ctime"])r[t]&&(e[t]=r[t])},lookup(e,r){throw new ye.ErrnoError(44)},mknod(e,r,t,n){throw new ye.ErrnoError(63)},rename(e,r,t){throw new ye.ErrnoError(63)},unlink(e,r){throw new ye.ErrnoError(63)},rmdir(e,r){throw new ye.ErrnoError(63)},readdir(e){var r=[".",".."];for(var t of Object.keys(e.contents))r.push(t);return r},symlink(e,r,t){throw new ye.ErrnoError(63)}},stream_ops:{read(e,r,t,n,o){if(o>=e.node.size)return 0;var a=e.node.contents.slice(o,o+n),i=ve.reader.readAsArrayBuffer(a);return r.set(new Uint8Array(i),t),a.size},write(e,r,t,n,o){throw new ye.ErrnoError(29)},llseek(e,r,t){var n=r;if(1===t?n+=e.position:2===t&&ye.isFile(e.node.mode)&&(n+=e.node.size),n<0)throw new ye.ErrnoError(28);return n}}},ye={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:class{name="ErrnoError";constructor(e){this.errno=e}},filesystems:null,syncFSRequests:0,readFiles:{},FSStream:class{shared={};get object(){return this.node}set object(e){this.node=e}get isRead(){return 1!=(2097155&this.flags)}get isWrite(){return!!(2097155&this.flags)}get isAppend(){return 1024&this.flags}get flags(){return this.shared.flags}set flags(e){this.shared.flags=e}get position(){return this.shared.position}set position(e){this.shared.position=e}},FSNode:class{node_ops={};stream_ops={};readMode=365;writeMode=146;mounted=null;constructor(e,r,t,n){e||(e=this),this.parent=e,this.mount=e.mount,this.id=ye.nextInode++,this.name=r,this.mode=t,this.rdev=n,this.atime=this.mtime=this.ctime=Date.now()}get read(){return(this.mode&this.readMode)===this.readMode}set read(e){e?this.mode|=this.readMode:this.mode&=~this.readMode}get write(){return(this.mode&this.writeMode)===this.writeMode}set write(e){e?this.mode|=this.writeMode:this.mode&=~this.writeMode}get isFolder(){return ye.isDir(this.mode)}get isDevice(){return ye.isChrdev(this.mode)}},lookupPath(e,r={}){if(!e)return{path:"",node:null};r.follow_mount??=!0,Q.isAbs(e)||(e=ye.cwd()+"/"+e);e:for(var t=0;t<40;t++){for(var n=e.split("/").filter((e=>!!e&&"."!==e)),o=ye.root,a="/",i=0;i<n.length;i++){var s=i===n.length-1;if(s&&r.parent)break;if(".."!==n[i]){a=Q.join2(a,n[i]);try{o=ye.lookupNode(o,n[i])}catch(e){if(44===e?.errno&&s&&r.noent_okay)return{path:a};throw e}if(!ye.isMountpoint(o)||s&&!r.follow_mount||(o=o.mounted.root),ye.isLink(o.mode)&&(!s||r.follow)){if(!o.node_ops.readlink)throw new ye.ErrnoError(52);var l=o.node_ops.readlink(o);Q.isAbs(l)||(l=Q.dirname(a)+"/"+l),e=l+"/"+n.slice(i+1).join("/");continue e}}else a=Q.dirname(a),o=o.parent}return{path:a,node:o}}throw new ye.ErrnoError(32)},getPath(e){for(var r;;){if(ye.isRoot(e)){var t=e.mount.mountpoint;return r?"/"!==t[t.length-1]?`${t}/${r}`:t+r:t}r=r?`${e.name}/${r}`:e.name,e=e.parent}},hashName(e,r){for(var t=0,n=0;n<r.length;n++)t=(t<<5)-t+r.charCodeAt(n)|0;return(e+t>>>0)%ye.nameTable.length},hashAddNode(e){var r=ye.hashName(e.parent.id,e.name);e.name_next=ye.nameTable[r],ye.nameTable[r]=e},hashRemoveNode(e){var r=ye.hashName(e.parent.id,e.name);if(ye.nameTable[r]===e)ye.nameTable[r]=e.name_next;else for(var t=ye.nameTable[r];t;){if(t.name_next===e){t.name_next=e.name_next;break}t=t.name_next}},lookupNode(e,r){var t=ye.mayLookup(e);if(t)throw new ye.ErrnoError(t);for(var n=ye.hashName(e.id,r),o=ye.nameTable[n];o;o=o.name_next){var a=o.name;if(o.parent.id===e.id&&a===r)return o}return ye.lookup(e,r)},createNode(e,r,t,n){var o=new ye.FSNode(e,r,t,n);return ye.hashAddNode(o),o},destroyNode(e){ye.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>!(49152&~e),flagsToPermissionString(e){var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:(e,r)=>ye.ignorePermissions||(!r.includes("r")||292&e.mode)&&(!r.includes("w")||146&e.mode)&&(!r.includes("x")||73&e.mode)?0:2,mayLookup(e){if(!ye.isDir(e.mode))return 54;var r=ye.nodePermissions(e,"x");return r||(e.node_ops.lookup?0:2)},mayCreate(e,r){if(!ye.isDir(e.mode))return 54;try{ye.lookupNode(e,r);return 20}catch(e){}return ye.nodePermissions(e,"wx")},mayDelete(e,r,t){var n;try{n=ye.lookupNode(e,r)}catch(e){return e.errno}var o=ye.nodePermissions(e,"wx");if(o)return o;if(t){if(!ye.isDir(n.mode))return 54;if(ye.isRoot(n)||ye.getPath(n)===ye.cwd())return 10}else if(ye.isDir(n.mode))return 31;return 0},mayOpen:(e,r)=>e?ye.isLink(e.mode)?32:ye.isDir(e.mode)&&("r"!==ye.flagsToPermissionString(r)||512&r)?31:ye.nodePermissions(e,ye.flagsToPermissionString(r)):44,MAX_OPEN_FDS:4096,nextfd(){for(var e=0;e<=ye.MAX_OPEN_FDS;e++)if(!ye.streams[e])return e;throw new ye.ErrnoError(33)},getStreamChecked(e){var r=ye.getStream(e);if(!r)throw new ye.ErrnoError(8);return r},getStream:e=>ye.streams[e],createStream:(e,r=-1)=>(e=Object.assign(new ye.FSStream,e),-1==r&&(r=ye.nextfd()),e.fd=r,ye.streams[r]=e,e),closeStream(e){ye.streams[e]=null},dupStream(e,r=-1){var t=ye.createStream(e,r);return t.stream_ops?.dup?.(t),t},chrdev_stream_ops:{open(e){var r=ye.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open?.(e)},llseek(){throw new ye.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,r)=>e<<8|r,registerDevice(e,r){ye.devices[e]={stream_ops:r}},getDevice:e=>ye.devices[e],getMounts(e){for(var r=[],t=[e];t.length;){var n=t.pop();r.push(n),t.push(...n.mounts)}return r},syncfs(e,r){"function"==typeof e&&(r=e,e=!1),ye.syncFSRequests++,ye.syncFSRequests>1&&y(`warning: ${ye.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);var t=ye.getMounts(ye.root.mount),n=0;function o(e){return ye.syncFSRequests--,r(e)}function a(e){if(e)return a.errored?void 0:(a.errored=!0,o(e));++n>=t.length&&o(null)}t.forEach((r=>{if(!r.type.syncfs)return a(null);r.type.syncfs(r,e,a)}))},mount(e,r,t){var n,o="/"===t,a=!t;if(o&&ye.root)throw new ye.ErrnoError(10);if(!o&&!a){var i=ye.lookupPath(t,{follow_mount:!1});if(t=i.path,n=i.node,ye.isMountpoint(n))throw new ye.ErrnoError(10);if(!ye.isDir(n.mode))throw new ye.ErrnoError(54)}var s={type:e,opts:r,mountpoint:t,mounts:[]},l=e.mount(s);return l.mount=s,s.root=l,o?ye.root=l:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),l},unmount(e){var r=ye.lookupPath(e,{follow_mount:!1});if(!ye.isMountpoint(r.node))throw new ye.ErrnoError(28);var t=r.node,n=t.mounted,o=ye.getMounts(n);Object.keys(ye.nameTable).forEach((e=>{for(var r=ye.nameTable[e];r;){var t=r.name_next;o.includes(r.mount)&&ye.destroyNode(r),r=t}})),t.mounted=null;var a=t.mount.mounts.indexOf(n);t.mount.mounts.splice(a,1)},lookup:(e,r)=>e.node_ops.lookup(e,r),mknod(e,r,t){var n=ye.lookupPath(e,{parent:!0}).node,o=Q.basename(e);if(!o||"."===o||".."===o)throw new ye.ErrnoError(28);var a=ye.mayCreate(n,o);if(a)throw new ye.ErrnoError(a);if(!n.node_ops.mknod)throw new ye.ErrnoError(63);return n.node_ops.mknod(n,o,r,t)},statfs(e){var r={bsize:4096,frsize:4096,blocks:1e6,bfree:5e5,bavail:5e5,files:ye.nextInode,ffree:ye.nextInode-1,fsid:42,flags:2,namelen:255},t=ye.lookupPath(e,{follow:!0}).node;return t?.node_ops.statfs&&Object.assign(r,t.node_ops.statfs(t.mount.opts.root)),r},create:(e,r=438)=>(r&=4095,r|=32768,ye.mknod(e,r,0)),mkdir:(e,r=511)=>(r&=1023,r|=16384,ye.mknod(e,r,0)),mkdirTree(e,r){for(var t=e.split("/"),n="",o=0;o<t.length;++o)if(t[o]){n+="/"+t[o];try{ye.mkdir(n,r)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,r,t)=>(void 0===t&&(t=r,r=438),r|=8192,ye.mknod(e,r,t)),symlink(e,r){if(!re.resolve(e))throw new ye.ErrnoError(44);var t=ye.lookupPath(r,{parent:!0}).node;if(!t)throw new ye.ErrnoError(44);var n=Q.basename(r),o=ye.mayCreate(t,n);if(o)throw new ye.ErrnoError(o);if(!t.node_ops.symlink)throw new ye.ErrnoError(63);return t.node_ops.symlink(t,n,e)},rename(e,r){var t,n,o=Q.dirname(e),a=Q.dirname(r),i=Q.basename(e),s=Q.basename(r);if(t=ye.lookupPath(e,{parent:!0}).node,n=ye.lookupPath(r,{parent:!0}).node,!t||!n)throw new ye.ErrnoError(44);if(t.mount!==n.mount)throw new ye.ErrnoError(75);var l,u=ye.lookupNode(t,i),d=re.relative(e,a);if("."!==d.charAt(0))throw new ye.ErrnoError(28);if("."!==(d=re.relative(r,o)).charAt(0))throw new ye.ErrnoError(55);try{l=ye.lookupNode(n,s)}catch(e){}if(u!==l){var c=ye.isDir(u.mode),f=ye.mayDelete(t,i,c);if(f)throw new ye.ErrnoError(f);if(f=l?ye.mayDelete(n,s,c):ye.mayCreate(n,s))throw new ye.ErrnoError(f);if(!t.node_ops.rename)throw new ye.ErrnoError(63);if(ye.isMountpoint(u)||l&&ye.isMountpoint(l))throw new ye.ErrnoError(10);if(n!==t&&(f=ye.nodePermissions(t,"w")))throw new ye.ErrnoError(f);ye.hashRemoveNode(u);try{t.node_ops.rename(u,n,s),u.parent=n}catch(e){throw e}finally{ye.hashAddNode(u)}}},rmdir(e){var r=ye.lookupPath(e,{parent:!0}).node,t=Q.basename(e),n=ye.lookupNode(r,t),o=ye.mayDelete(r,t,!0);if(o)throw new ye.ErrnoError(o);if(!r.node_ops.rmdir)throw new ye.ErrnoError(63);if(ye.isMountpoint(n))throw new ye.ErrnoError(10);r.node_ops.rmdir(r,t),ye.destroyNode(n)},readdir(e){var r=ye.lookupPath(e,{follow:!0}).node;if(!r.node_ops.readdir)throw new ye.ErrnoError(54);return r.node_ops.readdir(r)},unlink(e){var r=ye.lookupPath(e,{parent:!0}).node;if(!r)throw new ye.ErrnoError(44);var t=Q.basename(e),n=ye.lookupNode(r,t),o=ye.mayDelete(r,t,!1);if(o)throw new ye.ErrnoError(o);if(!r.node_ops.unlink)throw new ye.ErrnoError(63);if(ye.isMountpoint(n))throw new ye.ErrnoError(10);r.node_ops.unlink(r,t),ye.destroyNode(n)},readlink(e){var r=ye.lookupPath(e).node;if(!r)throw new ye.ErrnoError(44);if(!r.node_ops.readlink)throw new ye.ErrnoError(28);return r.node_ops.readlink(r)},stat(e,r){var t=ye.lookupPath(e,{follow:!r}).node;if(!t)throw new ye.ErrnoError(44);if(!t.node_ops.getattr)throw new ye.ErrnoError(63);return t.node_ops.getattr(t)},lstat:e=>ye.stat(e,!0),chmod(e,r,t){var n;"string"==typeof e?n=ye.lookupPath(e,{follow:!t}).node:n=e;if(!n.node_ops.setattr)throw new ye.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&r|-4096&n.mode,ctime:Date.now()})},lchmod(e,r){ye.chmod(e,r,!0)},fchmod(e,r){var t=ye.getStreamChecked(e);ye.chmod(t.node,r)},chown(e,r,t,n){var o;"string"==typeof e?o=ye.lookupPath(e,{follow:!n}).node:o=e;if(!o.node_ops.setattr)throw new ye.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown(e,r,t){ye.chown(e,r,t,!0)},fchown(e,r,t){var n=ye.getStreamChecked(e);ye.chown(n.node,r,t)},truncate(e,r){if(r<0)throw new ye.ErrnoError(28);var t;"string"==typeof e?t=ye.lookupPath(e,{follow:!0}).node:t=e;if(!t.node_ops.setattr)throw new ye.ErrnoError(63);if(ye.isDir(t.mode))throw new ye.ErrnoError(31);if(!ye.isFile(t.mode))throw new ye.ErrnoError(28);var n=ye.nodePermissions(t,"w");if(n)throw new ye.ErrnoError(n);t.node_ops.setattr(t,{size:r,timestamp:Date.now()})},ftruncate(e,r){var t=ye.getStreamChecked(e);if(!(2097155&t.flags))throw new ye.ErrnoError(28);ye.truncate(t.node,r)},utime(e,r,t){var n=ye.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{atime:r,mtime:t})},open(e,r,t=438){if(""===e)throw new ye.ErrnoError(44);var n;if(t=64&(r="string"==typeof r?(e=>{var r={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[e];if(void 0===r)throw new Error(`Unknown file open mode: ${e}`);return r})(r):r)?4095&t|32768:0,"object"==typeof e)n=e;else{var o=ye.lookupPath(e,{follow:!(131072&r),noent_okay:!0});n=o.node,e=o.path}var a=!1;if(64&r)if(n){if(128&r)throw new ye.ErrnoError(20)}else n=ye.mknod(e,t,0),a=!0;if(!n)throw new ye.ErrnoError(44);if(ye.isChrdev(n.mode)&&(r&=-513),65536&r&&!ye.isDir(n.mode))throw new ye.ErrnoError(54);if(!a){var s=ye.mayOpen(n,r);if(s)throw new ye.ErrnoError(s)}512&r&&!a&&ye.truncate(n,0),r&=-131713;var l=ye.createStream({node:n,path:ye.getPath(n),flags:r,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return l.stream_ops.open&&l.stream_ops.open(l),!i.logReadFiles||1&r||e in ye.readFiles||(ye.readFiles[e]=1),l},close(e){if(ye.isClosed(e))throw new ye.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{ye.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek(e,r,t){if(ye.isClosed(e))throw new ye.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new ye.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new ye.ErrnoError(28);return e.position=e.stream_ops.llseek(e,r,t),e.ungotten=[],e.position},read(e,r,t,n,o){if(n<0||o<0)throw new ye.ErrnoError(28);if(ye.isClosed(e))throw new ye.ErrnoError(8);if(1==(2097155&e.flags))throw new ye.ErrnoError(8);if(ye.isDir(e.node.mode))throw new ye.ErrnoError(31);if(!e.stream_ops.read)throw new ye.ErrnoError(28);var a=void 0!==o;if(a){if(!e.seekable)throw new ye.ErrnoError(70)}else o=e.position;var i=e.stream_ops.read(e,r,t,n,o);return a||(e.position+=i),i},write(e,r,t,n,o,a){if(n<0||o<0)throw new ye.ErrnoError(28);if(ye.isClosed(e))throw new ye.ErrnoError(8);if(!(2097155&e.flags))throw new ye.ErrnoError(8);if(ye.isDir(e.node.mode))throw new ye.ErrnoError(31);if(!e.stream_ops.write)throw new ye.ErrnoError(28);e.seekable&&1024&e.flags&&ye.llseek(e,0,2);var i=void 0!==o;if(i){if(!e.seekable)throw new ye.ErrnoError(70)}else o=e.position;var s=e.stream_ops.write(e,r,t,n,o,a);return i||(e.position+=s),s},allocate(e,r,t){if(ye.isClosed(e))throw new ye.ErrnoError(8);if(r<0||t<=0)throw new ye.ErrnoError(28);if(!(2097155&e.flags))throw new ye.ErrnoError(8);if(!ye.isFile(e.node.mode)&&!ye.isDir(e.node.mode))throw new ye.ErrnoError(43);if(!e.stream_ops.allocate)throw new ye.ErrnoError(138);e.stream_ops.allocate(e,r,t)},mmap(e,r,t,n,o){if(2&n&&!(2&o)&&2!=(2097155&e.flags))throw new ye.ErrnoError(2);if(1==(2097155&e.flags))throw new ye.ErrnoError(2);if(!e.stream_ops.mmap)throw new ye.ErrnoError(43);if(!r)throw new ye.ErrnoError(28);return e.stream_ops.mmap(e,r,t,n,o)},msync:(e,r,t,n,o)=>e.stream_ops.msync?e.stream_ops.msync(e,r,t,n,o):0,ioctl(e,r,t){if(!e.stream_ops.ioctl)throw new ye.ErrnoError(59);return e.stream_ops.ioctl(e,r,t)},readFile(e,r={}){if(r.flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error(`Invalid encoding type "${r.encoding}"`);var t,n=ye.open(e,r.flags),o=ye.stat(e).size,a=new Uint8Array(o);return ye.read(n,a,0,o,0),"utf8"===r.encoding?t=ne(a):"binary"===r.encoding&&(t=a),ye.close(n),t},writeFile(e,r,t={}){t.flags=t.flags||577;var n=ye.open(e,t.flags,t.mode);if("string"==typeof r){var o=new Uint8Array(ae(r)+1),a=ie(r,o,0,o.length);ye.write(n,o,0,a,void 0,t.canOwn)}else{if(!ArrayBuffer.isView(r))throw new Error("Unsupported data type");ye.write(n,r,0,r.byteLength,void 0,t.canOwn)}ye.close(n)},cwd:()=>ye.currentPath,chdir(e){var r=ye.lookupPath(e,{follow:!0});if(null===r.node)throw new ye.ErrnoError(44);if(!ye.isDir(r.node.mode))throw new ye.ErrnoError(54);var t=ye.nodePermissions(r.node,"x");if(t)throw new ye.ErrnoError(t);ye.currentPath=r.path},createDefaultDirectories(){ye.mkdir("/tmp"),ye.mkdir("/home"),ye.mkdir("/home/<USER>")},createDefaultDevices(){ye.mkdir("/dev"),ye.registerDevice(ye.makedev(1,3),{read:()=>0,write:(e,r,t,n,o)=>n,llseek:()=>0}),ye.mkdev("/dev/null",ye.makedev(1,3)),le.register(ye.makedev(5,0),le.default_tty_ops),le.register(ye.makedev(6,0),le.default_tty1_ops),ye.mkdev("/dev/tty",ye.makedev(5,0)),ye.mkdev("/dev/tty1",ye.makedev(6,0));var e=new Uint8Array(1024),r=0,t=()=>(0===r&&(r=ee(e).byteLength),e[--r]);ye.createDevice("/dev","random",t),ye.createDevice("/dev","urandom",t),ye.mkdir("/dev/shm"),ye.mkdir("/dev/shm/tmp")},createSpecialDirectories(){ye.mkdir("/proc");var e=ye.mkdir("/proc/self");ye.mkdir("/proc/self/fd"),ye.mount({mount(){var r=ye.createNode(e,"fd",16895,73);return r.stream_ops={llseek:ce.stream_ops.llseek},r.node_ops={lookup(e,r){var t=+r,n=ye.getStreamChecked(t),o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>n.path},id:t+1};return o.parent=o,o},readdir:()=>Array.from(ye.streams.entries()).filter((([e,r])=>r)).map((([e,r])=>e.toString()))},r}},{},"/proc/self/fd")},createStandardStreams(e,r,t){e?ye.createDevice("/dev","stdin",e):ye.symlink("/dev/tty","/dev/stdin"),r?ye.createDevice("/dev","stdout",null,r):ye.symlink("/dev/tty","/dev/stdout"),t?ye.createDevice("/dev","stderr",null,t):ye.symlink("/dev/tty1","/dev/stderr");ye.open("/dev/stdin",0),ye.open("/dev/stdout",1),ye.open("/dev/stderr",1)},staticInit(){ye.nameTable=new Array(4096),ye.mount(ce,{},"/"),ye.createDefaultDirectories(),ye.createDefaultDevices(),ye.createSpecialDirectories(),ye.filesystems={MEMFS:ce,WORKERFS:ve}},init(e,r,t){ye.initialized=!0,e??=i.stdin,r??=i.stdout,t??=i.stderr,ye.createStandardStreams(e,r,t)},quit(){ye.initialized=!1;for(var e=0;e<ye.streams.length;e++){var r=ye.streams[e];r&&ye.close(r)}},findObject(e,r){var t=ye.analyzePath(e,r);return t.exists?t.object:null},analyzePath(e,r){try{e=(n=ye.lookupPath(e,{follow:!r})).path}catch(e){}var t={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=ye.lookupPath(e,{parent:!0});t.parentExists=!0,t.parentPath=n.path,t.parentObject=n.node,t.name=Q.basename(e),n=ye.lookupPath(e,{follow:!r}),t.exists=!0,t.path=n.path,t.object=n.node,t.name=n.node.name,t.isRoot="/"===n.path}catch(e){t.error=e.errno}return t},createPath(e,r,t,n){e="string"==typeof e?e:ye.getPath(e);for(var o=r.split("/").reverse();o.length;){var a=o.pop();if(a){var i=Q.join2(e,a);try{ye.mkdir(i)}catch(e){}e=i}}return i},createFile(e,r,t,n,o){var a=Q.join2("string"==typeof e?e:ye.getPath(e),r),i=me(n,o);return ye.create(a,i)},createDataFile(e,r,t,n,o,a){var i=r;e&&(e="string"==typeof e?e:ye.getPath(e),i=r?Q.join2(e,r):e);var s=me(n,o),l=ye.create(i,s);if(t){if("string"==typeof t){for(var u=new Array(t.length),d=0,c=t.length;d<c;++d)u[d]=t.charCodeAt(d);t=u}ye.chmod(l,146|s);var f=ye.open(l,577);ye.write(f,t,0,t.length,0,a),ye.close(f),ye.chmod(l,s)}},createDevice(e,r,t,n){var o=Q.join2("string"==typeof e?e:ye.getPath(e),r),a=me(!!t,!!n);ye.createDevice.major??=64;var i=ye.makedev(ye.createDevice.major++,0);return ye.registerDevice(i,{open(e){e.seekable=!1},close(e){n?.buffer?.length&&n(10)},read(e,r,n,o,a){for(var i=0,s=0;s<o;s++){var l;try{l=t()}catch(e){throw new ye.ErrnoError(29)}if(void 0===l&&0===i)throw new ye.ErrnoError(6);if(null==l)break;i++,r[n+s]=l}return i&&(e.node.atime=Date.now()),i},write(e,r,t,o,a){for(var i=0;i<o;i++)try{n(r[t+i])}catch(e){throw new ye.ErrnoError(29)}return o&&(e.node.mtime=e.node.ctime=Date.now()),i}}),ye.mkdev(o,a,i)},forceLoadFile(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");try{e.contents=a(e.url),e.usedBytes=e.contents.length}catch(e){throw new ye.ErrnoError(29)}},createLazyFile(e,r,t,n,o){class a{lengthKnown=!1;chunks=[];get(e){if(!(e>this.length-1||e<0)){var r=e%this.chunkSize,t=e/this.chunkSize|0;return this.getter(t)[r]}}setDataGetter(e){this.getter=e}cacheLength(){var e=new XMLHttpRequest;if(e.open("HEAD",t,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+t+". Status: "+e.status);var r,n=Number(e.getResponseHeader("Content-length")),o=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,a=(r=e.getResponseHeader("Content-Encoding"))&&"gzip"===r,i=1048576;o||(i=n);var s=this;s.setDataGetter((e=>{var r=e*i,o=(e+1)*i-1;if(o=Math.min(o,n-1),void 0===s.chunks[e]&&(s.chunks[e]=((e,r)=>{if(e>r)throw new Error("invalid range ("+e+", "+r+") or no bytes requested!");if(r>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",t,!1),n!==i&&o.setRequestHeader("Range","bytes="+e+"-"+r),o.responseType="arraybuffer",o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+t+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):se(o.responseText||"",!0)})(r,o)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!a&&n||(i=n=1,n=this.getter(0).length,i=n,v("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=i,this.lengthKnown=!0}get length(){return this.lengthKnown||this.cacheLength(),this._length}get chunkSize(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}if("undefined"!=typeof XMLHttpRequest){if(!u)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var i={isDevice:!1,contents:new a}}else i={isDevice:!1,url:t};var s=ye.createFile(e,r,i,n,o);i.contents?s.contents=i.contents:i.url&&(s.contents=null,s.url=i.url),Object.defineProperties(s,{usedBytes:{get:function(){return this.contents.length}}});var l={};function d(e,r,t,n,o){var a=e.node.contents;if(o>=a.length)return 0;var i=Math.min(a.length-o,n);if(a.slice)for(var s=0;s<i;s++)r[t+s]=a[o+s];else for(s=0;s<i;s++)r[t+s]=a.get(o+s);return i}return Object.keys(s.stream_ops).forEach((e=>{var r=s.stream_ops[e];l[e]=(...e)=>(ye.forceLoadFile(s),r(...e))})),l.read=(e,r,t,n,o)=>(ye.forceLoadFile(s),d(e,r,t,n,o)),l.mmap=(e,r,t,n,o)=>{ye.forceLoadFile(s);var a=de(r);if(!a)throw new ye.ErrnoError(48);return d(e,E,a,r,t),{ptr:a,allocated:!0}},s.stream_ops=l,s}},ge=(e,r)=>e?ne(b,e,r):"",we={DEFAULT_POLLMASK:5,calculateAt(e,r,t){if(Q.isAbs(r))return r;var n;-100===e?n=ye.cwd():n=we.getStreamFromFD(e).path;if(0==r.length){if(!t)throw new ye.ErrnoError(44);return n}return n+"/"+r},doStat(e,r,t){var n=e(r);$[t>>2]=n.dev,$[t+4>>2]=n.mode,C[t+8>>2]=n.nlink,$[t+12>>2]=n.uid,$[t+16>>2]=n.gid,$[t+20>>2]=n.rdev,B=[n.size>>>0,(I=n.size,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[t+24>>2]=B[0],$[t+28>>2]=B[1],$[t+32>>2]=4096,$[t+36>>2]=n.blocks;var o=n.atime.getTime(),a=n.mtime.getTime(),i=n.ctime.getTime();return B=[Math.floor(o/1e3)>>>0,(I=Math.floor(o/1e3),+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[t+40>>2]=B[0],$[t+44>>2]=B[1],C[t+48>>2]=o%1e3*1e3*1e3,B=[Math.floor(a/1e3)>>>0,(I=Math.floor(a/1e3),+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[t+56>>2]=B[0],$[t+60>>2]=B[1],C[t+64>>2]=a%1e3*1e3*1e3,B=[Math.floor(i/1e3)>>>0,(I=Math.floor(i/1e3),+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[t+72>>2]=B[0],$[t+76>>2]=B[1],C[t+80>>2]=i%1e3*1e3*1e3,B=[n.ino>>>0,(I=n.ino,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[t+88>>2]=B[0],$[t+92>>2]=B[1],0},doMsync(e,r,t,n,o){if(!ye.isFile(r.node.mode))throw new ye.ErrnoError(43);if(2&n)return 0;var a=b.slice(e,e+t);ye.msync(r,a,o,t,n)},getStreamFromFD:e=>ye.getStreamChecked(e),varargs:void 0,getStr:e=>ge(e)};var Ee=()=>{var e=$[+we.varargs>>2];return we.varargs+=4,e},be=Ee;var _e=(e,r)=>r+2097152>>>0<4194305-!!e?(e>>>0)+4294967296*r:NaN;var ke=(e,r,t)=>ie(e,b,r,t);var $e,Ce,Pe,Te,De=(e,r)=>Object.defineProperty(r,"name",{value:e}),Fe=[],Se=[],Me=e=>{throw new $e(e)},Ae=()=>Se.length/2-5-Fe.length,je=e=>(e||Me("Cannot use deleted val. handle = "+e),Se[e]),Oe=e=>{switch(e){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:{const r=Fe.pop()||Se.length;return Se[r]=e,Se[r+1]=1,r}}},Re=(e,r)=>{var t=De(r,(function(e){this.name=r,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},t},Ne=e=>{for(var r="",t=e;b[t];)r+=Pe[b[t++]];return r},xe={},ze=(e,r)=>{for(void 0===r&&Me("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r},Ue={},We=e=>{var r=Tt(e),t=Ne(r);return Ft(r),t},Le=(e,r)=>{var t=Ue[e];return void 0===t&&Me(`${r} has unknown type ${We(e)}`),t},Ie=e=>{},Be=!1,He=e=>{e.count.value-=1,0===e.count.value&&(e=>{e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)})(e)},Ve=(e,r,t)=>{if(r===t)return e;if(void 0===t.baseClass)return null;var n=Ve(e,r,t.baseClass);return null===n?null:t.downcast(n)},Ye={},qe=e=>{throw new Te(e)},Ge=(e,r)=>(r.ptrType&&r.ptr||qe("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!==!!r.smartPtr&&qe("Both smartPtrType and smartPtr must be specified"),r.count={value:1},Je(Object.create(e,{$$:{value:r,writable:!0}})));function Xe(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;var t=((e,r)=>(r=ze(e,r),xe[r]))(this.registeredClass,r);if(void 0!==t){if(0===t.$$.count.value)return t.$$.ptr=r,t.$$.smartPtr=e,t.clone();var n=t.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?Ge(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):Ge(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var a,i=this.registeredClass.getActualType(r),s=Ye[i];if(!s)return o.call(this);a=this.isConst?s.constPointerType:s.pointerType;var l=Ve(r,this.registeredClass,a.registeredClass);return null===l?o.call(this):this.isSmartPointer?Ge(a.registeredClass.instancePrototype,{ptrType:a,ptr:l,smartPtrType:this,smartPtr:e}):Ge(a.registeredClass.instancePrototype,{ptrType:a,ptr:l})}var Je=e=>"undefined"==typeof FinalizationRegistry?(Je=e=>e,e):(Be=new FinalizationRegistry((e=>{He(e.$$)})),Je=e=>{var r=e.$$;if(!!r.smartPtr){var t={$$:r};Be.register(e,t,e)}return e},Ie=e=>Be.unregister(e),Je(e)),Ke={},Ze=e=>{for(;e.length;){var r=e.pop();e.pop()(r)}};function Qe(e){return this.fromWireType(C[e>>2])}var er={},rr={},tr=(e,r,t)=>{function n(r){var n=t(r);n.length!==e.length&&qe("Mismatched type converter count");for(var o=0;o<e.length;++o)nr(e[o],n[o])}e.forEach((e=>rr[e]=r));var o=new Array(r.length),a=[],i=0;r.forEach(((e,r)=>{Ue.hasOwnProperty(e)?o[r]=Ue[e]:(a.push(e),er.hasOwnProperty(e)||(er[e]=[]),er[e].push((()=>{o[r]=Ue[e],++i===a.length&&n(o)})))})),0===a.length&&n(o)};function nr(e,r,t={}){return function(e,r,t={}){var n=r.name;if(e||Me(`type "${n}" must have a positive integer typeid pointer`),Ue.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;Me(`Cannot register type '${n}' twice`)}if(Ue[e]=r,delete rr[e],er.hasOwnProperty(e)){var o=er[e];delete er[e],o.forEach((e=>e()))}}(e,r,t)}var or=8,ar=e=>{Me(e.$$.ptrType.registeredClass.name+" instance already deleted")},ir=[];function sr(){}var lr=(e,r,t)=>{if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(...n){return e[r].overloadTable.hasOwnProperty(n.length)||Me(`Function '${t}' called with an invalid number of arguments (${n.length}) - expects one of (${e[r].overloadTable})!`),e[r].overloadTable[n.length].apply(this,n)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}},ur=(e,r,t)=>{i.hasOwnProperty(e)?((void 0===t||void 0!==i[e].overloadTable&&void 0!==i[e].overloadTable[t])&&Me(`Cannot register public name '${e}' twice`),lr(i,e,e),i[e].overloadTable.hasOwnProperty(t)&&Me(`Cannot register multiple overloads of a function with the same number of arguments (${t})!`),i[e].overloadTable[t]=r):(i[e]=r,i[e].argCount=t)};function dr(e,r,t,n,o,a,i,s){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=a,this.upcast=i,this.downcast=s,this.pureVirtualFunctions=[]}var cr=(e,r,t)=>{for(;r!==t;)r.upcast||Me(`Expected null or instance of ${t.name}, got an instance of ${r.name}`),e=r.upcast(e),r=r.baseClass;return e};function fr(e,r){if(null===r)return this.isReference&&Me(`null is not a valid ${this.name}`),0;r.$$||Me(`Cannot pass "${Mr(r)}" as a ${this.name}`),r.$$.ptr||Me(`Cannot pass deleted object as a pointer of type ${this.name}`);var t=r.$$.ptrType.registeredClass;return cr(r.$$.ptr,t,this.registeredClass)}function hr(e,r){var t;if(null===r)return this.isReference&&Me(`null is not a valid ${this.name}`),this.isSmartPointer?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r&&r.$$||Me(`Cannot pass "${Mr(r)}" as a ${this.name}`),r.$$.ptr||Me(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&r.$$.ptrType.isConst&&Me(`Cannot convert argument of type ${r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name} to parameter type ${this.name}`);var n=r.$$.ptrType.registeredClass;if(t=cr(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&Me("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:Me(`Cannot convert argument of type ${r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var o=r.clone();t=this.rawShare(t,Oe((()=>o.delete()))),null!==e&&e.push(this.rawDestructor,t)}break;default:Me("Unsupporting sharing policy")}return t}function pr(e,r){if(null===r)return this.isReference&&Me(`null is not a valid ${this.name}`),0;r.$$||Me(`Cannot pass "${Mr(r)}" as a ${this.name}`),r.$$.ptr||Me(`Cannot pass deleted object as a pointer of type ${this.name}`),r.$$.ptrType.isConst&&Me(`Cannot convert argument of type ${r.$$.ptrType.name} to parameter type ${this.name}`);var t=r.$$.ptrType.registeredClass;return cr(r.$$.ptr,t,this.registeredClass)}function mr(e,r,t,n,o,a,i,s,l,u,d){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=a,this.sharingPolicy=i,this.rawGetPointee=s,this.rawConstructor=l,this.rawShare=u,this.rawDestructor=d,o||void 0!==r.baseClass?this.toWireType=hr:n?(this.toWireType=fr,this.destructorFunction=null):(this.toWireType=pr,this.destructorFunction=null)}var vr,yr,gr=(e,r,t)=>{i.hasOwnProperty(e)||qe("Replacing nonexistent public symbol"),void 0!==i[e].overloadTable&&void 0!==t?i[e].overloadTable[t]=r:(i[e]=r,i[e].argCount=t)},wr=e=>vr.get(e),Er=(e,r,t=[])=>e.includes("j")?((e,r,t)=>(e=e.replace(/p/g,"i"),(0,i["dynCall_"+e])(r,...t)))(e,r,t):wr(r)(...t),br=(e,r)=>{var t,n,o=(e=Ne(e)).includes("j")?(t=e,n=r,(...e)=>Er(t,n,e)):wr(r);return"function"!=typeof o&&Me(`unknown function pointer with signature ${e}: ${r}`),o},_r=(e,r)=>{var t=[],n={};throw r.forEach((function e(r){n[r]||Ue[r]||(rr[r]?rr[r].forEach(e):(t.push(r),n[r]=!0))})),new yr(`${e}: `+t.map(We).join([", "]))};function kr(e){for(var r=1;r<e.length;++r)if(null!==e[r]&&void 0===e[r].destructorFunction)return!0;return!1}function $r(e,r){if(!(e instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof e} which is not a function`);var t=De(e.name||"unknownFunctionName",(function(){}));t.prototype=e.prototype;var n=new t,o=e.apply(n,r);return o instanceof Object?o:n}function Cr(e,r,t,n,o,a){var i=r.length;i<2&&Me("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==r[1]&&null!==t,l=kr(r),u="void"!==r[0].name,d=[e,Me,n,o,Ze,r[0],r[1]],c=0;c<i-2;++c)d.push(r[c+2]);if(!l)for(c=s?1:2;c<r.length;++c)null!==r[c].destructorFunction&&d.push(r[c].destructorFunction);let[f,h]=function(e,r,t,n){var o=kr(e),a=e.length-2,i=[],s=["fn"];r&&s.push("thisWired");for(var l=0;l<a;++l)i.push(`arg${l}`),s.push(`arg${l}Wired`);i=i.join(","),s=s.join(",");var u=`return function (${i}) {\n`;o&&(u+="var destructors = [];\n");var d=o?"destructors":"null",c=["humanName","throwBindingError","invoker","fn","runDestructors","retType","classParam"];for(r&&(u+=`var thisWired = classParam['toWireType'](${d}, this);\n`),l=0;l<a;++l)u+=`var arg${l}Wired = argType${l}['toWireType'](${d}, arg${l});\n`,c.push(`argType${l}`);if(u+=(t||n?"var rv = ":"")+`invoker(${s});\n`,o)u+="runDestructors(destructors);\n";else for(l=r?1:2;l<e.length;++l){var f=1===l?"thisWired":"arg"+(l-2)+"Wired";null!==e[l].destructorFunction&&(u+=`${f}_dtor(${f});\n`,c.push(`${f}_dtor`))}return t&&(u+="var ret = retType['fromWireType'](rv);\nreturn ret;\n"),[c,u+="}\n"]}(r,s,u,a);f.push(h);var p=$r(Function,f)(...d);return De(e,p)}var Pr=(e,r)=>{for(var t=[],n=0;n<e;n++)t.push(C[r+4*n>>2]);return t},Tr=e=>{const r=(e=e.trim()).indexOf("(");return-1!==r?e.substr(0,r):e},Dr=(e,r,t)=>(e instanceof Object||Me(`${t} with invalid "this": ${e}`),e instanceof r.registeredClass.constructor||Me(`${t} incompatible with "this" of type ${e.constructor.name}`),e.$$.ptr||Me(`cannot call emscripten binding method ${t} on deleted object`),cr(e.$$.ptr,e.$$.ptrType.registeredClass,r.registeredClass)),Fr=e=>{e>9&&0==--Se[e+1]&&(Se[e]=void 0,Fe.push(e))},Sr={name:"emscripten::val",fromWireType:e=>{var r=je(e);return Fr(e),r},toWireType:(e,r)=>Oe(r),argPackAdvance:or,readValueFromPointer:Qe,destructorFunction:null},Mr=e=>{if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e},Ar=(e,r)=>{switch(r){case 4:return function(e){return this.fromWireType(P[e>>2])};case 8:return function(e){return this.fromWireType(T[e>>3])};default:throw new TypeError(`invalid float width (${r}): ${e}`)}},jr=(e,r,t)=>{switch(r){case 1:return t?e=>E[e]:e=>b[e];case 2:return t?e=>_[e>>1]:e=>k[e>>1];case 4:return t?e=>$[e>>2]:e=>C[e>>2];default:throw new TypeError(`invalid integer width (${r}): ${e}`)}},Or=Object.assign({optional:!0},Sr),Rr="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,Nr=(e,r)=>{for(var t=e,n=t>>1,o=n+r/2;!(n>=o)&&k[n];)++n;if((t=n<<1)-e>32&&Rr)return Rr.decode(b.subarray(e,t));for(var a="",i=0;!(i>=r/2);++i){var s=_[e+2*i>>1];if(0==s)break;a+=String.fromCharCode(s)}return a},xr=(e,r,t)=>{if(t??=2147483647,t<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,a=0;a<o;++a){var i=e.charCodeAt(a);_[r>>1]=i,r+=2}return _[r>>1]=0,r-n},zr=e=>2*e.length,Ur=(e,r)=>{for(var t=0,n="";!(t>=r/4);){var o=$[e+4*t>>2];if(0==o)break;if(++t,o>=65536){var a=o-65536;n+=String.fromCharCode(55296|a>>10,56320|1023&a)}else n+=String.fromCharCode(o)}return n},Wr=(e,r,t)=>{if(t??=2147483647,t<4)return 0;for(var n=r,o=n+t-4,a=0;a<e.length;++a){var i=e.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&e.charCodeAt(++a);if($[r>>2]=i,(r+=4)+4>o)break}return $[r>>2]=0,r-n},Lr=e=>{for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r},Ir=0,Br=(e,r,t)=>{var n=[],o=e.toWireType(n,t);return n.length&&(C[r>>2]=Oe(n)),o},Hr=[],Vr={},Yr=e=>{var r=Vr[e];return void 0===r?Ne(e):r},qr=()=>"object"==typeof globalThis?globalThis:Function("return this")();Reflect.construct;var Gr=e=>e%4==0&&(e%100!=0||e%400==0),Xr=[0,31,60,91,121,152,182,213,244,274,305,335],Jr=[0,31,59,90,120,151,181,212,243,273,304,334],Kr=e=>(Gr(e.getFullYear())?Xr:Jr)[e.getMonth()]+e.getDate()-1;var Zr=e=>jt(e);var Qr={},et=e=>{if(e instanceof J||"unwind"==e)return w;h(1,e)},rt=()=>Z||Ir>0,tt=e=>{w=e,rt()||(i.onExit?.(e),F=!0),h(e,new J(e))},nt=(e,r)=>{w=e,tt(e)},ot=e=>{if(!F)try{e(),(()=>{if(!rt())try{nt(w)}catch(e){et(e)}})()}catch(e){et(e)}},at=()=>performance.now(),it=()=>Date.now();var st=[],lt=(e,r,t)=>{var n=((e,r)=>{var t;for(st.length=0;t=b[e++];){var n=105!=t;r+=(n&=112!=t)&&r%8?4:0,st.push(112==t?C[r>>2]:105==t?$[r>>2]:T[r>>3]),r+=n?8:4}return st})(r,t);return X[e](...n)},ut=e=>{var r=(e-g.buffer.byteLength+65535)/65536|0;try{return g.grow(r),M(),1}catch(e){}},dt=e=>(W("Cannot use convertFrameToPC (needed by __builtin_return_address) without -sUSE_OFFSET_CONVERTER"),0),ct={},ft=e=>{e.forEach((e=>{var r=dt();r&&(ct[r]=e)}))},ht=()=>(new Error).stack.toString(),pt={},mt=()=>{if(!mt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:f||"./this.program"};for(var r in pt)void 0===pt[r]?delete e[r]:e[r]=pt[r];var t=[];for(var r in e)t.push(`${r}=${e[r]}`);mt.strings=t}return mt.strings};var vt=(e,r)=>{for(var t=0,n=0;n<=r;t+=e[n++]);return t},yt=[31,29,31,30,31,30,31,31,30,31,30,31],gt=[31,28,31,30,31,30,31,31,30,31,30,31],wt=(e,r)=>{for(var t=new Date(e.getTime());r>0;){var n=Gr(t.getFullYear()),o=t.getMonth(),a=(n?yt:gt)[o];if(!(r>a-t.getDate()))return t.setDate(t.getDate()+r),t;r-=a-t.getDate()+1,t.setDate(1),o<11?t.setMonth(o+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return t},Et=e=>parseInt(e),bt=ye.createPath,_t=ye.createLazyFile,kt=ye.createDevice;ye.createPreloadedFile=pe,ye.staticInit(),i.FS_createPath=ye.createPath,i.FS_createDataFile=ye.createDataFile,i.FS_createPreloadedFile=ye.createPreloadedFile,i.FS_unlink=ye.unlink,i.FS_createLazyFile=ye.createLazyFile,i.FS_createDevice=ye.createDevice,ce.doesNotExistError=new ye.ErrnoError(44),ce.doesNotExistError.stack="<generic error, no stack>",$e=i.BindingError=class extends Error{constructor(e){super(e),this.name="BindingError"}},Se.push(0,1,void 0,1,null,1,!0,1,!1,1),i.count_emval_handles=Ae,Ce=i.PureVirtualError=Re(Error,"PureVirtualError"),(()=>{for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);Pe=e})(),Te=i.InternalError=class extends Error{constructor(e){super(e),this.name="InternalError"}},Object.assign(sr.prototype,{isAliasOf(e){if(!(this instanceof sr))return!1;if(!(e instanceof sr))return!1;var r=this.$$.ptrType.registeredClass,t=this.$$.ptr;e.$$=e.$$;for(var n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o},clone(){if(this.$$.ptr||ar(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,r=Je(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return r.$$.count.value+=1,r.$$.deleteScheduled=!1,r},delete(){this.$$.ptr||ar(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Me("Object already scheduled for deletion"),Ie(this),He(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||ar(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Me("Object already scheduled for deletion"),ir.push(this),ir.length,this.$$.deleteScheduled=!0,this}}),Object.assign(mr.prototype,{getPointee(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e},destructor(e){this.rawDestructor?.(e)},argPackAdvance:or,readValueFromPointer:Qe,fromWireType:Xe}),yr=i.UnboundTypeError=Re(Error,"UnboundTypeError");var $t,Ct={qa:function(){return"undefined"!=typeof wasmOffsetConverter},ja:function(e,r){try{return e=we.getStr(e),ye.chmod(e,r),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},la:function(e,r,t,n){try{if(r=we.getStr(r),r=we.calculateAt(e,r),-8&t)return-28;var o=ye.lookupPath(r,{follow:!0}).node;if(!o)return-44;var a="";return 4&t&&(a+="r"),2&t&&(a+="w"),1&t&&(a+="x"),a&&ye.nodePermissions(o,a)?-2:0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},ka:function(e,r){try{return ye.fchmod(e,r),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},z:function(e,r,t){we.varargs=t;try{var n=we.getStreamFromFD(e);switch(r){case 0:if((o=Ee())<0)return-28;for(;ye.streams[o];)o++;return ye.dupStream(n,o).fd;case 1:case 2:case 13:case 14:return 0;case 3:return n.flags;case 4:var o=Ee();return n.flags|=o,0;case 12:o=be();return _[o+0>>1]=2,0}return-28}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},ga:function(e,r){try{var t=we.getStreamFromFD(e);return we.doStat(ye.stat,t.path,r)}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},P:function(e,r,t){var n=_e(r,t);try{return isNaN(n)?61:(ye.ftruncate(e,n),0)}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},Z:function(e,r,t){try{var n=we.getStreamFromFD(e);n.getdents||=ye.readdir(n.path);for(var o=280,a=0,i=ye.llseek(n,0,1),s=Math.floor(i/o),l=Math.min(n.getdents.length,s+Math.floor(t/o)),u=s;u<l;u++){var d,c,f=n.getdents[u];if("."===f)d=n.node.id,c=4;else if(".."===f){d=ye.lookupPath(n.path,{parent:!0}).node.id,c=4}else{var h;try{h=ye.lookupNode(n.node,f)}catch(e){if(28===e?.errno)continue;throw e}d=h.id,c=ye.isChrdev(h.mode)?2:ye.isDir(h.mode)?4:ye.isLink(h.mode)?10:8}B=[d>>>0,(I=d,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[r+a>>2]=B[0],$[r+a+4>>2]=B[1],B=[(u+1)*o>>>0,(I=(u+1)*o,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[r+a+8>>2]=B[0],$[r+a+12>>2]=B[1],_[r+a+16>>1]=280,E[r+a+18]=c,ke(f,r+a+19,256),a+=o}return ye.llseek(n,u*o,0),a}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},ia:function(e,r,t){we.varargs=t;try{var n=we.getStreamFromFD(e);switch(r){case 21509:case 21510:case 21511:case 21512:case 21524:case 21515:return n.tty?0:-59;case 21505:if(!n.tty)return-59;if(n.tty.ops.ioctl_tcgets){var o=n.tty.ops.ioctl_tcgets(n),a=be();$[a>>2]=o.c_iflag||0,$[a+4>>2]=o.c_oflag||0,$[a+8>>2]=o.c_cflag||0,$[a+12>>2]=o.c_lflag||0;for(var i=0;i<32;i++)E[a+i+17]=o.c_cc[i]||0;return 0}return 0;case 21506:case 21507:case 21508:if(!n.tty)return-59;if(n.tty.ops.ioctl_tcsets){a=be();var s=$[a>>2],l=$[a+4>>2],u=$[a+8>>2],d=$[a+12>>2],c=[];for(i=0;i<32;i++)c.push(E[a+i+17]);return n.tty.ops.ioctl_tcsets(n.tty,r,{c_iflag:s,c_oflag:l,c_cflag:u,c_lflag:d,c_cc:c})}return 0;case 21519:if(!n.tty)return-59;a=be();return $[a>>2]=0,0;case 21520:return n.tty?-28:-59;case 21531:a=be();return ye.ioctl(n,r,a);case 21523:if(!n.tty)return-59;if(n.tty.ops.ioctl_tiocgwinsz){var f=n.tty.ops.ioctl_tiocgwinsz(n.tty);a=be();_[a>>1]=f[0],_[a+2>>1]=f[1]}return 0;default:return-28}}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},ea:function(e,r){try{return e=we.getStr(e),we.doStat(ye.lstat,e,r)}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},_:function(e,r,t){try{return r=we.getStr(r),r=we.calculateAt(e,r),ye.mkdir(r,t,0),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},da:function(e,r,t,n){try{r=we.getStr(r);var o=256&n,a=4096&n;return n&=-6401,r=we.calculateAt(e,r,a),we.doStat(o?ye.lstat:ye.stat,r,t)}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},s:function(e,r,t,n){we.varargs=n;try{r=we.getStr(r),r=we.calculateAt(e,r);var o=n?Ee():0;return ye.open(r,t,o).fd}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},Y:function(e,r,t,n){try{return r=we.getStr(r),n=we.getStr(n),r=we.calculateAt(e,r),n=we.calculateAt(t,n),ye.rename(r,n),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},v:function(e){try{return e=we.getStr(e),ye.rmdir(e),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},fa:function(e,r){try{return e=we.getStr(e),we.doStat(ye.stat,e,r)}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},w:function(e,r,t){try{return r=we.getStr(r),r=we.calculateAt(e,r),0===t?ye.unlink(r):512===t?ye.rmdir(r):W("Invalid flags passed to unlinkat"),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},ma:()=>W(""),wa:(e,r,t)=>{e=Ne(e),r=Le(r,"wrapper"),t=je(t);var n=r.registeredClass,o=n.instancePrototype,a=n.baseClass.instancePrototype,i=n.baseClass.constructor,s=De(e,(function(...e){n.baseClass.pureVirtualFunctions.forEach(function(e){if(this[e]===a[e])throw new Ce(`Pure virtual function ${e} must be implemented in JavaScript`)}.bind(this)),Object.defineProperty(this,"__parent",{value:o}),this.__construct(...e)}));return o.__construct=function(...e){this===o&&Me("Pass correct 'this' to __construct");var r=i.implement(this,...e);Ie(r);var t,a,s,l=r.$$;r.notifyOnDestruction(),l.preservePointerOnDelete=!0,Object.defineProperties(this,{$$:{value:l}}),Je(this),t=n,a=l.ptr,s=this,a=ze(t,a),xe.hasOwnProperty(a)?Me(`Tried to register registered instance: ${a}`):xe[a]=s},o.__destruct=function(){var e,r;this===o&&Me("Pass correct 'this' to __destruct"),Ie(this),e=n,r=this.$$.ptr,r=ze(e,r),xe.hasOwnProperty(r)?delete xe[r]:Me(`Tried to unregister unregistered instance: ${r}`)},s.prototype=Object.create(o),Object.assign(s.prototype,t),Oe(s)},C:e=>{var r=Ke[e];delete Ke[e];var t=r.rawConstructor,n=r.rawDestructor,o=r.fields,a=o.map((e=>e.getterReturnType)).concat(o.map((e=>e.setterArgumentType)));tr([e],a,(e=>{var a={};return o.forEach(((r,t)=>{var n=r.fieldName,i=e[t],s=r.getter,l=r.getterContext,u=e[t+o.length],d=r.setter,c=r.setterContext;a[n]={read:e=>i.fromWireType(s(l,e)),write:(e,r)=>{var t=[];d(c,e,u.toWireType(t,r)),Ze(t)}}})),[{name:r.name,fromWireType:e=>{var r={};for(var t in a)r[t]=a[t].read(e);return n(e),r},toWireType:(e,r)=>{for(var o in a)if(!(o in r))throw new TypeError(`Missing field: "${o}"`);var i=t();for(o in a)a[o].write(i,r[o]);return null!==e&&e.push(n,i),i},argPackAdvance:or,readValueFromPointer:Qe,destructorFunction:n}]}))},R:(e,r,t,n,o)=>{},Aa:(e,r,t,n)=>{nr(e,{name:r=Ne(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?t:n},argPackAdvance:or,readValueFromPointer:function(e){return this.fromWireType(b[e])},destructorFunction:null})},i:(e,r,t,n,o,a,i,s,l,u,d,c,f)=>{d=Ne(d),a=br(o,a),s&&=br(i,s),u&&=br(l,u),f=br(c,f);var h=(e=>{var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=48&&r<=57?`_${e}`:e})(d);ur(h,(function(){_r(`Cannot construct ${d} due to unbound types`,[n])})),tr([e,r,t],n?[n]:[],(r=>{var t,o;r=r[0],o=n?(t=r.registeredClass).instancePrototype:sr.prototype;var i=De(d,(function(...e){if(Object.getPrototypeOf(this)!==l)throw new $e("Use 'new' to construct "+d);if(void 0===c.constructor_body)throw new $e(d+" has no accessible constructor");var r=c.constructor_body[e.length];if(void 0===r)throw new $e(`Tried to invoke ctor of ${d} with invalid number of parameters (${e.length}) - expected (${Object.keys(c.constructor_body).toString()}) parameters instead!`);return r.apply(this,e)})),l=Object.create(o,{constructor:{value:i}});i.prototype=l;var c=new dr(d,i,l,f,t,a,s,u);c.baseClass&&(c.baseClass.__derivedClasses??=[],c.baseClass.__derivedClasses.push(c));var p=new mr(d,c,!0,!1,!1),m=new mr(d+"*",c,!1,!1,!1),v=new mr(d+" const*",c,!1,!0,!1);return Ye[e]={pointerType:m,constPointerType:v},gr(h,i),[p,m,v]}))},B:(e,r,t,n,o,a,i,s,l)=>{var u=Pr(t,n);r=Ne(r),r=Tr(r),a=br(o,a),tr([],[e],(e=>{var n=`${(e=e[0]).name}.${r}`;function o(){_r(`Cannot call ${n} due to unbound types`,u)}r.startsWith("@@")&&(r=Symbol[r.substring(2)]);var l=e.registeredClass.constructor;return void 0===l[r]?(o.argCount=t-1,l[r]=o):(lr(l,r,n),l[r].overloadTable[t-1]=o),tr([],u,(o=>{var u=[o[0],null].concat(o.slice(1)),d=Cr(n,u,null,a,i,s);if(void 0===l[r].overloadTable?(d.argCount=t-1,l[r]=d):l[r].overloadTable[t-1]=d,e.registeredClass.__derivedClasses)for(const t of e.registeredClass.__derivedClasses)t.constructor.hasOwnProperty(r)||(t.constructor[r]=d);return[]})),[]}))},E:(e,r,t,n,o,a)=>{var i=Pr(r,t);o=br(n,o),tr([],[e],(e=>{var t=`constructor ${(e=e[0]).name}`;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[r-1])throw new $e(`Cannot register multiple constructors with identical number of parameters (${r-1}) for class '${e.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return e.registeredClass.constructor_body[r-1]=()=>{_r(`Cannot construct ${e.name} due to unbound types`,i)},tr([],i,(n=>(n.splice(1,0,null),e.registeredClass.constructor_body[r-1]=Cr(t,n,null,o,a),[]))),[]}))},b:(e,r,t,n,o,a,i,s,l,u)=>{var d=Pr(t,n);r=Ne(r),r=Tr(r),a=br(o,a),tr([],[e],(e=>{var n=`${(e=e[0]).name}.${r}`;function o(){_r(`Cannot call ${n} due to unbound types`,d)}r.startsWith("@@")&&(r=Symbol[r.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(r);var u=e.registeredClass.instancePrototype,c=u[r];return void 0===c||void 0===c.overloadTable&&c.className!==e.name&&c.argCount===t-2?(o.argCount=t-2,o.className=e.name,u[r]=o):(lr(u,r,n),u[r].overloadTable[t-2]=o),tr([],d,(o=>{var s=Cr(n,o,e,a,i,l);return void 0===u[r].overloadTable?(s.argCount=t-2,u[r]=s):u[r].overloadTable[t-2]=s,[]})),[]}))},q:(e,r,t,n,o,a,i,s,l,u)=>{r=Ne(r),o=br(n,o),tr([],[e],(e=>{var n=`${(e=e[0]).name}.${r}`,d={get(){_r(`Cannot access ${n} due to unbound types`,[t,i])},enumerable:!0,configurable:!0};return d.set=l?()=>_r(`Cannot access ${n} due to unbound types`,[t,i]):e=>Me(n+" is a read-only property"),Object.defineProperty(e.registeredClass.instancePrototype,r,d),tr([],l?[t,i]:[t],(t=>{var i=t[0],d={get(){var r=Dr(this,e,n+" getter");return i.fromWireType(o(a,r))},enumerable:!0};if(l){l=br(s,l);var c=t[1];d.set=function(r){var t=Dr(this,e,n+" setter"),o=[];l(u,t,c.toWireType(o,r)),Ze(o)}}return Object.defineProperty(e.registeredClass.instancePrototype,r,d),[]})),[]}))},ya:e=>nr(e,Sr),F:(e,r,t)=>{nr(e,{name:r=Ne(r),fromWireType:e=>e,toWireType:(e,r)=>r,argPackAdvance:or,readValueFromPointer:Ar(r,t),destructorFunction:null})},d:(e,r,t,n,o,a,i,s)=>{var l=Pr(r,t);e=Ne(e),e=Tr(e),o=br(n,o),ur(e,(function(){_r(`Cannot call ${e} due to unbound types`,l)}),r-1),tr([],l,(t=>{var n=[t[0],null].concat(t.slice(1));return gr(e,Cr(e,n,null,o,a,i),r-1),[]}))},h:(e,r,t,n,o)=>{r=Ne(r),-1===o&&(o=4294967295);var a=e=>e;if(0===n){var i=32-8*t;a=e=>e<<i>>>i}var s=r.includes("unsigned");nr(e,{name:r,fromWireType:a,toWireType:s?function(e,r){return this.name,r>>>0}:function(e,r){return this.name,r},argPackAdvance:or,readValueFromPointer:jr(r,t,0!==n),destructorFunction:null})},c:(e,r,t)=>{var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(e){var r=C[e>>2],t=C[e+4>>2];return new n(E.buffer,t,r)}nr(e,{name:t=Ne(t),fromWireType:o,argPackAdvance:or,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},G:(e,r)=>{nr(e,Or)},$:(e,r,t,n,o,a,i,s,l,u,d,c)=>{t=Ne(t),a=br(o,a),s=br(i,s),u=br(l,u),c=br(d,c),tr([e],[r],(e=>(e=e[0],[new mr(t,e.registeredClass,!1,!1,!0,e,n,a,s,u,c)])))},za:(e,r)=>{r=Ne(r);nr(e,{name:r,fromWireType(e){for(var r,t=C[e>>2],n=e+4,o=n,a=0;a<=t;++a){var i=n+a;if(a==t||0==b[i]){var s=ge(o,i-o);void 0===r?r=s:(r+=String.fromCharCode(0),r+=s),o=i+1}}return Ft(e),r},toWireType(e,r){var t;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var n="string"==typeof r;n||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||Me("Cannot pass non-string to std::string"),t=n?ae(r):r.length;var o=Dt(4+t+1),a=o+4;if(C[o>>2]=t,n)ke(r,a,t+1);else if(n)for(var i=0;i<t;++i){var s=r.charCodeAt(i);s>255&&(Ft(a),Me("String has UTF-16 code units that do not fit in 8 bits")),b[a+i]=s}else for(i=0;i<t;++i)b[a+i]=r[i];return null!==e&&e.push(Ft,o),o},argPackAdvance:or,readValueFromPointer:Qe,destructorFunction(e){Ft(e)}})},u:(e,r,t)=>{var n,o,a,i;t=Ne(t),2===r?(n=Nr,o=xr,i=zr,a=e=>k[e>>1]):4===r&&(n=Ur,o=Wr,i=Lr,a=e=>C[e>>2]),nr(e,{name:t,fromWireType:e=>{for(var t,o=C[e>>2],i=e+4,s=0;s<=o;++s){var l=e+4+s*r;if(s==o||0==a(l)){var u=n(i,l-i);void 0===t?t=u:(t+=String.fromCharCode(0),t+=u),i=l+r}}return Ft(e),t},toWireType:(e,n)=>{"string"!=typeof n&&Me(`Cannot pass non-string to C++ string type ${t}`);var a=i(n),s=Dt(4+a+r);return C[s>>2]=a/r,o(n,s+4,a+r),null!==e&&e.push(Ft,s),s},argPackAdvance:or,readValueFromPointer:Qe,destructorFunction(e){Ft(e)}})},xa:(e,r,t,n,o,a)=>{Ke[e]={name:Ne(r),rawConstructor:br(t,n),rawDestructor:br(o,a),fields:[]}},D:(e,r,t,n,o,a,i,s,l,u)=>{Ke[e].fields.push({fieldName:Ne(r),getterReturnType:t,getter:br(n,o),getterContext:a,setterArgumentType:i,setter:br(s,l),setterContext:u})},Ba:(e,r)=>{nr(e,{isVoid:!0,name:r=Ne(r),argPackAdvance:0,fromWireType:()=>{},toWireType:(e,r)=>{}})},oa:e=>{do{var r=C[e>>2],t=C[(e+=4)>>2],n=C[(e+=4)>>2];e+=4;var o=ge(r);ye.createPath("/",Q.dirname(o),!0,!0),ye.createDataFile(o,null,E.subarray(n,n+t),!0,!0,!0)}while(C[e>>2])},ha:(e,r,t)=>b.copyWithin(e,r,r+t),S:()=>{Z=!1,Ir=0},m:(e,r,t)=>(e=je(e),r=Le(r,"emval::as"),Br(r,t,e)),J:(e,r,t,n)=>(e=Hr[e])(null,r=je(r),t,n),g:(e,r,t,n,o)=>(e=Hr[e])(r=je(r),r[t=Yr(t)],n,o),a:Fr,p:e=>0===e?Oe(qr()):(e=Yr(e),Oe(qr()[e])),f:(e,r,t)=>{var n=((e,r)=>{for(var t=new Array(e),n=0;n<e;++n)t[n]=Le(C[r+4*n>>2],"parameter "+n);return t})(e,r),o=n.shift();e--;var a="return function (obj, func, destructorsRef, args) {\n",i=0,s=[];0===t&&s.push("obj");for(var l=["retType"],u=[o],d=0;d<e;++d)s.push("arg"+d),l.push("argType"+d),u.push(n[d]),a+=`  var arg${d} = argType${d}.readValueFromPointer(args${i?"+"+i:""});\n`,i+=n[d].argPackAdvance;a+=`  var rv = ${1===t?"new func":"func.call"}(${s.join(", ")});\n`,o.isVoid||(l.push("emval_returnValue"),u.push(Br),a+="  return emval_returnValue(retType, destructorsRef, rv);\n"),a+="};\n",l.push(a);var c,f,h=$r(Function,l)(...u),p=`methodCaller<(${n.map((e=>e.name)).join(", ")}) => ${o.name}>`;return c=De(p,h),f=Hr.length,Hr.push(c),f},ua:e=>(e=Yr(e),Oe(i[e])),o:(e,r)=>(e=je(e),r=je(r),Oe(e[r])),r:e=>{e>9&&(Se[e+1]+=1)},n:e=>Oe(Yr(e)),e:e=>{var r=je(e);Ze(r),Fr(e)},k:(e,r)=>{var t=(e=Le(e,"_emval_take_value")).readValueFromPointer(r);return Oe(t)},t:e=>(e=je(e),Oe(typeof e)),K:function(e,r,t){var n=_e(e,r),o=new Date(1e3*n);$[t>>2]=o.getUTCSeconds(),$[t+4>>2]=o.getUTCMinutes(),$[t+8>>2]=o.getUTCHours(),$[t+12>>2]=o.getUTCDate(),$[t+16>>2]=o.getUTCMonth(),$[t+20>>2]=o.getUTCFullYear()-1900,$[t+24>>2]=o.getUTCDay();var a=Date.UTC(o.getUTCFullYear(),0,1,0,0,0,0),i=(o.getTime()-a)/864e5|0;$[t+28>>2]=i},L:function(e,r,t){var n=_e(e,r),o=new Date(1e3*n);$[t>>2]=o.getSeconds(),$[t+4>>2]=o.getMinutes(),$[t+8>>2]=o.getHours(),$[t+12>>2]=o.getDate(),$[t+16>>2]=o.getMonth(),$[t+20>>2]=o.getFullYear()-1900,$[t+24>>2]=o.getDay();var a=0|Kr(o);$[t+28>>2]=a,$[t+36>>2]=-60*o.getTimezoneOffset();var i=new Date(o.getFullYear(),0,1),s=new Date(o.getFullYear(),6,1).getTimezoneOffset(),l=i.getTimezoneOffset(),u=0|(s!=l&&o.getTimezoneOffset()==Math.min(l,s));$[t+32>>2]=u},M:function(e){var r=(()=>{var r=new Date($[e+20>>2]+1900,$[e+16>>2],$[e+12>>2],$[e+8>>2],$[e+4>>2],$[e>>2],0),t=$[e+32>>2],n=r.getTimezoneOffset(),o=new Date(r.getFullYear(),0,1),a=new Date(r.getFullYear(),6,1).getTimezoneOffset(),i=o.getTimezoneOffset(),s=Math.min(i,a);if(t<0)$[e+32>>2]=Number(a!=i&&s==n);else if(t>0!=(s==n)){var l=Math.max(i,a),u=t>0?s:l;r.setTime(r.getTime()+6e4*(u-n))}$[e+24>>2]=r.getDay();var d=0|Kr(r);$[e+28>>2]=d,$[e>>2]=r.getSeconds(),$[e+4>>2]=r.getMinutes(),$[e+8>>2]=r.getHours(),$[e+12>>2]=r.getDate(),$[e+16>>2]=r.getMonth(),$[e+20>>2]=r.getYear();var c=r.getTime();return isNaN(c)?-1:c/1e3})();return Zr((I=r,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)),r>>>0},H:function(e,r,t,n,o,a,i,s){var l=_e(o,a);try{if(isNaN(l))return 61;var u=we.getStreamFromFD(n),d=ye.mmap(u,e,l,r,t),c=d.ptr;return $[i>>2]=d.allocated,C[s>>2]=c,0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},I:function(e,r,t,n,o,a,i){var s=_e(a,i);try{var l=we.getStreamFromFD(o);2&t&&we.doMsync(e,l,r,n,s)}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return-e.errno}},T:(e,r)=>{if(Qr[e]&&(clearTimeout(Qr[e].id),delete Qr[e]),!r)return 0;var t=setTimeout((()=>{delete Qr[e],ot((()=>Mt(e,at())))}),r);return Qr[e]={id:t,timeout_ms:r},0},N:function(e){var r=(()=>{var r=Date.UTC($[e+20>>2]+1900,$[e+16>>2],$[e+12>>2],$[e+8>>2],$[e+4>>2],$[e>>2],0),t=new Date(r);$[e+24>>2]=t.getUTCDay();var n=Date.UTC(t.getUTCFullYear(),0,1,0,0,0,0),o=(t.getTime()-n)/864e5|0;return $[e+28>>2]=o,t.getTime()/1e3})();return Zr((I=r,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)),r>>>0},Q:function(e,r,t,n){var o,a;if(_e(r,t),!((o=e)>=0&&o<=3))return 28;a=0===e?it():at();var i=Math.round(1e3*a*1e3);return B=[i>>>0,(I=i,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[n>>2]=B[0],$[n+4>>2]=B[1],0},ta:(e,r,t)=>lt(e,r,t),A:it,X:()=>2147483648,j:at,pa:e=>(W("Cannot use emscripten_pc_get_function without -sUSE_OFFSET_CONVERTER"),0),V:e=>{var r=b.length,t=2147483648;if((e>>>=0)>t)return!1;for(var n=1;n<=4;n*=2){var o=r*(1+.2/n);o=Math.min(o,e+100663296);var a=Math.min(t,ue(Math.max(e,o),65536));if(ut(a))return!0}return!1},sa:()=>{var e=ht().split("\n");return"Error"==e[0]&&e.shift(),ft(e),ct.last_addr=dt(e[3]),ct.last_stack=e,ct.last_addr},ra:(e,r,t)=>{var n;ct.last_addr==e?n=ct.last_stack:("Error"==(n=ht().split("\n"))[0]&&n.shift(),ft(n));for(var o=3;n[o]&&dt(n[o])!=e;)++o;for(var a=0;a<t&&n[a+o];++a)$[r+4*a>>2]=dt(n[a+o]);return a},aa:(e,r)=>{var t=0;return mt().forEach(((n,o)=>{var a=r+t;C[e+4*o>>2]=a,((e,r)=>{for(var t=0;t<e.length;++t)E[r++]=e.charCodeAt(t);E[r]=0})(n,a),t+=n.length+1})),0},ba:(e,r)=>{var t=mt();C[e>>2]=t.length;var n=0;return t.forEach((e=>n+=e.length+1)),C[r>>2]=n,0},l:function(e){try{var r=we.getStreamFromFD(e);return ye.close(r),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return e.errno}},W:function(e,r){try{var t=we.getStreamFromFD(e),n=t.tty?2:ye.isDir(t.mode)?3:ye.isLink(t.mode)?7:4;return E[r]=n,_[r+2>>1]=0,B=[0,(I=0,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[r+8>>2]=B[0],$[r+12>>2]=B[1],B=[0,(I=0,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[r+16>>2]=B[0],$[r+20>>2]=B[1],0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return e.errno}},y:function(e,r,t,n){try{var o=((e,r,t,n)=>{for(var o=0,a=0;a<t;a++){var i=C[r>>2],s=C[r+4>>2];r+=8;var l=ye.read(e,E,i,s,n);if(l<0)return-1;if(o+=l,l<s)break;void 0!==n&&(n+=l)}return o})(we.getStreamFromFD(e),r,t);return C[n>>2]=o,0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return e.errno}},O:function(e,r,t,n,o){var a=_e(r,t);try{if(isNaN(a))return 61;var i=we.getStreamFromFD(e);return ye.llseek(i,a,n),B=[i.position>>>0,(I=i.position,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],$[o>>2]=B[0],$[o+4>>2]=B[1],i.getdents&&0===a&&0===n&&(i.getdents=null),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return e.errno}},ca:function(e){try{var r=we.getStreamFromFD(e);return r.stream_ops?.fsync?r.stream_ops.fsync(r):0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return e.errno}},x:function(e,r,t,n){try{var o=((e,r,t,n)=>{for(var o=0,a=0;a<t;a++){var i=C[r>>2],s=C[r+4>>2];r+=8;var l=ye.write(e,E,i,s,n);if(l<0)return-1;if(o+=l,l<s)break;void 0!==n&&(n+=l)}return o})(we.getStreamFromFD(e),r,t);return C[n>>2]=o,0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return e.errno}},na:tt,U:function(e,r){try{return ee(b.subarray(e,e+r)),0}catch(e){if(void 0===ye||"ErrnoError"!==e.name)throw e;return e.errno}},va:(e,r,t)=>{for(var n=ge(r),o="\\!@#$^&*()+=-[]/{}|:<>?,.",a=0;a<25;++a)n=n.replace(new RegExp("\\"+o[a],"g"),"\\"+o[a]);var i={A:"%a",B:"%b",c:"%a %b %d %H:%M:%S %Y",D:"%m\\/%d\\/%y",e:"%d",F:"%Y-%m-%d",h:"%b",R:"%H\\:%M",r:"%I\\:%M\\:%S\\s%p",T:"%H\\:%M\\:%S",x:"%m\\/%d\\/(?:%y|%Y)",X:"%H\\:%M\\:%S"},s={a:"(?:Sun(?:day)?)|(?:Mon(?:day)?)|(?:Tue(?:sday)?)|(?:Wed(?:nesday)?)|(?:Thu(?:rsday)?)|(?:Fri(?:day)?)|(?:Sat(?:urday)?)",b:"(?:Jan(?:uary)?)|(?:Feb(?:ruary)?)|(?:Mar(?:ch)?)|(?:Apr(?:il)?)|May|(?:Jun(?:e)?)|(?:Jul(?:y)?)|(?:Aug(?:ust)?)|(?:Sep(?:tember)?)|(?:Oct(?:ober)?)|(?:Nov(?:ember)?)|(?:Dec(?:ember)?)",C:"\\d\\d",d:"0[1-9]|[1-9](?!\\d)|1\\d|2\\d|30|31",H:"\\d(?!\\d)|[0,1]\\d|20|21|22|23",I:"\\d(?!\\d)|0\\d|10|11|12",j:"00[1-9]|0?[1-9](?!\\d)|0?[1-9]\\d(?!\\d)|[1,2]\\d\\d|3[0-6]\\d",m:"0[1-9]|[1-9](?!\\d)|10|11|12",M:"0\\d|\\d(?!\\d)|[1-5]\\d",n:" ",p:"AM|am|PM|pm|A\\.M\\.|a\\.m\\.|P\\.M\\.|p\\.m\\.",S:"0\\d|\\d(?!\\d)|[1-5]\\d|60",U:"0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53",W:"0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53",w:"[0-6]",y:"\\d\\d",Y:"\\d\\d\\d\\d",t:" ",z:"Z|(?:[\\+\\-]\\d\\d:?(?:\\d\\d)?)"},l=[],u=n.replace(/%(.)/g,((e,r)=>i[r]||e)).replace(/%(.)/g,((e,r)=>{let t=s[r];return t?(l.push(r),`(${t})`):r})).replace(/\s+/g,"\\s*"),d=new RegExp("^"+u,"i").exec(ge(e));if(d){var c,f=function(){function e(e,r,t){return"number"!=typeof e||isNaN(e)?r:e>=r?e<=t?e:t:r}return{year:e($[t+20>>2]+1900,1970,9999),month:e($[t+16>>2],0,11),day:e($[t+12>>2],1,31),hour:e($[t+8>>2],0,23),min:e($[t+4>>2],0,59),sec:e($[t>>2],0,59),gmtoff:0}}(),h=e=>{var r=l.indexOf(e);if(r>=0)return d[r+1]};if((c=h("S"))&&(f.sec=Et(c)),(c=h("M"))&&(f.min=Et(c)),c=h("H"))f.hour=Et(c);else if(c=h("I")){var p=Et(c);(c=h("p"))&&(p+="P"===c.toUpperCase()[0]?12:0),f.hour=p}if(c=h("Y"))f.year=Et(c);else if(c=h("y")){var m=Et(c);(c=h("C"))?m+=100*Et(c):m+=m<69?2e3:1900,f.year=m}if((c=h("m"))?f.month=Et(c)-1:(c=h("b"))&&(f.month={JAN:0,FEB:1,MAR:2,APR:3,MAY:4,JUN:5,JUL:6,AUG:7,SEP:8,OCT:9,NOV:10,DEC:11}[c.substring(0,3).toUpperCase()]||0),c=h("d"))f.day=Et(c);else if(c=h("j"))for(var v=Et(c),y=Gr(f.year),g=0;g<12;++g){var w=vt(y?yt:gt,g-1);v<=w+(y?yt:gt)[g]&&(f.day=v-w)}else if(c=h("a")){var E=c.substring(0,3).toUpperCase();if(c=h("U")){var b={SUN:0,MON:1,TUE:2,WED:3,THU:4,FRI:5,SAT:6}[E],_=Et(c);C=0===(k=new Date(f.year,0,1)).getDay()?wt(k,b+7*(_-1)):wt(k,7-k.getDay()+b+7*(_-1)),f.day=C.getDate(),f.month=C.getMonth()}else if(c=h("W")){var k,C;b={MON:0,TUE:1,WED:2,THU:3,FRI:4,SAT:5,SUN:6}[E],_=Et(c);C=1===(k=new Date(f.year,0,1)).getDay()?wt(k,b+7*(_-1)):wt(k,7-k.getDay()+1+b+7*(_-1)),f.day=C.getDate(),f.month=C.getMonth()}}if(c=h("z"))if("z"===c.toLowerCase())f.gmtoff=0;else{var P=c.match(/^((?:\-|\+)\d\d):?(\d\d)?/);f.gmtoff=3600*P[1],P[2]&&(f.gmtoff+=f.gmtoff>0?60*P[2]:60*-P[2])}var T=new Date(f.year,f.month,f.day,f.hour,f.min,f.sec,0);return $[t>>2]=T.getSeconds(),$[t+4>>2]=T.getMinutes(),$[t+8>>2]=T.getHours(),$[t+12>>2]=T.getDate(),$[t+16>>2]=T.getMonth(),$[t+20>>2]=T.getFullYear()-1900,$[t+24>>2]=T.getDay(),$[t+28>>2]=vt(Gr(T.getFullYear())?yt:gt,T.getMonth()-1)+T.getDate()-1,$[t+32>>2]=0,$[t+36>>2]=f.gmtoff,e+se(d[0]).length-1}return 0}};!async function(){function e(e,r){var t;return $t=e.exports,g=$t.Ca,M(),vr=$t.Ha,t=$t.Da,j.unshift(t),U(),$t}z();var r={a:Ct};if(i.instantiateWasm)try{return i.instantiateWasm(r,e)}catch(e){y(`Module.instantiateWasm callback failed with error: ${e}`),n(e)}L??=Y();try{var t=await G(D,L,r);return function(r){e(r.instance)}(t),t}catch(e){return void n(e)}}();var Pt,Tt=e=>(Tt=$t.Ea)(e),Dt=e=>(Dt=$t.Fa)(e),Ft=e=>(Ft=$t.Ga)(e),St=(e,r)=>(St=$t.Ia)(e,r),Mt=(e,r)=>(Mt=$t.Ja)(e,r),At=()=>(At=$t.Ka)(),jt=e=>(jt=$t.La)(e);i.dynCall_vij=(e,r,t,n)=>(i.dynCall_vij=$t.Ma)(e,r,t,n),i.dynCall_jiji=(e,r,t,n,o)=>(i.dynCall_jiji=$t.Na)(e,r,t,n,o),i.dynCall_ji=(e,r)=>(i.dynCall_ji=$t.Oa)(e,r),i.dynCall_viij=(e,r,t,n,o)=>(i.dynCall_viij=$t.Pa)(e,r,t,n,o),i.dynCall_iij=(e,r,t,n)=>(i.dynCall_iij=$t.Qa)(e,r,t,n),i.dynCall_iiji=(e,r,t,n,o)=>(i.dynCall_iiji=$t.Ra)(e,r,t,n,o),i.dynCall_jji=(e,r,t,n)=>(i.dynCall_jji=$t.Sa)(e,r,t,n),i.dynCall_iji=(e,r,t,n)=>(i.dynCall_iji=$t.Ta)(e,r,t,n),i.dynCall_viijj=(e,r,t,n,o,a,s)=>(i.dynCall_viijj=$t.Ua)(e,r,t,n,o,a,s),i.dynCall_iiij=(e,r,t,n,o)=>(i.dynCall_iiij=$t.Va)(e,r,t,n,o),i.dynCall_viiiji=(e,r,t,n,o,a,s)=>(i.dynCall_viiiji=$t.Wa)(e,r,t,n,o,a,s),i.dynCall_viijii=(e,r,t,n,o,a,s)=>(i.dynCall_viijii=$t.Xa)(e,r,t,n,o,a,s),i.dynCall_viji=(e,r,t,n,o)=>(i.dynCall_viji=$t.Ya)(e,r,t,n,o),i.dynCall_iiiijij=(e,r,t,n,o,a,s,l,u)=>(i.dynCall_iiiijij=$t.Za)(e,r,t,n,o,a,s,l,u),i.dynCall_jij=(e,r,t,n)=>(i.dynCall_jij=$t._a)(e,r,t,n),i.dynCall_iiiij=(e,r,t,n,o,a)=>(i.dynCall_iiiij=$t.$a)(e,r,t,n,o,a),i.dynCall_iiiiij=(e,r,t,n,o,a,s)=>(i.dynCall_iiiiij=$t.ab)(e,r,t,n,o,a,s),i.dynCall_iiiiijj=(e,r,t,n,o,a,s,l,u)=>(i.dynCall_iiiiijj=$t.bb)(e,r,t,n,o,a,s,l,u),i.dynCall_iiiiiijj=(e,r,t,n,o,a,s,l,u,d)=>(i.dynCall_iiiiiijj=$t.cb)(e,r,t,n,o,a,s,l,u,d),i.___emscripten_embedded_file_data=168424;function Ot(){function e(){Pt||(Pt=!0,i.calledRun=!0,F||(R=!0,i.noFSInit||ye.initialized||ye.init(),ye.ignorePermissions=!1,le.init(),K(j),t(i),i.onRuntimeInitialized?.(),function(){if(i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)e=i.postRun.shift(),O.unshift(e);var e;K(O)}()))}N>0||(!function(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)e=i.preRun.shift(),A.unshift(e);var e;K(A)}(),N>0||(i.setStatus?(i.setStatus("Running..."),setTimeout((()=>{setTimeout((()=>i.setStatus("")),1),e()}),1)):e()))}if(i.addRunDependency=z,i.removeRunDependency=U,i.FS_createPreloadedFile=pe,i.FS_unlink=e=>ye.unlink(e),i.FS_createPath=bt,i.FS_createDevice=kt,i.FS=ye,i.FS_createDataFile=fe,i.FS_createLazyFile=_t,x=function e(){Pt||Ot(),Pt||(x=e)},i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();return Ot(),s}})();"object"==typeof exports&&"object"==typeof module?(module.exports=PSPDFModuleInit,module.exports.default=PSPDFModuleInit):"function"==typeof define&&define.amd&&define([],(()=>PSPDFModuleInit));