# PDF Text Extraction Backend

This backend service processes PDF files to extract text and bounding box information using the specified pattern matching.

## Setup Instructions

### 1. Install Python Dependencies

```bash
cd python_backend
pip install -r requirements.txt
```

### 2. Run the Backend Server

```bash
python main.py
```

The server will start on `http://localhost:8000`

### 3. API Endpoints

- **GET /** - Health check endpoint
- **POST /upload-pdf** - Upload a PDF file for processing

### 4. API Usage

Upload a PDF file to `/upload-pdf` endpoint:

```bash
curl -X POST "http://localhost:8000/upload-pdf" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_file.pdf"
```

### 5. Response Format

The API returns filtered pairs in the following format:

```json
{
  "success": true,
  "filename": "example.pdf",
  "total_matches": 5,
  "filtered_pairs": [
    {
      "label": "Example Label",
      "code": "01.A.01",
      "bbox": {
        "x0": 100.0,
        "y0": 200.0,
        "x1": 300.0,
        "y1": 220.0
      }
    }
  ]
}
```

### 6. Pattern Matching

The service looks for text elements with exactly 2 lines where:
- First line: Any text (becomes the label)
- Second line: Matches pattern `XX.Y.XX` where X is a digit and Y is a capital letter

### 7. CORS Configuration

The backend is configured to accept requests from:
- http://localhost:5173 (Vite dev server)
- http://localhost:3000 (React dev server)
- http://127.0.0.1:5173

### 8. Development

For development with auto-reload:

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```
