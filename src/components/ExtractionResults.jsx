import { useState } from 'react';

function ExtractionResults({ data, onClose }) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!data) return null;

  return (
    <div className="fixed top-20 left-4 z-50 bg-white border border-gray-300 rounded-lg shadow-lg max-w-md max-h-96 overflow-hidden">
      <div className="bg-green-600 text-white px-4 py-2 flex justify-between items-center">
        <div>
          <div className="font-semibold">Extraction Complete!</div>
          <div className="text-sm text-green-100">
            Found {data.total_matches} matching pairs
          </div>
        </div>
        <button 
          onClick={onClose}
          className="text-white hover:text-gray-200 text-xl"
        >
          ×
        </button>
      </div>
      
      <div className="p-4">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-blue-600 hover:text-blue-800 text-sm mb-2"
        >
          {isExpanded ? 'Hide Details' : 'Show Details'}
        </button>
        
        {isExpanded && (
          <div className="max-h-64 overflow-y-auto">
            <div className="text-sm text-gray-600 mb-2">
              File: {data.filename}
            </div>
            
            {data.filtered_pairs.map((pair, index) => (
              <div key={index} className="border-b border-gray-200 py-2 last:border-b-0">
                <div className="font-medium text-gray-800">{pair.label}</div>
                <div className="text-blue-600 font-mono text-sm">{pair.code}</div>
                <div className="text-xs text-gray-500">
                  BBox: ({pair.bbox.x0.toFixed(1)}, {pair.bbox.y0.toFixed(1)}, {pair.bbox.x1.toFixed(1)}, {pair.bbox.y1.toFixed(1)})
                </div>
              </div>
            ))}
          </div>
        )}
        
        <div className="mt-3 pt-3 border-t border-gray-200">
          <button
            onClick={() => {
              console.log('Extracted Data:', data);
              navigator.clipboard.writeText(JSON.stringify(data, null, 2));
            }}
            className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded mr-2"
          >
            Copy to Clipboard
          </button>
          <button
            onClick={() => {
              const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `${data.filename}_extraction.json`;
              a.click();
              URL.revokeObjectURL(url);
            }}
            className="text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded"
          >
            Download JSON
          </button>
        </div>
      </div>
    </div>
  );
}

export default ExtractionResults;
