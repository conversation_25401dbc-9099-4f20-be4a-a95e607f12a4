import { useEffect, useRef, useState } from 'react';

const TestViewer = ({ pdfUrl, extractedData }) => {
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [instance, setInstance] = useState(null);

  const [notification, setNotification] = useState(null);

  // Point-in-polygon algorithm using ray casting
  const isPointInPolygon = (point, polygon) => {
    const { x, y } = point;
    let inside = false;

    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i].x;
      const yi = polygon[i].y;
      const xj = polygon[j].x;
      const yj = polygon[j].y;

      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }

    return inside;
  };

  // Convert screen/viewport coordinates to PDF coordinates
  const convertToPdfCoordinates = async (screenPoints) => {
    if (!instance) return screenPoints;

    try {
      // Get the current page info and viewport
      const pageInfo = await instance.getPageInfo(0); // Assuming first page
      const viewState = instance.viewState;
      const currentZoom = viewState.zoom;

      console.log('Page info:', pageInfo);
      console.log('Current zoom:', currentZoom);
      console.log('View state:', viewState);

      // Convert each point from screen coordinates to PDF coordinates
      const pdfPoints = screenPoints.map(point => {
        // The annotation points should already be in PDF coordinate space
        // but let's verify and log the conversion
        console.log(`Original point: (${point.x}, ${point.y})`);

        // If the points are already in PDF space, return as-is
        // Otherwise, we'd need to apply inverse transformation
        return {
          x: point.x,
          y: point.y
        };
      });

      console.log('Converted PDF points:', pdfPoints);
      return pdfPoints;
    } catch (error) {
      console.error('Error converting coordinates:', error);
      return screenPoints;
    }
  };

  // Check which labels are inside the polygon and show notification
  const checkLabelsInPolygon = async (polygonPoints) => {
    console.log('Checking labels in polygon with points:', polygonPoints);

    if (!extractedData || !extractedData.filtered_pairs) {
      console.log('No extracted data available');
      return;
    }

    // Convert polygon points to PDF coordinate space
    const pdfPolygonPoints = await convertToPdfCoordinates(polygonPoints);
    console.log('PDF polygon points:', pdfPolygonPoints);

    console.log('Available labels:', extractedData.filtered_pairs.length);
    const labelsInside = [];

    extractedData.filtered_pairs.forEach((pair, index) => {
      // Use the center point of the bounding box for detection
      const centerX = (pair.bbox.x0 + pair.bbox.x1) / 2;
      const centerY = (pair.bbox.y0 + pair.bbox.y1) / 2;

      console.log(`Checking label ${index}: ${pair.label} at PDF coordinates (${centerX}, ${centerY})`);
      console.log(`Label bbox: x0=${pair.bbox.x0}, y0=${pair.bbox.y0}, x1=${pair.bbox.x1}, y1=${pair.bbox.y1}`);

      if (isPointInPolygon({ x: centerX, y: centerY }, pdfPolygonPoints)) {
        console.log(`Label ${pair.label} is inside polygon`);
        labelsInside.push(pair);
      } else {
        console.log(`Label ${pair.label} is outside polygon`);
      }
    });

    console.log('Labels found inside polygon:', labelsInside.length);

    // Also log the polygon bounds for debugging
    const minX = Math.min(...pdfPolygonPoints.map(p => p.x));
    const maxX = Math.max(...pdfPolygonPoints.map(p => p.x));
    const minY = Math.min(...pdfPolygonPoints.map(p => p.y));
    const maxY = Math.max(...pdfPolygonPoints.map(p => p.y));
    console.log(`Polygon bounds: x(${minX} to ${maxX}), y(${minY} to ${maxY})`);

    if (labelsInside.length > 0) {
      const roomNames = labelsInside.map(label => label.label).join(', ');
      setNotification({
        type: 'success',
        message: `Room(s) drawn: ${roomNames}`,
        labels: labelsInside
      });

      // Auto-hide notification after 5 seconds
      setTimeout(() => setNotification(null), 5000);
    } else {
      setNotification({
        type: 'info',
        message: 'No rooms found in the drawn area',
        labels: []
      });

      // Auto-hide notification after 3 seconds
      setTimeout(() => setNotification(null), 3000);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    let cleanup = () => { };

    (async () => {
      console.log('TestViewer mounted');
      console.log('Container:', container);
      console.log('PDF URL:', pdfUrl);

      if (!container || !pdfUrl) {
        console.log('No container or PDF URL available');
        setError('No PDF URL provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const NutrientViewer = (await import('@nutrient-sdk/viewer')).default;
        console.log('NutrientViewer imported:', NutrientViewer);

        // Ensure there's only one NutrientViewer instance
        NutrientViewer.unload(container);

        const loadConfig = {
          container,
          document: pdfUrl,
          baseUrl: `${window.location.protocol}//${window.location.host}/`,
          // Use default toolbar which includes all annotation tools including polygon
          // To remove "FOR EVALUATION PURPOSE ONLY" watermark, add your license key:
          // licenseKey: 'YOUR_LICENSE_KEY_HERE'
        };

        console.log('Load config:', loadConfig);

        const pdfInstance = await NutrientViewer.load(loadConfig);
        console.log('PDF loaded successfully:', pdfInstance);
        setInstance(pdfInstance);
        setIsLoading(false);

        // Show welcome notification if we have extracted data
        if (extractedData && extractedData.filtered_pairs && extractedData.filtered_pairs.length > 0) {
          setNotification({
            type: 'info',
            message: `PDF loaded! Use the Annotate → Polygon tool to draw around room labels.`,
            labels: []
          });
          setTimeout(() => setNotification(null), 6000);
        }

        // Listen for annotation creation events
        const unsubscribeAnnotationCreate = pdfInstance.addEventListener('annotations.create', (annotations) => {
          console.log('annotations.create event fired with:', annotations);
          annotations.forEach(annotation => {
            console.log('Processing annotation:', annotation);
            console.log('Annotation type:', typeof annotation);
            console.log('Annotation constructor:', annotation.constructor.name);
            console.log('Annotation properties:', Object.keys(annotation));

            // Try multiple ways to detect polygon annotation
            const isPolygon =
              annotation.constructor.name === 'PolygonAnnotation' ||
              annotation.className === 'PSPDFKit.Annotations.PolygonAnnotation' ||
              annotation.toString().includes('PolygonAnnotation') ||
              (annotation.points && annotation.points.size > 2); // Polygon should have multiple points

            console.log('Is polygon?', isPolygon);

            if (isPolygon && annotation.points) {
              console.log('Polygon annotation detected!');
              console.log('Points object:', annotation.points);
              console.log('Points size:', annotation.points.size);
              console.log('Annotation bounding box:', annotation.boundingBox);

              try {
                // Convert Nutrient points to our format
                const polygonPoints = annotation.points.toArray().map(point => {
                  console.log('Processing point:', point, 'x:', point.x, 'y:', point.y);
                  return {
                    x: point.x,
                    y: point.y
                  };
                });

                console.log('Converted polygon points (should be in PDF coordinate space):', polygonPoints);

                // Log coordinate ranges for debugging
                const xCoords = polygonPoints.map(p => p.x);
                const yCoords = polygonPoints.map(p => p.y);
                console.log('X coordinate range:', Math.min(...xCoords), 'to', Math.max(...xCoords));
                console.log('Y coordinate range:', Math.min(...yCoords), 'to', Math.max(...yCoords));

                if (polygonPoints.length >= 3) {
                  // Check which labels are inside this polygon
                  checkLabelsInPolygon(polygonPoints);
                } else {
                  console.log('Not enough points for a valid polygon');
                }
              } catch (error) {
                console.error('Error processing polygon points:', error);
              }
            } else {
              console.log('Not a polygon annotation or no points available');
            }
          });
        });

        // Also listen for annotation updates in case the polygon is modified
        const unsubscribeAnnotationUpdate = pdfInstance.addEventListener('annotations.update', (annotations) => {
          console.log('annotations.update event fired with:', annotations);
          // Process updates the same way as creation
          annotations.forEach(annotation => {
            const isPolygon =
              annotation.constructor.name === 'PolygonAnnotation' ||
              annotation.className === 'PSPDFKit.Annotations.PolygonAnnotation' ||
              annotation.toString().includes('PolygonAnnotation') ||
              (annotation.points && annotation.points.size > 2);

            if (isPolygon && annotation.points) {
              try {
                const polygonPoints = annotation.points.toArray().map(point => ({
                  x: point.x,
                  y: point.y
                }));

                if (polygonPoints.length >= 3) {
                  checkLabelsInPolygon(polygonPoints);
                }
              } catch (error) {
                console.error('Error processing updated polygon points:', error);
              }
            }
          });
        });

        cleanup = () => {
          unsubscribeAnnotationCreate();
          unsubscribeAnnotationUpdate();
          NutrientViewer.unload(container);
        };
      } catch (error) {
        console.error('Error loading PDF:', error);
        console.error('Error stack:', error.stack);
        console.error('Error name:', error.name);
        setError(`Failed to load PDF: ${error.message}`);
        setIsLoading(false);
      }
    })();

    return cleanup;
  }, [pdfUrl]);

  // Monitor extractedData changes
  useEffect(() => {
    console.log('=== EXTRACTED DATA CHANGED ===');
    console.log('New extractedData:', extractedData);
    if (extractedData) {
      console.log('Data keys:', Object.keys(extractedData));
      if (extractedData.filtered_pairs) {
        console.log('Number of filtered pairs:', extractedData.filtered_pairs.length);
        console.log('First few pairs:', extractedData.filtered_pairs.slice(0, 2));
      }
    }
  }, [extractedData]);



  // Clear all annotations
  const clearAnnotations = async () => {
    if (!instance) return;

    try {
      const annotations = await instance.getAnnotations(0); // Get annotations from first page
      if (annotations.size > 0) {
        await instance.delete(annotations);
        setNotification({
          type: 'info',
          message: 'All annotations cleared',
          labels: []
        });
        setTimeout(() => setNotification(null), 2000);
      }
    } catch (error) {
      console.error('Error clearing annotations:', error);
    }
  };

  // Test function to manually trigger polygon detection
  const testPolygonDetection = () => {
    console.log('=== TESTING POLYGON DETECTION ===');
    console.log('Extracted data:', extractedData);
    console.log('Extracted data type:', typeof extractedData);
    console.log('Is extractedData null?', extractedData === null);
    console.log('Is extractedData undefined?', extractedData === undefined);

    if (extractedData) {
      console.log('Extracted data keys:', Object.keys(extractedData));
      console.log('Has filtered_pairs?', 'filtered_pairs' in extractedData);
      console.log('filtered_pairs:', extractedData.filtered_pairs);
    }

    if (extractedData && extractedData.filtered_pairs && extractedData.filtered_pairs.length > 0) {
      // Log some sample label coordinates for reference
      console.log('Sample label coordinates:');
      extractedData.filtered_pairs.slice(0, 3).forEach((pair, index) => {
        console.log(`Label ${index}: ${pair.label} at (${pair.bbox.x0}, ${pair.bbox.y0}) to (${pair.bbox.x1}, ${pair.bbox.y1})`);
      });

      // Create a test polygon that should encompass some labels based on your sample data
      // Your sample shows labels around coordinates like (885, 2129), (1071, 1985), etc.
      const testPolygon = [
        { x: 800, y: 1900 },   // Bottom-left
        { x: 1200, y: 1900 },  // Bottom-right
        { x: 1200, y: 2200 },  // Top-right
        { x: 800, y: 2200 }    // Top-left
      ];

      console.log('Testing with polygon (PDF coordinates):', testPolygon);
      checkLabelsInPolygon(testPolygon);
    } else {
      // Show different messages based on the state
      let message = 'No extracted data available for testing';
      if (extractedData === null) {
        message = 'Extracted data is null - backend processing may still be in progress';
      } else if (extractedData && !extractedData.filtered_pairs) {
        message = 'Extracted data exists but has no filtered_pairs';
      } else if (extractedData && extractedData.filtered_pairs && extractedData.filtered_pairs.length === 0) {
        message = 'Extracted data exists but filtered_pairs is empty';
      }

      setNotification({
        type: 'info',
        message: message,
        labels: []
      });
      setTimeout(() => setNotification(null), 4000);
    }
  };

  if (!pdfUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-gray-600">No PDF selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      {/* Instructions Panel */}
      {instance && !isLoading && (
        <div className="absolute top-20 left-4 z-50">
          <div className="bg-blue-600 text-white px-4 py-3 rounded-lg text-sm shadow-lg max-w-sm">
            <div className="font-semibold mb-2 flex items-center gap-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Polygon Drawing Instructions
            </div>
            <div className="space-y-1 text-xs">
              <div>1. Click the "Annotate" button in the toolbar above</div>
              <div>2. Select "Polygon" from the annotation tools</div>
              <div>3. Draw around room labels to identify them</div>
              <div className="mt-2 pt-2 border-t border-blue-400 opacity-90">
                {extractedData === null && (
                  <div className="text-yellow-200">⏳ Processing PDF for room labels...</div>
                )}
                {extractedData && extractedData.filtered_pairs && extractedData.filtered_pairs.length > 0 && (
                  <div className="text-green-200">✅ {extractedData.filtered_pairs.length} room labels available</div>
                )}
                {extractedData && (!extractedData.filtered_pairs || extractedData.filtered_pairs.length === 0) && (
                  <div className="text-red-200">❌ No room labels found in PDF</div>
                )}
              </div>
            </div>
            <div className="flex gap-2 mt-3">
              <button
                onClick={clearAnnotations}
                className="px-3 py-1 bg-blue-700 hover:bg-blue-800 text-white rounded text-xs transition-colors duration-200 flex items-center gap-1"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Clear All
              </button>
              <button
                onClick={testPolygonDetection}
                className="px-3 py-1 bg-green-700 hover:bg-green-800 text-white rounded text-xs transition-colors duration-200 flex items-center gap-1"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Test Detection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notification */}
      {notification && (
        <div className={`absolute top-24 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg ${notification.type === 'success' ? 'bg-green-600 text-white' : 'bg-blue-600 text-white'
          }`}>
          <div className="flex items-start gap-3">
            <svg className="w-6 h-6 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {notification.type === 'success' ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              )}
            </svg>
            <div className="flex-1">
              <div className="font-semibold">{notification.message}</div>
              {notification.labels.length > 0 && (
                <div className="mt-2 text-sm opacity-90">
                  {notification.labels.map((label, index) => (
                    <div key={index} className="mb-1">
                      <span className="font-medium">{label.label}</span>
                      <span className="ml-2 font-mono text-xs">({label.code})</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <button
              onClick={() => setNotification(null)}
              className="text-white hover:text-gray-200 ml-2"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="text-center">
            <svg className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <p className="text-gray-600">Loading PDF...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
          <div className="text-center">
            <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-red-600 mb-2">Error loading PDF</p>
            <p className="text-sm text-red-500">{error}</p>
          </div>
        </div>
      )}

      <div
        ref={containerRef}
        className="w-full h-full"
        style={{
          width: '100vw',
          height: '100vh'
        }}
      />
    </div>
  );
};

export default TestViewer;
